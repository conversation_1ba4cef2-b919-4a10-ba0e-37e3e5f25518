# Enhanced Dance Battle Heroes - AI Agent Development Guide

**Version:** 1.0  
**Last Updated:** 2025-05-25  
**Purpose:** Comprehensive guide for AI agents to understand, maintain, enhance, and debug the Enhanced Dance Battle Heroes game system.

---

## 🎯 SYSTEM OVERVIEW

### Core Vision
The Enhanced Dance Battle Heroes is a comprehensive RPG-style dance game that combines:
- **Exploration elements** inspired by Diablo/Heroes (world map with discoverable venues)
- **Combo-based combat** inspired by Mortal Kombat/Tekken (body movement sequences)
- **Rich progression systems** of modern RPGs (skills, equipment, quests, achievements)
- **Authentic dance culture** centered around salsa, bachata, kizomba, zouk, and cha-cha

### Current Implementation Status: ✅ FULLY OPERATIONAL

**Database:** ✅ Complete with all models seeded  
**Backend API:** ✅ All endpoints functional  
**Frontend UI:** ✅ All game tabs implemented  
**Game Systems:** ✅ All core mechanics working  
**Integration:** ✅ Frontend-backend fully connected  

---

## 🗄️ DATABASE ARCHITECTURE

### Core Models (Prisma Schema)

#### Game Entities
```prisma
// Hero System
model Hero {
  id: String @id @default(cuid())
  userId: String @unique
  name: String
  level: Int @default(1)
  experience: Int @default(0)
  energy: Int @default(10)
  health: Int @default(100)
  mana: Int @default(50)
  attack: Int @default(10)
  defense: Int @default(10)
  speed: Int @default(10)
  luck: Int @default(10)
  primaryStyle: DanceStyle @default(SALSA)
  secondaryStyle: DanceStyle?
  stylePoints: Json @default("{}")
  skills: Json @default("{}")
  traits: String[] @default([])
  wins: Int @default(0)
  losses: Int @default(0)
  draws: Int @default(0)
  winStreak: Int @default(0)
  globalRank: Int?
  styleRanks: Json @default("{}")
  trophies: Int @default(0)
  titles: String[] @default([])
  skillPoints: Int @default(0)
  traitPoints: Int @default(0)
  coins: Int @default(1000)
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

// Battle System
model Battle {
  id: String @id @default(cuid())
  hero1Id: String
  hero2Id: String
  venueId: String?
  danceStyle: DanceStyle
  status: BattleStatus @default(ACTIVE)
  currentTurn: Int @default(1)
  maxTurns: Int @default(10)
  hero1Health: Int
  hero2Health: Int
  hero1Mana: Int
  hero2Mana: Int
  winner: String?
  moves: Json @default("[]")
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

// Venue System
model DanceVenue {
  id: String @id @default(cuid())
  name: String
  type: VenueType
  description: String
  location: String
  coordinates: Json // {x: number, y: number}
  isHidden: Boolean @default(false)
  unlockLevel: Int @default(1)
  specialties: DanceStyle[]
  difficulty: Int // 1-10 scale
  rewards: Json @default("{}")
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

// Combo System
model BattleCombo {
  id: String @id @default(cuid())
  name: String
  description: String
  danceStyle: DanceStyle
  bodySequence: String[] // ["LL", "RL", "LH", "RH", "H", "T", "HP"]
  beatCount: Int
  timingWindow: Int // milliseconds
  difficulty: Int // 1-10
  power: Int
  energyCost: Int
  manaCost: Int @default(0)
  unlockLevel: Int @default(1)
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}
```

#### Progression Systems
```prisma
// Equipment System
model Equipment {
  id: String @id @default(cuid())
  name: String
  description: String
  type: EquipmentType
  rarity: Rarity
  price: Int
  statBonuses: Json @default("{}")
  skillBonuses: Json @default("{}")
  specialEffects: String[] @default([])
  requiredLevel: Int @default(1)
  danceStyle: DanceStyle?
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

// Quest System
model Quest {
  id: String @id @default(cuid())
  title: String
  description: String
  type: QuestType
  category: String
  targetValue: Int
  rewards: Json @default("{}")
  expiresAt: DateTime?
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

// User Progress Tracking
model UserQuest {
  id: String @id @default(cuid())
  userId: String
  questId: String
  progress: Int @default(0)
  completed: Boolean @default(false)
  claimedAt: DateTime?
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}
```

### Enums
```prisma
enum DanceStyle {
  SALSA
  BACHATA
  KIZOMBA
  ZOUK
  CHACHA
  UNIVERSAL
}

enum VenueType {
  DANCE_ACADEMY
  UNDERGROUND_CLUB
  COMPETITION_HALL
  FESTIVAL_GROUND
  MYSTICAL_STUDIO
  PRACTICE_ROOM
  ROOFTOP_TERRACE
  BEACH_CLUB
  HISTORIC_BALLROOM
}

enum BattleStatus {
  ACTIVE
  COMPLETED
  ABANDONED
}

enum EquipmentType {
  SHOES
  OUTFIT
  ACCESSORY
  INSTRUMENT
  CHARM
}

enum Rarity {
  COMMON
  RARE
  EPIC
  LEGENDARY
  MYTHIC
}

enum QuestType {
  DAILY
  WEEKLY
  MONTHLY
  STORY
  SPECIAL
}
```

---

## 🔧 BACKEND ARCHITECTURE

### API Endpoints Structure

#### Game Routes (`/api/game/`)
```javascript
// Hero Management
GET    /api/game/hero              // Get user's hero
PUT    /api/game/hero/name         // Rename hero
POST   /api/game/hero/create       // Create new hero

// Battle System
GET    /api/game/battle/moves      // Get available battle moves
GET    /api/game/battle/opponents  // Get AI opponents
POST   /api/game/battle/start      // Start regular battle
POST   /api/game/battle/venue/start // Start venue battle
POST   /api/game/battle/:id/turn   // Execute battle move
POST   /api/game/battle/:id/combo  // Execute combo move

// Venue System
GET    /api/game/venues            // Get all venues
GET    /api/game/venues/discovered // Get user's discovered venues
POST   /api/game/venues/:id/discover // Discover hidden venue

// Combo System
GET    /api/game/combos            // Get available combos

// Equipment System
GET    /api/game/equipment/shop    // Get equipment shop
GET    /api/game/equipment/inventory // Get user inventory
POST   /api/game/equipment/purchase // Purchase equipment

// Quest System
GET    /api/game/quests            // Get user quests
POST   /api/game/quests/:id/claim  // Claim quest reward

// Leaderboard
GET    /api/game/leaderboard       // Get rankings
```

### Service Layer Architecture

#### BattleService (`backend/services/battleService.js`)
```javascript
class BattleService {
  // Core battle mechanics
  async createBattle(hero1Id, hero2Id, danceStyle, venueId = null)
  async executeTurn(battleId, moveId, timingScore)
  async executeCombo(battleId, comboId, bodySequence, timingAccuracy)
  
  // Venue battles
  async startVenueBattle(heroId, venueId, danceStyle)
  async calculateVenueBattleRewards(battle, venue, performance)
  
  // Combat calculations
  calculateDamage(attacker, defender, move, timingScore)
  calculateComboEffectiveness(combo, bodySequence, timingAccuracy)
  checkBattleEnd(battle)
  
  // AI opponent behavior
  selectAIMove(aiHero, playerHero, battle)
  calculateAIDifficulty(venue, heroLevel)
}
```

#### QuestService (`backend/services/questService.js`)
```javascript
class QuestService {
  // Quest progression
  async updateQuestProgress(userId, questType, action, value = 1)
  async checkQuestCompletion(userId, questId)
  async claimQuestReward(userId, questId)
  
  // Quest generation
  async generateDailyQuests(userId)
  async generateWeeklyQuests(userId)
  async generateMonthlyQuests(userId)
  
  // Progress tracking
  trackBattleVictory(userId, danceStyle, venue)
  trackComboUsage(userId, comboId, accuracy)
  trackVenueDiscovery(userId, venueId)
}
```

### Database Seeding Status

#### Seeded Data (All Complete ✅)
- **Heroes:** 5 heroes (1 player + 4 AI opponents)
- **Battle Moves:** 17 enhanced moves across all dance styles
- **Combos:** 20 body movement combinations with timing mechanics
- **Venues:** 16 dance venues with coordinates and specialties
- **Equipment:** 100 items across 5 categories (20 each)
- **Quests:** 100 quests across all types (Daily: 30, Weekly: 25, Monthly: 20, Story: 15, Special: 10)
- **Users:** 10 test users with varying levels and progress

---

## 🎮 FRONTEND ARCHITECTURE

### Component Structure

#### Main Game Page (`frontend/src/pages/dancer/GamePage.tsx`)
```typescript
interface GamePageState {
  hero: Hero | null
  battleMoves: BattleMove[]
  combos: Combo[]
  opponents: any[]
  equipment: Equipment[]
  inventory: Equipment[]
  quests: Quest[]
  leaderboard: any[]
  currentBattle: Battle | null
  activeBattle: any | null
  battleResult: any | null
  selectedCombo: any | null
  comboSequence: string[]
  timingAccuracy: number
  discoveredVenues: string[]
  // ... other state
}

// Tab System
const tabs = [
  'hero',           // Hero stats and progression
  'battle',         // Battle interface and history
  'world-map',      // Venue exploration and discovery
  'combo-trainer',  // Combo practice and mastery
  'equipment',      // Equipment shop
  'quests',         // Quest tracking and rewards
  'leaderboard',    // Rankings and achievements
  'inventory'       // Owned equipment
]
```

#### Key Components

**WorldMap Component** (`frontend/src/components/dancer/battle/WorldMap.tsx`)
```typescript
interface WorldMapProps {
  heroLevel: number
  onVenueSelect: (venue: Venue) => void
  discoveredVenues: string[]
  onVenueBattle?: (venueId: string, danceStyle: string) => void
}

// Features:
// - Interactive venue discovery
// - Level-gated access
// - Hidden venue mechanics
// - Battle initiation
// - Visual difficulty indicators
```

**ComboTrainer Component** (`frontend/src/components/dancer/battle/ComboTrainer.tsx`)
```typescript
interface ComboTrainerProps {
  heroLevel: number
  onComboMastered: (comboId: string) => void
}

// Features:
// - Combo practice interface
// - Body movement input system
// - Timing accuracy training
// - Mastery progression tracking
```

### State Management Patterns

#### Data Fetching
```typescript
// Centralized data fetching in GamePage
const fetchHero = useCallback(async () => { /* ... */ }, [])
const fetchCombos = useCallback(async () => { /* ... */ }, [])
const fetchVenues = useCallback(async () => { /* ... */ }, [])

// Initialization pattern
useEffect(() => {
  const initializeGame = async () => {
    await Promise.all([
      fetchHero(),
      fetchBattleMoves(),
      fetchCombos(),
      fetchOpponents(),
      fetchEquipment(),
      fetchInventory(),
      fetchQuests(),
      fetchLeaderboard()
    ])
  }
  if (user) initializeGame()
}, [user, /* dependencies */])
```

#### Battle State Management
```typescript
// Battle flow
const handleVenueBattle = async (venueId: string, danceStyle: string) => {
  const response = await fetch('/api/game/battle/venue/start', {
    method: 'POST',
    body: JSON.stringify({ venueId, danceStyle })
  })
  const data = await response.json()
  if (data.success) {
    setActiveBattle(data.battle)
    setActiveTab('battle')
  }
}

const executeCombo = async (comboId: string, bodySequence: string[], timingAccuracy: number) => {
  const response = await fetch(`/api/game/battle/${activeBattle.id}/combo`, {
    method: 'POST',
    body: JSON.stringify({ comboId, bodySequence, timingAccuracy })
  })
  const data = await response.json()
  if (data.success) {
    setActiveBattle(data.battle)
    setBattleResult(data.comboResult)
    if (data.comboResult.battleEnded) {
      // Handle battle completion
    }
  }
}
```

---

## 🎯 GAME MECHANICS DEEP DIVE

### Combat System

#### Body Movement Combos
```javascript
// Body part controls
const bodyParts = {
  LL: 'Left Leg',
  RL: 'Right Leg', 
  LH: 'Left Hand',
  RH: 'Right Hand',
  H: 'Head',
  T: 'Torso/Body',
  HP: 'Hips'
}

// Example combo sequences
const combos = {
  salsa_basic: ['LL', 'RL'],
  salsa_cross_body: ['LL', 'RL', 'LH', 'RH'],
  bachata_sensual: ['LL', 'RL', 'HP', 'T', 'LH', 'RH'],
  universal_fusion: ['LL', 'RL', 'LH', 'RH', 'H', 'T', 'HP', 'LL', 'RL']
}

// Timing mechanics
interface ComboExecution {
  sequence: string[]        // Required body movements
  timing: number           // Beat count (4-20 beats)
  accuracy: number         // Timing window (100-300ms)
  userInput: string[]      // Player's input sequence
  timingAccuracy: number   // Player's timing score (0-100%)
}
```

#### Damage Calculation
```javascript
function calculateComboDamage(combo, execution) {
  const baseDamage = combo.power
  const sequenceAccuracy = calculateSequenceMatch(combo.bodySequence, execution.bodySequence)
  const timingMultiplier = execution.timingAccuracy / 100
  const difficultyBonus = combo.difficulty * 0.1
  
  return Math.floor(baseDamage * sequenceAccuracy * timingMultiplier * (1 + difficultyBonus))
}

function calculateSequenceMatch(required, input) {
  if (required.length !== input.length) return 0.5
  const matches = required.filter((part, index) => part === input[index]).length
  return matches / required.length
}
```

### Venue System

#### Venue Types and Characteristics
```javascript
const venueTypes = {
  DANCE_ACADEMY: {
    icon: '🏫',
    baseReward: 100,
    specialtyBonus: 1.2,
    description: 'Professional training environment'
  },
  UNDERGROUND_CLUB: {
    icon: '🕳️',
    baseReward: 150,
    specialtyBonus: 1.5,
    description: 'Hidden venues with unique challenges',
    isHidden: true
  },
  MYSTICAL_STUDIO: {
    icon: '✨',
    baseReward: 200,
    specialtyBonus: 2.0,
    description: 'Magical venues with special powers',
    isHidden: true
  }
  // ... other venue types
}
```

#### Discovery Mechanics
```javascript
async function discoverVenue(userId, venueId) {
  const venue = await getVenue(venueId)
  const hero = await getHero(userId)
  
  // Check unlock requirements
  if (hero.level < venue.unlockLevel) {
    throw new Error(`Requires level ${venue.unlockLevel}`)
  }
  
  // Create discovery record
  await createVenueDiscovery(userId, venueId)
  
  // Award discovery XP
  const xpReward = venue.difficulty * 50
  await updateHeroXP(userId, xpReward)
  
  // Check for level up
  const levelUp = await checkLevelUp(userId)
  
  return { venue, xpReward, levelUp }
}
```

### Quest System

#### Quest Categories and Mechanics
```javascript
const questTypes = {
  DAILY: {
    resetInterval: '24 hours',
    maxActive: 5,
    baseReward: { xp: 100, coins: 50 }
  },
  WEEKLY: {
    resetInterval: '7 days',
    maxActive: 3,
    baseReward: { xp: 500, coins: 200 }
  },
  MONTHLY: {
    resetInterval: '30 days',
    maxActive: 2,
    baseReward: { xp: 2000, coins: 1000 }
  },
  STORY: {
    resetInterval: 'never',
    progressive: true,
    baseReward: { xp: 1000, coins: 500, equipment: true }
  }
}
```

#### Progress Tracking
```javascript
async function updateQuestProgress(userId, action, metadata = {}) {
  const activeQuests = await getUserActiveQuests(userId)
  
  for (const quest of activeQuests) {
    if (questMatchesAction(quest, action, metadata)) {
      await incrementQuestProgress(quest.id, 1)
      
      if (quest.progress >= quest.targetValue) {
        await markQuestComplete(quest.id)
        await notifyQuestCompletion(userId, quest)
      }
    }
  }
}

// Example quest triggers
const questTriggers = {
  BATTLE_VICTORY: (quest, metadata) => {
    return quest.category === 'battle' && 
           quest.requirements.danceStyle === metadata.danceStyle
  },
  COMBO_PERFECT: (quest, metadata) => {
    return quest.category === 'combo' && 
           metadata.accuracy >= 95
  },
  VENUE_DISCOVERY: (quest, metadata) => {
    return quest.category === 'exploration' &&
           quest.requirements.venueType === metadata.venueType
  }
}
```

---

## 🔄 INTEGRATION PATTERNS

### Frontend-Backend Communication

#### API Response Patterns
```typescript
// Standard API response format
interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Battle response with additional data
interface BattleResponse extends ApiResponse<Battle> {
  moveResult?: {
    damage: number
    effectiveness: string
    timingAccuracy: number
    sequenceAccuracy: number
    battleEnded: boolean
    winner?: string
    rewards?: {
      xp: number
      coins: number
      equipment?: Equipment[]
    }
  }
  levelUp?: {
    newLevel: number
    skillPoints: number
    traitPoints: number
  }
}
```

#### Error Handling Patterns
```typescript
// Frontend error handling
const handleApiCall = async (apiCall: () => Promise<any>) => {
  try {
    setLoading(true)
    const response = await apiCall()
    const data = await response.json()
    
    if (data.success) {
      return data
    } else {
      setError(data.error || 'Operation failed')
      return null
    }
  } catch (err) {
    setError('Network error occurred')
    return null
  } finally {
    setLoading(false)
  }
}
```

### Real-time Updates

#### WebSocket Integration
```javascript
// Battle updates via WebSocket
socket.on('battleUpdate', (battleData) => {
  setActiveBattle(battleData.battle)
  if (battleData.moveResult) {
    setBattleResult(battleData.moveResult)
  }
})

// Quest completion notifications
socket.on('questCompleted', (questData) => {
  showNotification(`Quest completed: ${questData.title}`)
  fetchQuests() // Refresh quest list
})
```

---

## 🚨 CRITICAL CONSTRAINTS & GUIDELINES

### Mandatory Requirements

#### 1. No Lazy Loading of UI Elements
```typescript
// ❌ PROHIBITED - Dynamic imports for UI components
const LazyComponent = React.lazy(() => import('./Component'))

// ✅ REQUIRED - Eager loading of all UI elements
import Component from './Component'
```

#### 2. Real Data Only - No Mocking
```typescript
// ❌ PROHIBITED - Mock data or simulated responses
const mockBattleResult = { damage: 100, winner: 'player' }

// ✅ REQUIRED - Always use real API responses
const battleResult = await fetch('/api/game/battle/execute').then(r => r.json())
```

#### 3. Comprehensive Logging
```javascript
// ✅ REQUIRED - Detailed logging for all operations
logger.info('Battle started', { 
  battleId, 
  heroId, 
  venueId, 
  danceStyle,
  timestamp: new Date().toISOString()
})

logger.debug('Combo execution', {
  comboId,
  bodySequence,
  timingAccuracy,
  calculatedDamage
})
```

#### 4. File Freshness Verification
```typescript
// ✅ REQUIRED - Always re-read files before editing
const currentContent = await readFile(filePath)
// Verify content matches expectations before making changes
```

---

## 🐛 DEBUGGING GUIDELINES

### Log Analysis Protocol

#### 1. Check Recent Activity
```bash
# Always start with recent logs
tail -n 500 logs/combined.log

# Look for specific patterns
grep "ERROR\|WARN" logs/combined.log | tail -20
grep "Battle\|Combo\|Venue" logs/combined.log | tail -50
```

#### 2. Common Error Patterns
```javascript
// Database connection issues
"Prisma Client initialization failed"
"Database connection timeout"

// Authentication problems
"JWT verification failed"
"User not authenticated"

// Game logic errors
"Battle not found"
"Combo execution failed"
"Venue not accessible"

// Frontend-backend sync issues
"Failed to fetch"
"Network request failed"
```

#### 3. Performance Monitoring
```javascript
// API response times
grep "HTTP Response" logs/combined.log | grep "duration" | tail -20

// Database query performance
grep "Prisma query" logs/combined.log | tail -10

// Memory usage patterns
grep "Memory\|GC" logs/combined.log | tail -10
```

### Common Issues and Solutions

#### Battle System Issues
```javascript
// Issue: Combo execution fails
// Check: Body sequence validation
// Solution: Verify combo.bodySequence format matches input

// Issue: Battle doesn't end properly
// Check: Health calculation logic
// Solution: Ensure battle.status updates correctly

// Issue: Venue battles not starting
// Check: Venue accessibility logic
// Solution: Verify hero level vs venue.unlockLevel
```

#### Frontend State Issues
```typescript
// Issue: Component not updating after API call
// Check: State setter calls and useEffect dependencies
// Solution: Ensure proper state updates and re-renders

// Issue: Tab switching problems
// Check: activeTab state management
// Solution: Verify tab state consistency across components
```

---

## 🔧 MAINTENANCE PROCEDURES

### Database Maintenance

#### Schema Updates
```bash
# After schema changes
npx prisma db push
npx prisma generate

# Seed data refresh
node prisma/seed.js
```

#### Data Integrity Checks
```sql
-- Check for orphaned records
SELECT * FROM Battle WHERE hero1Id NOT IN (SELECT id FROM Hero);
SELECT * FROM UserQuest WHERE userId NOT IN (SELECT id FROM User);

-- Verify quest progress consistency
SELECT * FROM UserQuest WHERE progress > (SELECT targetValue FROM Quest WHERE Quest.id = UserQuest.questId);
```

### Performance Optimization

#### Database Queries
```javascript
// Use proper indexing for frequent queries
// Index on: userId, heroId, battleId, venueId
// Composite indexes for complex queries

// Optimize N+1 queries with includes
const heroWithBattles = await prisma.hero.findUnique({
  where: { userId },
  include: {
    battles: {
      orderBy: { createdAt: 'desc' },
      take: 10
    }
  }
})
```

#### Frontend Performance
```typescript
// Memoize expensive calculations
const memoizedDamageCalculation = useMemo(() => {
  return calculateComboDamage(combo, execution)
}, [combo, execution])

// Debounce user inputs
const debouncedTimingUpdate = useCallback(
  debounce((value: number) => setTimingAccuracy(value), 100),
  []
)
```

---

## 📋 FEATURE REQUEST SECTION

### Current Feature Requests

#### High Priority
- [ ] **Audio Integration System**
  - Beat detection for songs in database
  - Combo timing synchronization with music
  - Sound effects for body movements
  - Musical accent rewards

- [ ] **Tournament System**
  - Bracket-based competitions
  - Seasonal tournaments
  - Prize pools and rankings
  - Spectator mode

- [ ] **Advanced AI Personalities**
  - Unique fighting styles per AI opponent
  - Adaptive difficulty based on player performance
  - Personality-driven move selection
  - Dynamic dialogue system

#### Medium Priority
- [ ] **Social Features Enhancement**
  - Player-vs-player challenges
  - Guild/team system
  - Social leaderboards
  - Achievement sharing

- [ ] **Mobile Optimization**
  - Touch-optimized combo input
  - Gesture recognition for body movements
  - Mobile-specific UI improvements
  - Offline mode capabilities

- [ ] **Content Expansion**
  - Additional dance styles (Tango, Swing, Hip-Hop)
  - More venue types and locations
  - Seasonal events and limited-time content
  - Story mode expansion

#### Low Priority
- [ ] **Customization System**
  - Hero appearance customization
  - Custom combo creation
  - Venue decoration/modification
  - Personal dance style development

- [ ] **Analytics Dashboard**
  - Performance tracking and statistics
  - Progress visualization
  - Skill development recommendations
  - Training suggestions

### Feature Request Template

When adding new feature requests, use this template:

```markdown
#### Feature Name: [Name]
**Priority:** [High/Medium/Low]
**Category:** [Combat/Social/UI/Performance/Content]
**Description:** [Detailed description of the feature]
**Technical Requirements:**
- Backend changes needed
- Frontend components affected
- Database schema modifications
- API endpoints required
**Dependencies:** [Other features or systems this depends on]
**Estimated Complexity:** [Low/Medium/High]
**User Impact:** [How this improves user experience]
**Implementation Notes:** [Technical considerations and approaches]
```

---

## 🔍 TESTING GUIDELINES

### Manual Testing Checklist

#### Core Game Flow
- [ ] Hero creation and stats display
- [ ] Battle initiation and execution
- [ ] Combo input and timing mechanics
- [ ] Venue discovery and battles
- [ ] Quest progression and rewards
- [ ] Equipment purchase and inventory
- [ ] Leaderboard updates

#### Edge Cases
- [ ] Battle with 0 health/mana
- [ ] Invalid combo sequences
- [ ] Venue access with insufficient level
- [ ] Quest completion edge cases
- [ ] Equipment purchase with insufficient coins

#### Performance Testing
- [ ] Large venue map rendering
- [ ] Multiple simultaneous battles
- [ ] Quest list with 100+ items
- [ ] Equipment shop with full inventory

### Automated Testing Considerations

#### API Testing
```javascript
// Example test structure
describe('Battle System', () => {
  test('should start venue battle successfully', async () => {
    const response = await request(app)
      .post('/api/game/battle/venue/start')
      .send({ venueId: 'test-venue', danceStyle: 'SALSA' })
      .expect(200)
    
    expect(response.body.success).toBe(true)
    expect(response.body.battle).toBeDefined()
  })
})
```

---

## 📚 REFERENCE DOCUMENTATION

### Key Files and Their Purposes

#### Backend Core
- `backend/server.js` - Main server configuration and routing
- `backend/services/battleService.js` - Combat mechanics and calculations
- `backend/services/questService.js` - Quest progression and rewards
- `backend/routes/game.js` - Game API endpoints
- `prisma/schema.prisma` - Database schema definition

#### Frontend Core
- `frontend/src/pages/dancer/GamePage.tsx` - Main game interface
- `frontend/src/components/dancer/battle/WorldMap.tsx` - Venue exploration
- `frontend/src/components/dancer/battle/ComboTrainer.tsx` - Combo practice

#### Data Files
- `prisma/seed-data/venues.js` - Venue definitions and coordinates
- `prisma/seed-data/combos.js` - Combo sequences and mechanics
- `prisma/seed-data/quests.js` - Quest definitions and rewards
- `prisma/seed-data/equipment.js` - Equipment items and stats

### External Dependencies

#### Backend
- `@prisma/client` - Database ORM
- `express` - Web framework
- `jsonwebtoken` - Authentication
- `winston` - Logging system
- `node-cron` - Scheduled tasks

#### Frontend
- `react` - UI framework
- `typescript` - Type safety
- `lucide-react` - Icons
- `tailwindcss` - Styling

---

## 🚀 DEPLOYMENT NOTES

### Environment Setup
```bash
# Install dependencies
npm install

# Database setup
npx prisma db push
npx prisma generate
node prisma/seed.js

# Build frontend
cd frontend && npm run build

# Start server
npm start
```

### Production Considerations
- Database connection pooling
- Redis for session management
- CDN for static assets
- Load balancing for multiple instances
- Monitoring and alerting setup

---

**Last Updated:** 2025-05-25  
**Version:** 1.0  
**Maintainer:** AI Agent Development Team

*This guide should be updated whenever significant changes are made to the game system. Always verify current implementation against this documentation before making modifications.* 

## Latest Update: Battle System Parameter Fix (May 25, 2025)

### Critical Issue Resolved: Frontend-Backend Parameter Mismatch

**Problem Identified:**
The battle system was failing because the frontend was sending `{ battleType: "quick" }` while the backend expected `{ opponentId: string, danceStyle: string }` parameters.

**Root Cause Analysis:**
- Frontend `startBattle()` function was calling the API with battle type strings ("quick", "ranked", "tournament")
- Backend `/api/game/battle/start` endpoint expected specific opponent ID and dance style parameters
- This mismatch caused 400 Bad Request errors preventing any battles from starting

**Solution Implemented:**
1. **Frontend Fix**: Modified `startBattle()` function in `frontend/src/pages/dancer/GamePage.tsx`:
   - Added validation to ensure opponents are available before starting battle
   - Implemented random opponent selection from available opponents list
   - Used hero's primary dance style as the battle dance style
   - Changed request payload from `{ battleType }` to `{ opponentId, danceStyle }`

2. **Parameter Mapping**:
   ```typescript
   // Before (causing 400 errors):
   body: JSON.stringify({ battleType })
   
   // After (working solution):
   body: JSON.stringify({ 
     opponentId: randomOpponent.id,
     danceStyle: hero?.primaryStyle || 'SALSA'
   })
   ```

3. **Error Handling**: Added proper validation for empty opponents list with user-friendly error messages

**Technical Implementation Details:**
- **File Modified**: `frontend/src/pages/dancer/GamePage.tsx` (lines 548-584)
- **Dependencies Added**: Function now depends on `[opponents, hero]` state
- **Validation Logic**: Checks for opponents availability before API call
- **Fallback Values**: Uses 'SALSA' as default dance style if hero data unavailable

**Testing Status:**
- ✅ Frontend build completed successfully
- ✅ Server restarted and serving updated frontend
- ✅ Parameter validation implemented
- ⏳ Awaiting user testing of battle functionality

**Log Analysis Insights:**
- Previous logs showed consistent 400 errors with `{"battleType":"quick"}` requests
- Server restart timestamps: 09:19:54 and 09:20:39 (May 25, 2025)
- All game initialization endpoints working correctly (opponents, hero, combos, etc.)

**Next Steps for Verification:**
1. User should attempt to start a battle using any of the three battle types
2. Monitor logs for successful battle creation with proper parameters
3. Verify battle interface loads correctly with opponent and combo selection
``` 
</rewritten_file>