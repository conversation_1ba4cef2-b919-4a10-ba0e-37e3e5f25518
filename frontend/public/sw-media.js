// Service Worker for Media Files
const CACHE_NAME = 'audio-streams-cache-v1';

// Install event - prepare the cache
self.addEventListener('install', (event) => {
  // Skip waiting to update immediately
  self.skipWaiting();
  console.log('Media Service Worker installed');
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cache) => {
          if (cache !== CACHE_NAME) {
            console.log('Media Service Worker clearing old cache:', cache);
            return caches.delete(cache);
          }
        })
      );
    })
  );
  // Claim clients so the service worker is in control immediately
  self.clients.claim();
  console.log('Media Service Worker activated');
});

// Function to determine if a request is for an audio stream
const isAudioRequest = (url) => {
  return url.includes('/api/public/stream/') && url.endsWith('.mp3');
};

// Fetch event - network first with cache fallback for audio, pass through for everything else
self.addEventListener('fetch', (event) => {
  const url = event.request.url;
  
  // Only handle GET requests
  if (event.request.method !== 'GET') return;
  
  // Check if this is an audio stream request
  if (isAudioRequest(url)) {
    // Audio files go through a special strategy
    event.respondWith(networkFirstWithCaching(event.request));
  }
  // All other requests bypass the service worker
});

// Network-first strategy with caching
async function networkFirstWithCaching(request) {
  const cache = await caches.open(CACHE_NAME);
  
  try {
    // Try the network first
    const networkResponse = await fetch(request);
    
    // Handle 204 No Content responses (e.g., when in external mirror mode)
    if (networkResponse.status === 204) {
      console.log('Media Service Worker: No content needed (204) for request:', request.url);
      return networkResponse; // Just return the empty response, don't cache it
    }
    
    // If successful, clone and cache the response
    if (networkResponse.ok) {
      // Only cache responses that have actual content
      if (networkResponse.headers.get('content-length') !== '0') {
        // Cache a clone of the response
        const responseToCache = networkResponse.clone();
        cache.put(request, responseToCache);
        console.log('Media Service Worker: Cached audio stream', request.url);
      } else {
        console.log('Media Service Worker: Not caching empty response for:', request.url);
      }
    }
    
    return networkResponse;
  } catch (error) {
    // Network failed, try the cache
    console.log('Media Service Worker: Network failed, trying cache for:', request.url);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      console.log('Media Service Worker: Serving cached audio stream', request.url);
      return cachedResponse;
    }
    
    // If not in cache either, return a 503 response
    console.log('Media Service Worker: Network and cache both failed for:', request.url);
    return new Response('Network error and no cached version available', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

// Cleanup old cache entries periodically
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'CLEAR_AUDIO_CACHE') {
    caches.open(CACHE_NAME).then((cache) => {
      cache.keys().then((keys) => {
        keys.forEach((request) => {
          cache.delete(request);
        });
      });
    });
    console.log('Media Service Worker: Cache cleared by request');
  }
});

console.log('Media Service Worker initialized'); 