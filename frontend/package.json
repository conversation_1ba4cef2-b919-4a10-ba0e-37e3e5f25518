{"name": "frontend", "version": "0.1.1", "private": true, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@radix-ui/react-slot": "^1.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^18.2.0", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.19", "@types/socket.io-client": "^1.4.36", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.9.2", "history": "^5.3.0", "lucide-react": "^0.503.0", "nodemon": "^3.1.10", "react-beautiful-dnd": "^13.1.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.2.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "build:server": "webpack --config webpack.server.config.js", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/preset-env": "^7.24.0", "@babel/preset-react": "^7.24.0", "@babel/preset-typescript": "^7.24.0", "@craco/craco": "^7.1.0", "@tailwindcss/forms": "^0.5.10", "autoprefixer": "^10.4.17", "babel-loader": "^9.1.3", "clean-webpack-plugin": "^4.0.0", "ignore-loader": "^0.1.2", "npm-force-resolutions": "^0.0.10", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}}