import { useState, useEffect, useCallback } from 'react';
import { getAdminSettings } from '../utils/api';
import { logInfo, logError } from '../utils/logger';

interface AdminSettings {
  id?: string | number;
  createdAt?: string;
  updatedAt?: string;
  defaultCredits?: number;
  activeYoutubePlaylistId?: string | null;
  operationalMode?: string;
  styleSourcePlaylists?: Record<string, string> | null;
  styleSequentialExtractionPrefs?: Record<string, boolean> | null;
  isYouTubeConnected?: boolean;
  externalMirrorPlaylistId?: string | null;
}

export function useAdminSettings() {
  const [settings, setSettings] = useState<AdminSettings | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchSettings = useCallback(async () => {
    setLoading(true);
    setError(null);
    logInfo('useAdminSettings: Fetching settings...');
    try {
      const response = await getAdminSettings();
      if (response.success && response.data) {
        setSettings(response.data);
        logInfo('useAdminSettings: Settings fetched successfully', response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch settings');
      }
    } catch (err: any) {
      logError('useAdminSettings: Error fetching settings', err);
      setError(err);
      setSettings(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  return { settings, loading, error, refetch: fetchSettings };
} 