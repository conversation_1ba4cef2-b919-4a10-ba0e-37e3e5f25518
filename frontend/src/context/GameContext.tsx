import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuthContext } from './AuthContext';
import { useWebSocket } from './WebSocketContext';
import axios from 'axios';

// Define the types for the game context
interface Hero {
  id: string;
  name: string;
  level: number;
  experience: number;
  maxHealth: number;
  energy: number;
  lastEnergyRefill: string;
  traits: string[];
  wins: number;
  losses: number;
  draws: number;
  createdAt: string;
  updatedAt: string;
}

interface Quest {
  quest: {
    id: number;
    title: string;
    description: string;
    type: string;
    targetValue: number;
    rewardXp: number;
    rewardCoins: number;
    isDaily: boolean;
  };
  progress: number;
  completed: boolean;
  claimedAt: string | null;
}

interface BattleMove {
  id: string;
  name: string;
  description: string;
  power: number;
  accuracy: number;
  type: string;
  cooldown: number;
  unlockLevel: number;
}

interface Opponent {
  id: string;
  name: string;
  level: number;
  maxHealth: number;
  wins: number;
  losses: number;
  traits: string[];
}

interface BattleState {
  id: string;
  heroes: {
    id: string;
    name: string;
    level: number;
    maxHealth: number;
    currentHealth: number;
  }[];
  currentTurn: number;
  maxTurns: number;
  status: string;
}

interface BattleEmoteEvent {
  heroId: string;
  heroName: string;
  userId: string;
  username: string;
  emoji: string;
}

interface MoveSelectedEvent {
  heroId: string;
  moveId: string;
  timingScore: number;
}

// Define the context interface
interface GameContextType {
  hero: Hero | null;
  quests: Quest[];
  availableMoves: BattleMove[];
  opponents: Opponent[];
  currentBattle: BattleState | null;
  loading: boolean;
  error: string | null;
  
  // Hero actions
  createHero: (name: string, trait: string) => Promise<void>;
  refreshHero: () => Promise<void>;
  
  // Quest actions
  fetchQuests: () => Promise<void>;
  claimQuestReward: (questId: number) => Promise<void>;
  
  // Battle actions
  fetchMoves: () => Promise<void>;
  fetchOpponents: () => Promise<void>;
  startBattle: (opponentId: string) => Promise<void>;
  selectMove: (moveId: string, timingScore?: number) => Promise<void>;
  sendEmote: (emoji: string) => Promise<void>;
  endBattle: () => void;
  
  // Tap-on-beat
  startTiming: () => void;
  stopTiming: (score: number) => void;
  isTimingActive: boolean;
}

// Create the context with a default value
const GameContext = createContext<GameContextType | null>(null);

// Provider component
export const GameProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, isAuthenticated } = useAuthContext();
  const { socket } = useWebSocket();
  
  // State for the game
  const [hero, setHero] = useState<Hero | null>(null);
  const [quests, setQuests] = useState<Quest[]>([]);
  const [availableMoves, setAvailableMoves] = useState<BattleMove[]>([]);
  const [opponents, setOpponents] = useState<Opponent[]>([]);
  const [currentBattle, setCurrentBattle] = useState<BattleState | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isTimingActive, setIsTimingActive] = useState(false);
  
  // Fetch hero data on mount or auth change
  useEffect(() => {
    if (isAuthenticated) {
      refreshHero();
    }
  }, [isAuthenticated]);
  
  // WebSocket event listeners
  useEffect(() => {
    if (!socket) return;
    
    // Battle events
    socket.on('battle-emote', (data: BattleEmoteEvent) => {
      console.log('Received emote:', data);
      // Handle emote (show emote animation, etc.)
    });
    
    socket.on('move-selected', (data: MoveSelectedEvent) => {
      console.log('Move selected:', data);
      // Handle move selection (update battle state, etc.)
    });
    
    return () => {
      socket.off('battle-emote');
      socket.off('move-selected');
    };
  }, [socket]);
  
  // Refresh hero data
  const refreshHero = useCallback(async () => {
    if (!isAuthenticated) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.get('/api/game/hero');
      
      if (response.data.success) {
        const heroData = response.data.hero;
        // Calculate maxHealth based on level
        heroData.maxHealth = 100 + ((heroData.level - 1) * 10);
        setHero(heroData);
      } else {
        setError(response.data.error || 'Failed to fetch hero data');
      }
    } catch (err: any) {
      console.error('Error fetching hero:', err);
      setError(err.response?.data?.error || err.message || 'Failed to fetch hero data');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);
  
  // Create a new hero
  const createHero = useCallback(async (name: string, trait: string) => {
    if (!isAuthenticated) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.post('/api/game/hero', { name, trait });
      
      if (response.data.success) {
        const heroData = response.data.hero;
        // Calculate maxHealth based on level
        heroData.maxHealth = 100 + ((heroData.level - 1) * 10);
        setHero(heroData);
      } else {
        setError(response.data.error || 'Failed to create hero');
      }
    } catch (err: any) {
      console.error('Error creating hero:', err);
      setError(err.response?.data?.error || err.message || 'Failed to create hero');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);
  
  // Fetch quests
  const fetchQuests = useCallback(async () => {
    if (!isAuthenticated || !hero) return;
    
    setLoading(true);
    
    try {
      const response = await axios.get('/api/game/quests');
      
      if (response.data.success) {
        setQuests(response.data.quests);
      }
    } catch (err: any) {
      console.error('Error fetching quests:', err);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, hero]);
  
  // Claim quest reward
  const claimQuestReward = useCallback(async (questId: number) => {
    if (!isAuthenticated || !hero) return;
    
    setLoading(true);
    
    try {
      const response = await axios.post('/api/game/quest/claim', { questId });
      
      if (response.data.success) {
        // Refresh quests and hero data
        await Promise.all([
          fetchQuests(),
          refreshHero()
        ]);
        
        // Return success with level up info
        return response.data;
      }
    } catch (err: any) {
      console.error('Error claiming quest reward:', err);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, hero, fetchQuests, refreshHero]);
  
  // Fetch available moves
  const fetchMoves = useCallback(async () => {
    if (!isAuthenticated || !hero) return;
    
    setLoading(true);
    
    try {
      const response = await axios.get('/api/game/moves');
      
      if (response.data.success) {
        setAvailableMoves(response.data.moves);
      }
    } catch (err: any) {
      console.error('Error fetching moves:', err);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, hero]);
  
  // Fetch opponents
  const fetchOpponents = useCallback(async () => {
    if (!isAuthenticated || !hero) return;
    
    setLoading(true);
    
    try {
      const response = await axios.get('/api/game/opponents');
      
      if (response.data.success) {
        setOpponents(response.data.opponents);
      }
    } catch (err: any) {
      console.error('Error fetching opponents:', err);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, hero]);
  
  // Start a battle
  const startBattle = useCallback(async (opponentId: string) => {
    if (!isAuthenticated || !hero) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.post('/api/game/battle/start', { opponentId });
      
      if (response.data.success) {
        setCurrentBattle(response.data);
        
        // Join the battle room
        if (socket) {
          socket.emit('battle:subscribe', response.data.battleId);
        }
      } else {
        setError(response.data.error || 'Failed to start battle');
      }
    } catch (err: any) {
      console.error('Error starting battle:', err);
      setError(err.response?.data?.error || err.message || 'Failed to start battle');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, hero, socket]);
  
  // Select a move in battle
  const selectMove = useCallback(async (moveId: string, timingScore = 0) => {
    if (!isAuthenticated || !hero || !currentBattle) return;
    
    try {
      // Emit move selection via socket
      if (socket) {
        socket.emit('select-move', {
          battleId: currentBattle.id,
          moveId,
          timingScore
        });
      }
      
      // Also make API call for logging/validation
      const response = await axios.post(`/api/game/battle/${currentBattle.id}/turn`, {
        moveId,
        timingScore
      });
      
      // Return the response for UI updates
      return response.data;
    } catch (err: any) {
      console.error('Error selecting move:', err);
    }
  }, [isAuthenticated, hero, currentBattle, socket]);
  
  // Send an emote in battle
  const sendEmote = useCallback(async (emoji: string) => {
    if (!isAuthenticated || !hero || !currentBattle || !socket) return;
    
    try {
      // Emit emote via socket
      socket.emit('send-emote', {
        battleId: currentBattle.id,
        emoji
      });
      
      // Also make API call for logging
      await axios.post('/api/game/emote', {
        emoji,
        battleId: currentBattle.id
      });
    } catch (err: any) {
      console.error('Error sending emote:', err);
    }
  }, [isAuthenticated, hero, currentBattle, socket]);
  
  // End the current battle
  const endBattle = useCallback(() => {
    setCurrentBattle(null);
    
    // Leave the battle room
    if (socket && currentBattle) {
      socket.emit('battle:unsubscribe', currentBattle.id);
    }
  }, [currentBattle, socket]);
  
  // Timing handlers for tap-on-beat
  const startTiming = useCallback(() => {
    setIsTimingActive(true);
  }, []);
  
  const stopTiming = useCallback((score: number) => {
    setIsTimingActive(false);
    // The score will be used by the selectMove function
  }, []);
  
  // Provide the context value
  const contextValue: GameContextType = {
    hero,
    quests,
    availableMoves,
    opponents,
    currentBattle,
    loading,
    error,
    
    createHero,
    refreshHero,
    
    fetchQuests,
    claimQuestReward,
    
    fetchMoves,
    fetchOpponents,
    startBattle,
    selectMove,
    sendEmote,
    endBattle,
    
    startTiming,
    stopTiming,
    isTimingActive
  };
  
  return (
    <GameContext.Provider value={contextValue}>
      {children}
    </GameContext.Provider>
  );
};

// Custom hook to use the game context
export const useGame = () => {
  const context = useContext(GameContext);
  
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  
  return context;
}; 