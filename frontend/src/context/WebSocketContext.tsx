import React, { createContext, useContext, useEffect, useState, ReactNode, useMemo, useRef } from 'react';
import * as SocketIOClient from 'socket.io-client';
import { logInfo, logError } from '../utils/logger'; // Only import logInfo and logError
import toast from 'react-hot-toast'; // Import toast

// Define the shape of the context data
interface WebSocketContextType {
  socket: SocketIOClient.Socket | null;
  isConnected: boolean;
}

// Create the context with a default value
const WebSocketContext = createContext<WebSocketContextType>({ socket: null, isConnected: false });

// Custom hook to use the WebSocket context
export const useWebSocket = () => useContext(WebSocketContext);

// Define the props for the provider component
interface WebSocketProviderProps {
  children: ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<SocketIOClient.Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const loggedInitialRender = useRef(false);
  const lastLogTime = useRef(Date.now());
  
  // Check for either token or authToken for backwards compatibility
  const token = localStorage.getItem('token') || localStorage.getItem('authToken');

  // Debounced logging function to prevent excessive logs
  const debouncedLog = (message: string, data: any) => {
    const now = Date.now();
    // Only log once per 5 seconds for the same message type
    if (now - lastLogTime.current > 5000) {
      logInfo(message, data);
      lastLogTime.current = now;
    }
  };

  // Log initial render only once
  if (!loggedInitialRender.current) {
    logInfo('WebSocketProvider: Initial render.', { hasSocket: !!socket, isConnected, hasToken: !!token });
    loggedInitialRender.current = true;
  } else {
    // Use debounced logging for subsequent renders
    debouncedLog('WebSocketProvider: Rendering provider.', { hasSocket: !!socket, isConnected, hasToken: !!token });
  }

  useEffect(() => {
    // Only log the useEffect trigger once per token change
    logInfo('WebSocketProvider: useEffect triggered.', { hasToken: !!token });

    if (!token) {
      logError('WebSocketProvider: No token found, WebSocket connection not initiated.');
      if (socket) {
        logInfo('WebSocketProvider: useEffect - Disconnecting due to missing token.');
        socket.disconnect();
      }
      return;
    }

    if (!socket || !socket.connected) {
      const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:3001';
      logInfo('WebSocketProvider: Attempting to connect...', { backendUrl });

      const connectionOptions: Partial<SocketIOClient.ConnectOpts> = {
        reconnectionAttempts: 8,
        reconnectionDelay: 3000,
        transports: ['websocket'],
        auth: { token },
      };
      logInfo('WebSocketProvider: Connection options', connectionOptions);

      const newSocket = SocketIOClient.default(backendUrl, connectionOptions);
      logInfo('WebSocketProvider: Socket instance created (ID will be available on connect).');

      newSocket.on('connect', () => {
        logInfo(`WebSocketProvider: connect event fired. Socket ID: ${newSocket.id}`);
        setIsConnected(true);
        setSocket(newSocket);
      });

      newSocket.on('disconnect', (reason: string) => {
        logInfo(`WebSocketProvider: disconnect event fired. Reason: ${reason}`);
        setIsConnected(false);
      });

      newSocket.on('connect_error', (error: Error) => {
        logError('WebSocketProvider: connect_error event fired.', { error }); 
        setIsConnected(false);
      });
      
      newSocket.on('reconnect_attempt', (attemptNumber: number) => {
        logInfo(`WebSocketProvider: reconnect_attempt #${attemptNumber}`);
      });

      newSocket.on('reconnect', (attemptNumber: number) => {
        logInfo(`WebSocketProvider: reconnect success after attempt #${attemptNumber}`);
        setIsConnected(true);
        setSocket(newSocket);
      });

      newSocket.on('reconnect_error', (error: Error) => {
        logError('WebSocketProvider: reconnect_error', { error }); 
      });

      newSocket.on('reconnect_failed', () => {
        logError('WebSocketProvider: reconnect_failed after multiple attempts.');
        toast.error('Real-time connection failed. Please refresh the page to try again.', { duration: 10000, id: 'ws-reconnect-fail' }); 
      });

      setSocket(newSocket);
      logInfo('WebSocketProvider: Socket instance set in state (may not be connected yet).');
    } else {
      debouncedLog('WebSocketProvider: useEffect - Socket already exists and is connected/connecting.', {});
    }

    return () => {
      if (socket && socket.connected) {
        logInfo('WebSocketProvider: Cleanup - Disconnecting socket.');
        socket.disconnect();
      } else if (socket) {
        logInfo('WebSocketProvider: Cleanup - Socket exists but not connected, attempting disconnect anyway.');
        socket.disconnect();
      } else {
        logInfo('WebSocketProvider: Cleanup - Socket is null.');
      }
      setSocket(null);
      setIsConnected(false);
    };
  }, [token]);

  const contextValue = useMemo(() => ({ 
      socket, 
      isConnected 
  }), [socket, isConnected]);

  // Use debounced logging for render logs to prevent excessive API calls
  debouncedLog('WebSocketProvider: Rendering with context value', { isConnected, hasSocket: !!socket, socketId: socket?.id });

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
}; 