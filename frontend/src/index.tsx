import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import './styles/radio.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { WebSocketProvider } from './context/WebSocketContext';

// ReactDOM.createRoot is for client-side rendering from scratch.
// For SSR, we use hydrateRoot to attach to the server-rendered HTML.
const container = document.getElementById('root') as HTMLElement;
ReactDOM.hydrateRoot(
  container,
  <React.StrictMode>
    <WebSocketProvider>
      <App />
    </WebSocketProvider>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
