/* Animation styles for the party playlist */

@keyframes pulse-base {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

@keyframes subtle-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* Refined Cozy Glow Animation for Text - Tighter Shadow */
@keyframes cozy-text-glow {
  0% {
    text-shadow: 0 0 3px rgba(255, 255, 255, 0.4), 0 0 5px rgba(255, 255, 255, 0.2);
  }
  50% {
    text-shadow: 0 0 6px rgba(255, 255, 255, 0.6), 0 0 8px rgba(255, 255, 255, 0.3);
  }
  100% {
    text-shadow: 0 0 3px rgba(255, 255, 255, 0.4), 0 0 5px rgba(255, 255, 255, 0.2);
  }
}

.animate-cozy-glow {
  animation: cozy-text-glow 2.0s infinite ease-in-out;
}

.pulse-animation {
  animation: pulse-base 2s infinite ease-in-out;
}

.animate-subtle-pulse {
  animation: subtle-pulse 3s infinite ease-in-out;
}

/* Dance style-specific pulse animations */
.animate-pulse-pink {
  animation: pulse-pink 2s infinite ease-in-out;
}

.animate-pulse-red {
  animation: pulse-red 2s infinite ease-in-out;
}

.animate-pulse-blue {
  animation: pulse-blue 2s infinite ease-in-out;
}

.animate-pulse-purple {
  animation: pulse-purple 2s infinite ease-in-out;
}

.animate-pulse-green {
  animation: pulse-green 2s infinite ease-in-out;
}

.animate-pulse-orange {
  animation: pulse-orange 2s infinite ease-in-out;
}

.animate-pulse-gray {
  animation: pulse-base 2s infinite ease-in-out;
}

@keyframes pulse-pink {
  0% {
    background-color: rgba(236, 72, 153, 0.7);
  }
  50% {
    background-color: rgba(236, 72, 153, 1);
  }
  100% {
    background-color: rgba(236, 72, 153, 0.7);
  }
}

@keyframes pulse-red {
  0%, 100% {
    background-color: rgba(239, 68, 68, 0.1);
  }
  50% {
    background-color: rgba(239, 68, 68, 0.2);
  }
}

@keyframes pulse-blue {
  0% {
    background-color: rgba(59, 130, 246, 0.7);
  }
  50% {
    background-color: rgba(59, 130, 246, 1);
  }
  100% {
    background-color: rgba(59, 130, 246, 0.7);
  }
}

@keyframes pulse-purple {
  0% {
    background-color: rgba(168, 85, 247, 0.7);
  }
  50% {
    background-color: rgba(168, 85, 247, 1);
  }
  100% {
    background-color: rgba(168, 85, 247, 0.7);
  }
}

@keyframes pulse-green {
  0%, 100% {
    background-color: rgba(34, 197, 94, 0.1);
  }
  50% {
    background-color: rgba(34, 197, 94, 0.2);
  }
}

@keyframes pulse-orange {
  0% {
    background-color: rgba(249, 115, 22, 0.7);
  }
  50% {
    background-color: rgba(249, 115, 22, 1);
  }
  100% {
    background-color: rgba(249, 115, 22, 0.7);
  }
}

/* == New Text Color Pulse Animations == */

/* Bachata - Green */
@keyframes pulse-text-green {
  0%, 100% { color: #166534; /* green-800 */ }
  50% { color: #16a34a; /* green-600 */ }
}
.animate-pulse-text-green { animation: pulse-text-green 2s infinite ease-in-out; }

/* Salsa - Red */
@keyframes pulse-text-red {
  0%, 100% { color: #b91c1c; /* red-700 */ }
  50% { color: #dc2626; /* red-600 */ }
}
.animate-pulse-text-red { animation: pulse-text-red 2s infinite ease-in-out; }

/* Kizomba - Sky Blue */
@keyframes pulse-text-sky {
  0%, 100% { color: #075985; /* sky-800 */ }
  50% { color: #0284c7; /* sky-600 */ }
}
.animate-pulse-text-sky { animation: pulse-text-sky 2s infinite ease-in-out; }

/* Zouk - Yellow */
@keyframes pulse-text-yellow {
  0%, 100% { color: #854d0e; /* yellow-800 */ }
  50% { color: #a16207; /* yellow-700 */ }
}
.animate-pulse-text-yellow { animation: pulse-text-yellow 2s infinite ease-in-out; }

/* Mixed - Pink */
@keyframes pulse-text-pink {
  0%, 100% { color: #be185d; /* pink-700 */ }
  50% { color: #db2777; /* pink-600 */ }
}
.animate-pulse-text-pink { animation: pulse-text-pink 2s infinite ease-in-out; }

/* Default - Gray */
@keyframes pulse-text-gray {
  0%, 100% { color: #374151; /* gray-700 */ }
  50% { color: #4b5563; /* gray-600 */ }
}
.animate-pulse-text-gray { animation: pulse-text-gray 2s infinite ease-in-out; }

/* Pulsing animations for different dance styles */
@keyframes pulse-sky {
  0%, 100% {
    background-color: rgba(14, 165, 233, 0.1);
  }
  50% {
    background-color: rgba(14, 165, 233, 0.2);
  }
}

@keyframes pulse-yellow {
  0%, 100% {
    background-color: rgba(234, 179, 8, 0.1);
  }
  50% {
    background-color: rgba(234, 179, 8, 0.2);
  }
}

.animate-pulse-red {
  animation: pulse-red 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-pulse-green {
  animation: pulse-green 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-pulse-sky {
  animation: pulse-sky 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-pulse-yellow {
  animation: pulse-yellow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Currently playing song animation */
.currently-playing {
  position: relative;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.currently-playing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid rgba(255, 255, 255, 0.7);
  border-radius: 0.5rem;
  animation: pulse-border 1.5s ease-in-out infinite;
  pointer-events: none;
}

@keyframes pulse-border {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
} 