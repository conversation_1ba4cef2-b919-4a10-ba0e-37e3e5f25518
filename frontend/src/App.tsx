import React from 'react';
import { BrowserRouter as ClientRouter, Routes, Route, Navigate, Router } from 'react-router-dom';
import { Suspense } from 'react';
import Layout from './components/Layout';
import LoadingSpinner from './components/LoadingSpinner';
import * as AdminPages from './pages/admin'; // Import all admin pages
import BraveSearchPage from './pages/admin/BraveSearchPage'; // Added for Brave Search
import * as DancerPages from './pages/dancer'; // Import all dancer pages
import * as PublicPages from './pages'; // Import all public pages
import FullLeaderboardPage from './pages/FullLeaderboardPage'; // Added import
import FullTopVotedPage from './pages/FullTopVotedPage'; // Added import for Top Voted Users
import ErrorBoundary from './components/ErrorBoundary';
import { Toaster } from 'react-hot-toast';
import { WebSocketProvider } from './context/WebSocketContext';
import { useScrollRestoration } from './hooks/useScrollRestoration';
import { AuthProvider } from './context/AuthProvider';
import { useAuthContext } from './context/AuthContext';
import { ThemeProvider } from './context/ThemeContext';
import { GameProvider } from './context/GameContext'; // Added import for GameProvider
import { createMemoryHistory, MemoryHistory } from 'history'; // For SSR

// Define props for App, including optional ones for SSR
interface AppProps {
  location?: string; // Passed from server.js for SSR path
  // routerContext could be added if needed for redirects, but v6 handles this differently
}

function RequireAuth({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuthContext();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <WebSocketProvider>{children}</WebSocketProvider>;
}

function RequireAdmin({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading, user } = useAuthContext();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (!user?.isAdmin) {
    return <Navigate to="/dancer" replace />;
  }

  return <WebSocketProvider>{children}</WebSocketProvider>;
}

function App(props: AppProps) {
  const isSSR = typeof window === 'undefined';

  // Only call browser-specific hooks on the client
  if (!isSSR) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useScrollRestoration();
  }

  // Create a memory history for SSR if location is provided
  // This ref will hold the history object for SSR
  // Only create ref on client-side, for SSR we'll use a simple variable
  let ssrHistory: MemoryHistory | undefined = undefined;
  let ssrHistoryRef;
  
  if (isSSR) {
    // In SSR mode, create history directly without useRef
    if (props.location) {
      ssrHistory = createMemoryHistory({ initialEntries: [props.location] });
    }
  } else {
    // Only use useRef on client-side
    // eslint-disable-next-line react-hooks/rules-of-hooks
    ssrHistoryRef = React.useRef<MemoryHistory>();
    if (props.location && !ssrHistoryRef.current) {
      ssrHistoryRef.current = createMemoryHistory({ initialEntries: [props.location] });
    }
  }

  const routesContent = (
    <Suspense fallback={<LoadingSpinner />}>
      <Toaster
        position="bottom-center"
        reverseOrder={false}
        toastOptions={{
          duration: 5000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: 'green',
              secondary: 'white',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: 'red',
              secondary: 'white'
            }
          }
        }}
      />
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={<PublicPages.Login />} />
        <Route path="/signup" element={<PublicPages.Signup />} />
        <Route path="/public-playlist" element={<PublicPages.PublicPlayer />} />
        <Route path="/" element={<PublicPages.WelcomePage />} />
        <Route path="/charts" element={<DancerPages.Charts />} />
        <Route path="/charts/:chartType/:style" element={<DancerPages.ChartDetails />} />
        <Route path="/user/:userId" element={<DancerPages.UserProfile />} />
        <Route path="/song/:youtubeVideoId" element={<PublicPages.SongDetail />} />
        <Route path="/djs" element={<PublicPages.DJsPage />} />
        <Route path="/dancers" element={<PublicPages.DancersPage />} />
        <Route path="/singers" element={<PublicPages.SingersPage />} />
        
        {/* Radio player route */}
        {/* <Route path="/radio" element={<RadioPage />} /> */}

        {/* New Radio Channel Routes */}
        <Route path="/radio/my-songs" element={<DancerPages.MySongsRadioPage />} />
        <Route path="/radio/liked-songs" element={<DancerPages.LikedSongsRadioPage />} />
        <Route path="/radio/general" element={<DancerPages.GeneralRadioPage />} />
        
        {/* New public pages */}
        <Route path="/authors" element={<PublicPages.AuthorsPage />} />
        <Route path="/contributors" element={<PublicPages.ContributorsPage />} />
        <Route path="/feedback" element={<PublicPages.FeedbackPage />} />
        <Route path="/clubs" element={<DancerPages.ClubsPage />} />
        <Route path="/clubs/:clubId" element={<DancerPages.ClubDetailsPage />} />
        <Route path="/events" element={<DancerPages.EventsPage />} />
        <Route path="/events/:eventId" element={<DancerPages.EventDetailsPage />} />
        <Route path="/article/:id" element={<PublicPages.ArticlePage />} />
        
        {/* Protected routes */}
        <Route path="/" element={<Layout />}>
          {/* Admin routes */}
          <Route path="/admin" element={<RequireAdmin><PublicPages.AdminDashboard /></RequireAdmin>} />
          <Route path="/admin/playlist" element={<RequireAdmin><AdminPages.PlaylistManagement /></RequireAdmin>} />
          <Route path="/admin/suggestions" element={<RequireAdmin><AdminPages.SuggestionOversight /></RequireAdmin>} />
          <Route path="/admin/settings" element={<RequireAdmin><AdminPages.AdminSettings /></RequireAdmin>} />
          <Route path="/admin/clubs" element={<RequireAdmin><AdminPages.ClubsManagement /></RequireAdmin>} />
          <Route path="/admin/events" element={<RequireAdmin><AdminPages.EventsManagement /></RequireAdmin>} />
          <Route path="/admin/authors" element={<RequireAdmin><AdminPages.AuthorsManagement /></RequireAdmin>} />
          <Route path="/admin/contributors" element={<RequireAdmin><AdminPages.ContributorsManagement /></RequireAdmin>} />
          <Route path="/admin/feedback" element={<RequireAdmin><AdminPages.FeedbackManagement /></RequireAdmin>} />
          <Route path="/admin/notifications" element={<RequireAdmin><AdminPages.NotificationsManagement /></RequireAdmin>} />
          <Route path="/admin/content-discovery" element={<RequireAdmin><BraveSearchPage /></RequireAdmin>} />
          
          {/* Dancer routes */}
          {/* Note: Using DancerDashboardPage from ./pages for the /dancer route if intended, otherwise use DancerPages.DancerDashboard */} 
          <Route path="/dancer" element={<RequireAuth><DancerPages.DancerDashboard /></RequireAuth>} /> 
          <Route path="/dancer/playlist" element={<RequireAuth><DancerPages.ViewPlaylist /></RequireAuth>} /> 
          <Route path="/dancer/suggestion-history" element={<RequireAuth><DancerPages.SuggestionHistoryPage /></RequireAuth>} />
          <Route path="/dancer/notifications" element={<RequireAuth><DancerPages.NotificationsPage /></RequireAuth>} />
          <Route path="/dancer/dance-hero" element={<RequireAuth><GameProvider><DancerPages.DanceHero /></GameProvider></RequireAuth>} />
          <Route path="/leaderboard/all" element={<RequireAuth><FullLeaderboardPage /></RequireAuth>} />
          <Route path="/full-top-voted" element={<RequireAuth><FullTopVotedPage /></RequireAuth>} />
          <Route path="/dancer/suggest" element={<DancerPages.SuggestSong />} />
          <Route path="/dancer/vote" element={<DancerPages.VoteSuggestions />} />
          <Route path="/dancer/history" element={<DancerPages.SuggestionHistoryPage />} />
          <Route path="/charts" element={<DancerPages.Charts />} />
          <Route path="/charts/:chartType/:style" element={<DancerPages.ChartDetails />} />
        </Route>
        {/* 404 route */}
        <Route path="*" element={<PublicPages.NotFound />} />
      </Routes>
    </Suspense>
  );

  return (
    <AuthProvider>
      <WebSocketProvider>
        <ThemeProvider>
          <div className="App min-h-screen bg-gradient-to-r from-[var(--gradient-start)] via-[var(--gradient-mid)] to-[var(--gradient-end)]">
            {isSSR && ssrHistory ? (
              <Router location={ssrHistory.location} navigator={ssrHistory}>
                <ErrorBoundary>
                  {routesContent}
                </ErrorBoundary>
              </Router>
            ) : (!isSSR && ssrHistoryRef?.current) ? (
              <Router location={ssrHistoryRef.current.location} navigator={ssrHistoryRef.current}>
                <ErrorBoundary>
                  {routesContent}
                </ErrorBoundary>
              </Router>
            ) : (
              <ClientRouter>
                <ErrorBoundary>
                  {routesContent}
                </ErrorBoundary>
              </ClientRouter>
            )}
          </div>
        </ThemeProvider>
      </WebSocketProvider>
    </AuthProvider>
  );
}

export default App;
