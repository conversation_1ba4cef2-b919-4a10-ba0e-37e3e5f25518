const LOG_STORAGE_KEY = 'logs';
const MAX_LOG_ENTRIES = 500; // Define max number of entries
const LOG_QUEUE_KEY = 'log_queue';
const MAX_QUEUE_SIZE = 100;
const LOG_THROTTLE_INTERVAL = 10000; // Increased from 1000ms to 10000ms (10 seconds)
const BATCH_SIZE = 10; // Number of logs to send in a single request
const BATCH_WAIT_TIME = 5000; // Time to wait for collecting logs into a batch

// Define LogLevel type
type LogLevel = 'info' | 'warn' | 'error' | 'debug';

// Queue for pending logs
interface QueuedLog {
  level: LogLevel;
  message: string;
  error?: any;
  info?: any;
  data?: any;
  timestamp: string;
  retryCount: number;
}

let isProcessingQueue = false;
let lastLogSentTime = 0;
// Add a logs counter to avoid excessive logging
let logsSentInLastMinute = 0;
const MAX_LOGS_PER_MINUTE = 6; // Reduced from 60 to 6 (one log per 10 seconds on average)
let lastLogCountResetTime = Date.now();
let batchCollectionTimerId: number | null = null;

// Load log queue from localStorage
const loadLogQueue = (): QueuedLog[] => {
  try {
    const queueStr = localStorage.getItem(LOG_QUEUE_KEY);
    if (queueStr) {
      return JSON.parse(queueStr);
    }
  } catch (e) {
    console.error('Error loading log queue:', e);
  }
  return [];
};

// Save log queue to localStorage
const saveLogQueue = (queue: QueuedLog[]) => {
  try {
    // If queue is too large, trim it
    if (queue.length > MAX_QUEUE_SIZE) {
      queue = queue.slice(-MAX_QUEUE_SIZE);
    }
    localStorage.setItem(LOG_QUEUE_KEY, JSON.stringify(queue));
  } catch (e) {
    console.error('Error saving log queue:', e);
  }
};

// Filter duplicate or redundant logs
const isRedundantLog = (queue: QueuedLog[], newLog: QueuedLog): boolean => {
  if (queue.length === 0) return false;
  
  // Check the last 10 logs or all logs if less than 10
  const recentLogs = queue.slice(-10);
  
  return recentLogs.some(existingLog => 
    existingLog.level === newLog.level &&
    existingLog.message === newLog.message &&
    JSON.stringify(existingLog.data) === JSON.stringify(newLog.data)
  );
};

// Add log to queue
const queueLog = (level: LogLevel, message: string, error?: any, info?: any, data?: any) => {
  const newLog = {
    level,
    message,
    error,
    info,
    data,
    timestamp: new Date().toISOString(),
    retryCount: 0
  };
  
  const queue = loadLogQueue();
  
  // Skip redundant logs
  if (isRedundantLog(queue, newLog)) {
    return;
  }
  
  queue.push(newLog);
  saveLogQueue(queue);
  
  // Start batch collection timer if not already started
  if (batchCollectionTimerId === null) {
    batchCollectionTimerId = window.setTimeout(() => {
      batchCollectionTimerId = null;
      processLogQueue();
    }, BATCH_WAIT_TIME);
  }
};

// Process the log queue with throttling and error handling
const processLogQueue = async () => {
  if (isProcessingQueue) return;
  isProcessingQueue = true;
  
  try {
    const queue = loadLogQueue();
    if (queue.length === 0) {
      isProcessingQueue = false;
      return;
    }
    
    const now = Date.now();
    
    // Reset the counter if a minute has passed
    if (now - lastLogCountResetTime > 60000) {
      logsSentInLastMinute = 0;
      lastLogCountResetTime = now;
    }
    
    // Check if we've sent too many logs in the last minute
    if (logsSentInLastMinute >= MAX_LOGS_PER_MINUTE) {
      // If we're over the limit, wait until the reset
      const timeToWait = 60000 - (now - lastLogCountResetTime);
      setTimeout(processLogQueue, timeToWait > 0 ? timeToWait : 1000);
      isProcessingQueue = false;
      return;
    }
    
    const timeSinceLastLog = now - lastLogSentTime;
    
    if (timeSinceLastLog < LOG_THROTTLE_INTERVAL) {
      // Need to wait before sending next log
      setTimeout(processLogQueue, LOG_THROTTLE_INTERVAL - timeSinceLastLog);
      isProcessingQueue = false;
      return;
    }
    
    // Get up to BATCH_SIZE logs to send in one request
    const logsToSend = queue.slice(0, BATCH_SIZE);
    
    try {
      const response = await fetch('/api/auth/logs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          batch: true,
          logs: logsToSend
        }),
      });
      
      lastLogSentTime = now;
      logsSentInLastMinute++; // Increment the counter once per batch
      
      if (response.ok) {
        // Logs sent successfully, remove from queue
        queue.splice(0, logsToSend.length);
        saveLogQueue(queue);
      } else if (response.status === 429) {
        // Rate limited - wait longer before retrying
        setTimeout(processLogQueue, 30000); // Increase wait time to 30 seconds on rate limit
        isProcessingQueue = false;
        return;
      } else {
        // Other error - increment retry count for all logs in batch
        logsToSend.forEach(log => {
          log.retryCount++;
          if (log.retryCount > 3) {
            // Remove logs that have been retried too many times
            const index = queue.indexOf(log);
            if (index !== -1) {
              queue.splice(index, 1);
            }
          }
        });
        saveLogQueue(queue);
      }
    } catch (e) {
      // Network error or other exception
      logsToSend.forEach(log => {
        log.retryCount++;
        if (log.retryCount > 3) {
          // Remove logs that have been retried too many times
          const index = queue.indexOf(log);
          if (index !== -1) {
            queue.splice(index, 1);
          }
        }
      });
      saveLogQueue(queue);
    }
    
    // If there are more logs, process the next batch after the throttle interval
    if (queue.length > 0) {
      setTimeout(processLogQueue, LOG_THROTTLE_INTERVAL);
    }
  } finally {
    isProcessingQueue = false;
  }
};

// Simple frontend logger utility - replaced with queued version
const sendLogToServer = (level: LogLevel, message: string, error?: any, info?: any, data?: any) => {
  // For debug level logs, only save locally and don't send to server
  if (level === 'debug') {
    return;
  }
  
  // Skip logs with common repetitive messages that aren't critical
  if (message.includes('Fetch already in progress') || 
      message.includes('Using cached data') ||
      message.includes('Background refresh')) {
    return;
  }
  
  queueLog(level, message, error, info, data);
};

// Start processing the log queue when the script loads
setTimeout(processLogQueue, 10000); // Increased initial delay to 10 seconds

export function logError(message: string, error?: any, info?: any) {
  let errorDetails: any = undefined;
  if (error) {
    if (error instanceof Error) {
      errorDetails = { message: error.message, stack: error.stack };
    } else if (typeof error === 'object') {
      errorDetails = JSON.stringify(error);
    } else {
      errorDetails = String(error);
    }
  }

  const logEntry = {
    level: 'error',
    message,
    error: errorDetails,
    info,
    timestamp: new Date().toISOString(),
  };
  try {
    // Store in localStorage with rotation
    let logs = [];
    try {
        const storedLogs = localStorage.getItem(LOG_STORAGE_KEY);
        if (storedLogs) {
            logs = JSON.parse(storedLogs);
        }
    } catch (parseError) {
        console.error('Error parsing logs from localStorage:', parseError);
        localStorage.removeItem(LOG_STORAGE_KEY); // Clear corrupted data
        logs = []; // Start fresh
    }

    if (!Array.isArray(logs)) { // Ensure it's an array
        console.error('Corrupted log data in localStorage is not an array. Resetting.');
        logs = [];
    }

    logs.push(logEntry);

    // Rotate logs: Keep only the last MAX_LOG_ENTRIES
    if (logs.length > MAX_LOG_ENTRIES) {
      logs = logs.slice(logs.length - MAX_LOG_ENTRIES);
    }

    localStorage.setItem(LOG_STORAGE_KEY, JSON.stringify(logs));
    sendLogToServer('error', message, errorDetails, info);
  } catch (e) {
    // fallback
    // eslint-disable-next-line no-console
    console.error('Failed to log error', logEntry, e);
  }
}

export function logWarn(message: string, data?: any) {
  const logEntry = {
    level: 'warn',
    message,
    data,
    timestamp: new Date().toISOString(),
  };
  try {
    let logs = [];
    try {
        const storedLogs = localStorage.getItem(LOG_STORAGE_KEY);
        if (storedLogs) {
            logs = JSON.parse(storedLogs);
        }
    } catch (parseError) {
        console.error('Error parsing logs from localStorage:', parseError);
        localStorage.removeItem(LOG_STORAGE_KEY); // Clear corrupted data
        logs = []; // Start fresh
    }

    if (!Array.isArray(logs)) { // Ensure it's an array
        console.error('Corrupted log data in localStorage is not an array. Resetting.');
        logs = [];
    }

    logs.push(logEntry);

    // Rotate logs: Keep only the last MAX_LOG_ENTRIES
    if (logs.length > MAX_LOG_ENTRIES) {
      logs = logs.slice(logs.length - MAX_LOG_ENTRIES);
    }

    localStorage.setItem(LOG_STORAGE_KEY, JSON.stringify(logs));
    sendLogToServer('warn', message, undefined, undefined, data);
  } catch (e) {
    // eslint-disable-next-line no-console
    console.warn('Failed to log warning', logEntry, e);
  }
}

export function logInfo(message: string, data?: any) {
  const logEntry = {
    level: 'info',
    message,
    data,
    timestamp: new Date().toISOString(),
  };
  try {
    let logs = [];
    try {
        const storedLogs = localStorage.getItem(LOG_STORAGE_KEY);
        if (storedLogs) {
            logs = JSON.parse(storedLogs);
        }
    } catch (parseError) {
        console.error('Error parsing logs from localStorage:', parseError);
        localStorage.removeItem(LOG_STORAGE_KEY); // Clear corrupted data
        logs = []; // Start fresh
    }

    if (!Array.isArray(logs)) { // Ensure it's an array
        console.error('Corrupted log data in localStorage is not an array. Resetting.');
        logs = [];
    }

    logs.push(logEntry);

    // Rotate logs: Keep only the last MAX_LOG_ENTRIES
    if (logs.length > MAX_LOG_ENTRIES) {
      logs = logs.slice(logs.length - MAX_LOG_ENTRIES);
    }

    localStorage.setItem(LOG_STORAGE_KEY, JSON.stringify(logs));
    sendLogToServer('info', message, undefined, undefined, data);
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('Failed to log info', logEntry, e);
  }
} 