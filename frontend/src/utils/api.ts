import { logInfo, logError } from './logger';
// Remove this import if api<PERSON>lient is not used
// import { apiClient } from './apiClient'; 
import { Song } from '../types/song';

// Add a simple in-memory cache for 304 responses
const apiResponseCache: Record<string, any> = {};

// Helper to get the auth token
const getAuthToken = (): string | null => {
  // Try both possible token keys for backward compatibility
  return localStorage.getItem('token') || localStorage.getItem('authToken');
};

// Helper to create Authorization headers
export const getAuthHeaders = (): HeadersInit => {
  const token = getAuthToken();
  // Don't send Authorization header if token is a placeholder or missing
  // The application uses HttpOnly cookies for authentication
  if (!token || token === 'session_active') {
    return {};
  }
  return { Authorization: `Bearer ${token}` };
};

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  status?: number;
  pagination?: {
    currentPage?: number;
    totalPages?: number;
    totalCount?: number;
    limit?: number;
  };
}

// Define enum here to match AdminSettings.tsx (or move to shared types)
enum PlaylistMode {
  GENERATED = 'GENERATED',
  EXTERNAL_MIRROR = 'EXTERNAL_MIRROR',
}

// Fetch wrapper with consistent error handling and logging
// Updated to be more generic and include a context parameter for logging
export async function fetchApi<T = any>(
  url: string,
  options: RequestInit = {},
  logContext: string = 'fetchApi' // Generic context if not provided
): Promise<ApiResponse<T>> {
  const method = options.method || 'GET';
  logInfo(`API Call: ${logContext}`, { url, method, options: { ...options, headers: undefined } }); // Log options minus headers

  try {
    const response = await fetch(url, {
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options, // Spread other options like method, body, etc.
    });

    // --- Specific Handling for 304 Not Modified ---
    if (response.status === 304) {
      logInfo(`API Info: ${logContext} content not modified (304)`, { status: 304, url });
      // Check if we have a cached response for this URL
      if (apiResponseCache[url]) {
        logInfo(`API Info: ${logContext} returning cached data for 304 response`, { url });
        return { ...apiResponseCache[url], status: 304 };
      }
      // If no cached data found (should be rare), return a generic success response
      return { success: true, status: 304, data: ([] as unknown) as T, message: 'Content not modified' };
    }
    // --- End 304 Handling ---

    // --- Specific Handling for 429 Rate Limit ---
    if (response.status === 429) {
      const message = 'Rate limit exceeded. Please try again shortly.';
      logError(`API Error: ${logContext} rate limited (429)`, { status: 429, url });
      // Try to read potential text message, but don't fail if it's not text
      let responseText = '';
      try {
           responseText = await response.text();
      } catch {}
      return { success: false, message: responseText || message, status: 429 };
    }
    // --- End 429 Handling ---

    // Attempt to parse JSON, handle errors gracefully
    let responseData: any;
    try {
      responseData = await response.json();
    } catch (jsonError: any) {
       // Handle cases where response is OK (2xx) but not JSON, or Fetch failed entirely before JSON parse
       if (response.ok) {
           logError(`API Error: ${logContext} received non-JSON response`, { status: response.status, url });
           return { success: false, message: 'Received invalid data structure from server.', status: response.status };
       } else {
           // If response was not ok AND not JSON, construct error message
           const message = `HTTP Error ${response.status} (${response.statusText})`;
           logError(`API Error: ${logContext} failed (non-JSON body)`, { status: response.status, statusText: response.statusText, url });
           return { success: false, message: responseData?.message || responseData?.error || message, status: response.status };
       }
    }

    // Handle non-OK responses that WERE valid JSON
    if (!response.ok) {
      logError(`API Error: ${logContext} failed`, { status: response.status, response: responseData, url });
      return { success: false, message: responseData.message || responseData.error || `HTTP Error ${response.status}`, status: response.status };
    }

    // Success case
    logInfo(`API Success: ${logContext}`, { status: response.status /* response: responseData - potentially too verbose */ });
    
    // Handle special case if we get a nested structure with "success" and "data" fields
    if (responseData.success === true && responseData.data && 
        typeof responseData.data === 'object' && 
        responseData.data.success === true) {
      // This is a nested structure, unwrap it
      logInfo(`API Unwrapping: ${logContext} - detected nested success/data structure`);
      responseData = responseData.data;
    }
    
    // Structure the success response consistently
    let successResponse: ApiResponse<T>;

    // ---- START DEBUG LOGGING ----
    logInfo(`[fetchApi Debug - ${logContext}] Raw responseData:`, responseData);
    const hasDataField = 'data' in responseData;
    const hasPaginationBlock = responseData.pagination && typeof responseData.pagination === 'object';
    // Check for common array keys at the top level if 'data' isn't present
    const hasTopLevelSuggestions = 'suggestions' in responseData && Array.isArray(responseData.suggestions);
    const hasTopLevelItems = 'items' in responseData && Array.isArray(responseData.items);
    const hasTopLevelResults = 'results' in responseData && Array.isArray(responseData.results);

    logInfo(`[fetchApi Debug - ${logContext}] Checks:`, {
      hasDataField,
      hasPaginationBlock,
      hasTopLevelSuggestions,
      hasTopLevelItems,
      hasTopLevelResults
      // ... (keep existing top-level page checks if needed, though less relevant with hasPaginationBlock)
    });
    // ---- END DEBUG LOGGING ----

    if (responseData && typeof responseData === 'object') {
        // Case 1: Backend wrapped it in { data: ..., pagination: ... }
        if (hasDataField && hasPaginationBlock) {
            logInfo(`[fetchApi Debug - ${logContext}] Branch 1: Using 'data' field and 'pagination' block.`);
            successResponse = { 
                success: true, 
                data: responseData.data as T, 
                pagination: responseData.pagination, 
                message: responseData.message, 
                status: response.status 
            }; 
        // Case 2: Backend returned { suggestions: [], pagination: {} } or { items: [], pagination: {} } etc.
        } else if ((hasTopLevelSuggestions || hasTopLevelItems || hasTopLevelResults) && hasPaginationBlock) {
            logInfo(`[fetchApi Debug - ${logContext}] Branch 2: Using top-level array and 'pagination' block.`);
            let mainData: T;
            if (hasTopLevelSuggestions) mainData = responseData.suggestions as T;
            else if (hasTopLevelItems) mainData = responseData.items as T;
            else mainData = responseData.results as T; // Default to results if others not found

            successResponse = {
                success: true,
                data: mainData,
                pagination: responseData.pagination,
                message: responseData.message,
                status: response.status
            };
        // Case 3: Backend returned data field and top-level pagination fields (less ideal, but handle)
        } else if (hasDataField && (responseData.currentPage !== undefined || responseData.totalPages !== undefined)) {
            logInfo(`[fetchApi Debug - ${logContext}] Branch 3: Using 'data' field and top-level pagination fields.`);
            successResponse = {
                success: true,
                data: responseData.data as T,
                pagination: {
                    currentPage: responseData.currentPage,
                    totalPages: responseData.totalPages,
                    totalCount: responseData.totalCount,
                    limit: responseData.limit
                },
                message: responseData.message,
                status: response.status
            };
        // Case 4: Backend returned success and message, but no data
        } else if (responseData.success === true && !hasDataField) {
            logInfo(`[fetchApi Debug - ${logContext}] Branch 4.1: Found success flag but no data field.`);
            successResponse = { 
                success: true, 
                message: responseData.message, 
                status: response.status 
            };
        // Case 5: Assume the entire responseData is the data payload, no specific pagination structure detected
        } else {
            logInfo(`[fetchApi Debug - ${logContext}] Branch 4: Assuming responseData is the data payload (no specific pagination structure detected).`);
            successResponse = { success: true, data: responseData as T, status: response.status };
        }
    } else {
         logInfo(`[fetchApi Debug - ${logContext}] Branch 5: responseData is not an object or is null/undefined.`);
         // Handle cases where response might be just a message or non-object (though unusual for success)
         successResponse = { success: true, data: responseData as T, message: typeof responseData === 'string' ? responseData : undefined, status: response.status };
    }

    // ---- START DEBUG LOGGING ----
    logInfo(`[fetchApi Debug - ${logContext}] Constructed successResponse:`, successResponse);
    // ---- END DEBUG LOGGING ----

    // For GET requests, cache the successful response for potential 304 usage later
    if (method === 'GET') {
      apiResponseCache[url] = successResponse;
    }

    return successResponse;

  } catch (error: any) {
    // Catch fetch-level errors (network issues, DNS errors etc.)
    logError(`API Exception: ${logContext}`, error, { url });
    return { success: false, message: error.message || 'An unexpected network error occurred.' };
  }
}
// --- End Central Fetch Handler ---

// Define User Search Result structure here as well
interface UserSearchResult {
  id: string;
  username: string;
  credits: number;
}

// Search for AdminSettings interface and add isYouTubeConnected if it doesn't exist
export interface AdminSettings {
  // ... existing properties
  isYouTubeConnected: boolean;
}

// --- Playlist Generation & Sync API Functions --- 

/**
 * Calls the backend endpoint to generate a new YouTube playlist from the active template.
 * Available only in GENERATED mode.
 */
export const generatePlaylist = async (): Promise<ApiResponse> => {
  return fetchApi('/api/playlists/generate', { method: 'POST' }, 'generatePlaylist');
};

/**
 * Calls the backend endpoint to sync the active playlist with YouTube.
 */
export const syncPlaylist = async (): Promise<ApiResponse> => {
  return fetchApi('/api/admin/sync-playlist', { method: 'POST' }, 'syncPlaylist');
};

// --- Suggestion API Functions ---

/**
 * Fetches suggestions (likely for admin view).
 * TODO: Confirm if a specific admin endpoint is needed.
 */
export const fetchAdminSuggestions = async (params?: { 
  page?: number; 
  limit?: number; 
  status?: 'PENDING' | 'APPROVED' | 'REJECTED'; 
  danceStyle?: string;
  searchTerm?: string;
  sortBy?: 'newest' | 'oldest';
}): Promise<ApiResponse> => {
  let url = '/api/suggestions';
  if (params) {
    const queryParams = new URLSearchParams();
    if (params.page !== undefined) queryParams.append('page', params.page.toString());
    if (params.limit !== undefined) queryParams.append('limit', params.limit.toString());
    if (params.status) queryParams.append('status', params.status);
    if (params.danceStyle) queryParams.append('danceStyle', params.danceStyle);
    if (params.searchTerm) queryParams.append('searchTerm', params.searchTerm);
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    
    const queryString = queryParams.toString();
    if (queryString) {
      url += `?${queryString}`;
    }
  }
  return fetchApi(url, { method: 'GET' }, 'fetchAdminSuggestions');
};

/**
 * Approves a suggestion.
 */
export const approveSuggestion = async (suggestionId: string): Promise<ApiResponse> => {
   return fetchApi(`/api/suggestions/${suggestionId}/approve`, { method: 'POST' }, 'approveSuggestion');
};

/**
 * Rejects a suggestion.
 */
export const rejectSuggestion = async (suggestionId: string): Promise<ApiResponse> => {
   return fetchApi(`/api/suggestions/${suggestionId}/reject`, { method: 'POST' }, 'rejectSuggestion');
};

/**
 * Locks or unlocks a suggestion from being voted on or commented on.
 */
export const updateSuggestionLock = async (suggestionId: string, isLocked: boolean): Promise<ApiResponse> => {
    return fetchApi(`/api/suggestions/${suggestionId}/lock`, {
        method: 'PUT',
        body: JSON.stringify({ isLocked }),
    }, 'updateSuggestionLock');
};

/**
 * Updates a suggestion's dance style.
 */
export const updateSuggestionStyle = async (suggestionId: string, danceStyle: string): Promise<ApiResponse> => {
    return fetchApi(`/api/suggestions/${suggestionId}/update-style`, {
        method: 'PUT',
        body: JSON.stringify({ danceStyle }),
    }, 'updateSuggestionStyle');
};

/**
 * Deletes a suggestion (admin only).
 */
export const deleteSuggestion = async (suggestionId: string): Promise<ApiResponse> => {
    return fetchApi(`/api/suggestions/${suggestionId}`, { method: 'DELETE' }, 'deleteSuggestion');
};

/**
 * Fetches the current user's suggestion history.
 */
export const getSuggestionHistory = async (page: number = 1, limit: number = 15, danceStyle?: string): Promise<ApiResponse> => {
  const params = new URLSearchParams();
  params.append('page', page.toString());
  params.append('limit', limit.toString());
  if (danceStyle && danceStyle !== 'all') {
    params.append('danceStyle', danceStyle);
  }
  return fetchApi(`/api/suggestions/my-history?${params.toString()}`, { method: 'GET' }, 'getSuggestionHistory');
};

// --- Settings API Functions ---

// Base interface for Admin Settings data from API
export interface AdminSettingsData {
  isWatchHistorySyncEnabled?: boolean;
  watchHistorySyncIntervalSeconds?: number;
  youtubeEmail?: string;
  youtubeProfileId?: string;
  isYouTubeConnected?: boolean;
  accessToken?: string;
  refreshToken?: string;
  tokenExpiry?: string;
  operationalMode?: string;
  suggestionPlaylistIdsJson?: Record<string, string> | null;
  styleSourcePlaylists?: Record<string, string> | null;
  styleSequentialExtractionPrefs?: Record<string, boolean> | null;
  externalMirrorPlaylistId?: string | null;
  activeYoutubePlaylistId?: string | null;
  playlistSubmitterUsernames?: string[];
  singletonLock?: boolean;
  id?: number;
  createdAt?: string;
  updatedAt?: string;
  dailySuggestionLimit?: number;
  defaultCredits?: number;
}

/**
 * Fetches the current admin settings from the backend.
 */
export const getAdminSettings = async (): Promise<ApiResponse<AdminSettingsData>> => {
  return fetchApi<AdminSettingsData>('/api/settings', { method: 'GET' }, 'getAdminSettings');
};

interface UpdateAdminSettingsData {
    youtubeEmail?: string;
    youtubeProfileId?: string;
    isYouTubeConnected?: boolean;
    accessToken?: string;
    refreshToken?: string;
    tokenExpiry?: string;
    dailySuggestionLimit?: number;
    defaultCredits?: number;
    operationalMode?: string;
    externalMirrorPlaylistId?: string | null;
    styleSourcePlaylists?: Record<string, string> | null;
    styleSequentialExtractionPrefs?: Record<string, boolean> | null;
    isWatchHistorySyncEnabled?: boolean;
    watchHistorySyncIntervalSeconds?: number;
    activeYoutubePlaylistId?: string | null;
    playlistSubmitterUsernames?: string[];
}

/**
 * Updates admin settings.
 */
export const updateAdminSettings = async (data: UpdateAdminSettingsData): Promise<ApiResponse<AdminSettingsData>> => {
    return fetchApi<AdminSettingsData>('/api/settings', {
        method: 'PUT',
        body: JSON.stringify(data),
    }, 'updateAdminSettings');
};

// --- YouTube Search API ---
export const searchYouTube = async (query: string): Promise<ApiResponse> => {
  const url = `/api/youtube/search?q=${encodeURIComponent(query)}`;
  return fetchApi(url, { method: 'GET' }, 'searchYouTube');
};

// --- Suggestion Submission ---

interface SuggestionPayload {
  youtubeVideoId: string;
  title: string;
  channelTitle?: string;
  thumbnailUrl?: string;
  danceStyle: string;
}

/**
 * Submits a new suggestion.
 */
export const submitSuggestion = async (payload: SuggestionPayload): Promise<ApiResponse> => {
  return fetchApi('/api/suggestions', { method: 'POST', body: JSON.stringify(payload) }, 'submitSuggestion');
};

// --- Playlist Suggestion Submission ---
interface PlaylistSuggestionPayload {
  youtubePlaylistUrl: string;
  danceStyle: string;
}

export const submitPlaylistSuggestion = async (payload: PlaylistSuggestionPayload): Promise<ApiResponse> => {
  return fetchApi(
    '/api/suggestions/playlist',
    { 
      method: 'POST', 
      body: JSON.stringify(payload) 
    },
    'submitPlaylistSuggestion'
  );
};

// --- User & Public Data API Functions ---
export const getCurrentUser = async (): Promise<ApiResponse> => {
  return fetchApi('/api/users/me', { method: 'GET' }, 'getCurrentUser');
};

export const getPublicSuggestions = async (options?: { filterBy?: string; sortBy?: string; danceStyle?: string }): Promise<ApiResponse> => {
  const params = new URLSearchParams();
  if (options?.filterBy) params.append('filterBy', options.filterBy);
  if (options?.sortBy) params.append('sortBy', options.sortBy);
  if (options?.danceStyle) params.append('danceStyle', options.danceStyle);
  const url = `/api/suggestions/public?${params.toString()}`;
  return fetchApi(url, { method: 'GET' }, 'getPublicSuggestions');
}

/**
 * Fetches all approved suggestions.
 */
export const getApprovedSuggestions = async (
  danceStyle?: string,
  page: number = 1, 
  limit: number = 50,
  chartType?: string
): Promise<ApiResponse> => {
  const params = new URLSearchParams();
  
  if (danceStyle && danceStyle !== 'all') {
    params.append('danceStyle', danceStyle);
  }
  
  params.append('page', page.toString());
  params.append('limit', limit.toString());
  
  if (chartType) {
    params.append('chartType', chartType);
  }
  
  const queryString = params.toString() ? `?${params.toString()}` : '';
  
  // No try/catch here to avoid duplicate declarations - let fetchApi handle errors
  return fetchApi(`/api/suggestions/approved${queryString}`, { 
    method: 'GET',
    // Include auth headers if available, but endpoint allows anonymous access 
  }, 'getApprovedSuggestions');
};

export const voteSuggestion = async (suggestionId: string): Promise<ApiResponse<any>> => {
  return fetchApi(`/api/suggestions/${suggestionId}/vote`, { method: 'POST' }, 'voteSuggestion');
};

export const unvoteSuggestion = async (suggestionId: string): Promise<ApiResponse<{ message: string }>> => {
  return fetchApi(`/api/suggestions/${suggestionId}/vote`, { method: 'DELETE' }, 'unvoteSuggestion');
}

export const getDanceStyles = async (): Promise<ApiResponse<string[]>> => {
  return fetchApi<string[]>('/api/config/dance-styles', { method: 'GET' }, 'getDanceStyles');
};

export const getActiveTemplateRotation = async (): Promise<ApiResponse<{ styleRotation: { style: string; count: number }[] | null }>> => {
  return fetchApi('/api/playlists/active-template/rotation', { method: 'GET' }, 'getActiveTemplateRotation');
}

export const getUserDisplayProfile = async (): Promise<ApiResponse> => {
  return fetchApi('/api/users/me/profile', { method: 'GET' }, 'getUserDisplayProfile');
};

export const getUserCoreInfo = async (): Promise<ApiResponse> => {
  // Assumes an endpoint like /api/users/core-info or similar exists
  return fetchApi('/api/users/core', { method: 'GET' }, 'getUserCoreInfo');
};

interface UpdateProfilePayload {
    displayName?: string;
}

/**
 * Updates the current user's profile (e.g., display name).
 * Avatar update is handled separately.
 */
export const updateUserProfile = async (payload: UpdateProfilePayload): Promise<ApiResponse> => {
  return fetchApi('/api/profile', { method: 'PATCH', body: JSON.stringify(payload) }, 'updateUserProfile');
};


export const getLeaderboard = async (limit: number = 10): Promise<ApiResponse> => {
  return fetchApi(`/api/users/leaderboard?limit=${limit}`, { method: 'GET' }, 'getLeaderboard');
};

export const getTopVotedUsers = async (
  limit: number = 10, 
  timeframe: 'week' | 'month' | 'all' | 'alltime' = 'all'
): Promise<ApiResponse> => {
  // Map 'alltime' to 'all' for backward compatibility with backend
  const backendTimeframe = timeframe === 'alltime' ? 'all' : timeframe;
  return fetchApi(`/api/users/top-voted?limit=${limit}&timeframe=${backendTimeframe}`, { method: 'GET' }, 'getTopVotedUsers');
};

// Achievement-related functions - commented out or removed
/*
export const getMyAchievements = async (): Promise<ApiResponse> => {
  return fetchApi('/api/achievements/my', { method: 'GET' }, 'getMyAchievements');
}

export const getUserAchievements = async (userId: string): Promise<ApiResponse> => {
  return fetchApi(`/api/users/${userId}/achievements`, { method: 'GET' }, 'getUserAchievements');
};
*/

// --- Playlist Templates API Functions ---
export const getPlaylistTemplates = async (): Promise<ApiResponse> => {
  return fetchApi('/api/playlists/templates', { method: 'GET' }, 'getPlaylistTemplates');
};

/**
 * Creates a new playlist template.
 * @param name - The name of the template.
 * @param config - The configuration object for the template.
 */
export const createPlaylistTemplate = async (name: string, config: any): Promise<ApiResponse> => {
  return fetchApi('/api/playlists/templates', { method: 'POST', body: JSON.stringify({ name, config }) }, 'createPlaylistTemplate');
};

/**
 * Updates an existing playlist template.
 * @param templateId - The ID of the template to update.
 * @param name - The new name of the template.
 * @param config - The new configuration object for the template.
 */
export const updatePlaylistTemplate = async (templateId: string, name: string, config: any): Promise<ApiResponse> => {
  return fetchApi(`/api/playlists/templates/${templateId}`, { method: 'PUT', body: JSON.stringify({ name, config }) }, 'updatePlaylistTemplate');
};

// --- Suggestion Comments API Functions ---
export const getSuggestionComments = async (suggestionId: string): Promise<ApiResponse> => {
  return fetchApi(`/api/suggestions/${suggestionId}/comments`, { method: 'GET' }, 'getSuggestionComments');
};

export const addSuggestionComment = async (suggestionId: string, content: string): Promise<ApiResponse> => {
  return fetchApi(`/api/suggestions/${suggestionId}/comments`, { method: 'POST', body: JSON.stringify({ content }) }, 'addSuggestionComment');
};

// --- Undo Suggestion Actions ---
export const undoRejectSuggestion = async (suggestionId: string): Promise<ApiResponse> => {
  return fetchApi(`/api/suggestions/${suggestionId}/undo-reject`, { method: 'POST' }, 'undoRejectSuggestion');
};

export const undoApproveSuggestion = async (suggestionId: string): Promise<ApiResponse> => {
  return fetchApi(`/api/suggestions/${suggestionId}/undo-approve`, { method: 'POST' }, 'undoApproveSuggestion');
};

// --- Playlist Template Activation ---
export const activatePlaylistTemplate = async (templateId: string): Promise<ApiResponse> => {
  return fetchApi(`/api/playlists/templates/${templateId}/activate`, { method: 'POST' }, 'activatePlaylistTemplate');
};


// --- User Management API Functions (Admin) ---
export const searchUsers = async (query: string): Promise<ApiResponse<UserSearchResult[]>> => {
  const url = `/api/users/search/admin?q=${encodeURIComponent(query)}`; // <--- NEW LINE
  return fetchApi<UserSearchResult[]>(url, { method: 'GET' }, 'searchUsers');
};

interface CreditUpdatePayload {
    amount: number;
    action: 'set' | 'add' | 'remove';
    reason?: string; // Optional reason for logging
}

/**
 * Updates credits for a specific user.
 */
export const updateUserCredits = async (userId: string, payload: CreditUpdatePayload): Promise<ApiResponse<{ id: string; credits: number }>> => {
    return fetchApi<{ id: string; credits: number }>(
        `/api/users/${userId}/credits`,
        { method: 'POST', body: JSON.stringify(payload) },
        'updateUserCredits'
    );
};

// --- Playback Control (Admin) ---
export const startPlayback = async (): Promise<ApiResponse> => {
  return fetchApi('/api/playback/start', { method: 'POST' }, 'startPlayback');
};

export const stopPlayback = async (): Promise<ApiResponse> => {
  return fetchApi('/api/playback/stop', { method: 'POST' }, 'stopPlayback');
};

// --- Sync Playback State (Optional, depends on implementation) ---
export const syncPlayback = async (): Promise<ApiResponse> => {
  return fetchApi('/api/playback/sync', { method: 'GET' }, 'syncPlayback');
};

// --- Source Playlist Management (Admin) ---
export const getSuggestionPlaylists = async (): Promise<ApiResponse> => {
  return fetchApi('/api/settings/suggestion-playlists', { method: 'GET' }, 'getSuggestionPlaylists');
};

export const updateSuggestionPlaylists = async (stylePlaylistsMap: Record<string, string>): Promise<ApiResponse> => {
    return fetchApi('/api/settings/suggestion-playlists', {
        method: 'PUT',
        body: JSON.stringify({ stylePlaylistsMap }),
    }, 'updateSuggestionPlaylists');
};

// --- Utility: Clear YouTube Playlist (Admin) ---
export const clearYoutubePlaylist = async (playlistId: string, password: string): Promise<ApiResponse> => {
    return fetchApi('/api/youtube/clear-playlist', {
        method: 'POST',
        body: JSON.stringify({ playlistId, password }),
    }, 'clearYoutubePlaylist');
};

// --- Authentication --- 
// NOTE: Login/Logout might need special handling (e.g., storing token) 
// and might not fit the standard `fetchApi` pattern perfectly.
// Keep existing auth functions for now, review later if needed.

export const login = async (credentials: any): Promise<any> => {
  try {
    // Don't log the credentials
    logInfo('API Call: login', { url: '/api/auth/login' });
    
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
      credentials: 'include', // This ensures cookies are sent with the request
    });

    // Do NOT log the actual credentials or token response
    const data = await response.json();
    
    // Only log success or failure, not the actual data
    if (response.ok) {
      logInfo('API Success: login', { status: response.status });
    } else {
      logError('API Error: login failed', { status: response.status });
    }
    
    return data;
  } catch (error) {
    logError('API Error: login', error);
    throw error;
  }
};

export const logout = async (): Promise<void> => {
  logInfo('API Call: logout');
  try {
    // Call backend logout endpoint to invalidate session/token server-side
    await fetch('/api/auth/logout', { 
      method: 'POST', 
      headers: getAuthHeaders(),
      credentials: 'include'  // Add this to ensure cookies are sent
    }); 
    localStorage.removeItem('token');
    localStorage.removeItem('authToken');
    localStorage.removeItem('authUser');
    logInfo('Token removed from localStorage and server session invalidated');
  } catch (error) {
    logError('API Exception: logout', error);
    // Still clear local storage even if server call fails
    localStorage.removeItem('token');
    localStorage.removeItem('authToken');
    localStorage.removeItem('authUser');
  }
};

export const sendFrontendLog = async (logEntry: any): Promise<void> => {
  try {
    // Don't await this, just fire and forget
    fetch('/api/auth/logs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
         // No auth needed for logging endpoint
      },
      body: JSON.stringify(logEntry),
      keepalive: true // Attempt to keep request going even if page unloads
    });
  } catch (error) {
    // console.error('[Logger] Failed to send log to backend:', error);
    // Avoid infinite loop if logging itself fails
  }
};

/**
 * Fetches the history of generated playlists
 * @param limit Optional limit on number of history items to retrieve (default 10)
 */
export const getPlaylistHistory = async (limit: number = 10): Promise<ApiResponse<any[]>> => {
  return fetchApi(`/api/playlists/history?limit=${limit}`, { method: 'GET' }, 'getPlaylistHistory');
};

/**
 * Deletes a playlist from both history and YouTube
 * @param historyId The ID of the playlist history record
 */
export const deletePlaylistHistory = async (historyId: string): Promise<ApiResponse> => {
  return fetchApi(`/api/playlists/history/${historyId}`, { method: 'DELETE' }, 'deletePlaylistHistory');
};

/**
 * Sets a saved playlist as the active public playlist
 * @param historyId The ID of the playlist history record
 */
export const setPlaylistAsActive = async (historyId: string): Promise<ApiResponse<{playlistId: string, operationalMode: string, modeChanged: boolean}>> => {
  return fetchApi(`/api/playlists/history/${historyId}/activate`, { method: 'PATCH' }, 'setPlaylistAsActive');
};

/**
 * Uploads an avatar file for the currently logged-in user.
 */
export const uploadAvatar = async (file: File): Promise<ApiResponse<{ avatarUrl: string | null }>> => {
    const formData = new FormData();
    formData.append('avatar', file); // Backend expects 'avatar' field name

    // Create custom fetch for FormData to avoid Content-Type setting
    try {
        logInfo('API Call: uploadAvatar', { method: 'POST', url: '/api/users/me/avatar', fileName: file.name });
        
        // Get token for authorization
        const token = getAuthToken();
        // Create headers without Content-Type
        const headers: HeadersInit = {};
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        
        const response = await fetch('/api/users/me/avatar', {
            method: 'POST',
            headers,
            body: formData,
            credentials: 'include'
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            logError('API Error: uploadAvatar failed', { status: response.status, response: data });
            return { 
                success: false, 
                message: data.error || `Upload failed with status: ${response.status}`,
                status: response.status 
            };
        }
        
        logInfo('API Success: uploadAvatar', { status: response.status });
        return { success: true, data: data.data };
    } catch (error: any) {
        logError('API Exception: uploadAvatar', error);
        return { 
            success: false, 
            message: error.message || 'An unexpected error occurred during avatar upload'
        };
    }
};

// --- Leaderboard API ---
// ... existing code ... 

// User profile functions
/**
 * Get a user's profile by userId
 */
export const getUserProfile = async (userId: string) => {
  try {
    const response = await fetch(`/api/users/${userId}/profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });
    
    return await handleApiResponse(response);
  } catch (error: any) {
    logError('API: getUserProfile failed', error);
    return { success: false, message: error.message || 'Failed to fetch user profile' };
  }
};

/**
 * Get all suggestions by a specific user
 */
export const getUserSuggestions = async (userId: string) => {
  try {
    const response = await fetch(`/api/users/${userId}/suggestions`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });
    
    return await handleApiResponse(response);
  } catch (error: any) {
    logError('API: getUserSuggestions failed', error);
    return { success: false, message: error.message || 'Failed to fetch user suggestions' };
  }
};

// Helper function to handle API responses
const handleApiResponse = async (response: Response) => {
  const data = await response.json();
  if (!response.ok) {
    return { success: false, message: data.message || 'API request failed' };
  }
  return data;
};

// Radio API endpoints
export const getRadioChannels = async () => {
  try {
    const response = await fetch('/api/radio/channels');
    if (!response.ok) {
      throw new Error(`Failed to get radio channels: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching radio channels:', error);
    throw error;
  }
};

const addCacheBuster = (url: string): string => {
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}_cb=${new Date().getTime()}`;
};

export const getSongsForChannel = async (channelId: string) => {
  logInfo('[api.ts] getSongsForChannel: Received channelId:', channelId);
  const baseUrl = `/api/radio/songs/${channelId}`;
  logInfo('[api.ts] getSongsForChannel: URL before cache buster:', baseUrl);
  const urlWithCb = addCacheBuster(baseUrl);
  logInfo('[api.ts] getSongsForChannel: URL after cache buster (sent to fetchApi):', urlWithCb);
  return fetchApi(urlWithCb, { method: 'GET' }, 'getSongsForChannel');
};

export const likeSong = async (songId: string): Promise<ApiResponse<any>> => {
  logInfo(`Liking song via radio specific endpoint: ${songId}`);
  return fetchApi(`/api/radio/like/${songId}`, { method: 'POST' }, 'radioLikeSong');
};

export const getPopularSongs = async (channelId: string, limit = 10) => {
  try {
    const response = await fetch(`/api/radio/popular/${channelId}?limit=${limit}`);
    if (!response.ok) {
      throw new Error(`Failed to get popular songs for channel ${channelId}: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`Error fetching popular songs for channel ${channelId}:`, error);
    throw error;
  }
};

export const getUserVotedSongs = async (): Promise<ApiResponse> => {
  return fetchApi('/api/suggestions/voted', { method: 'GET' }, 'getUserVotedSongs');
};

// --- Favorite Dancer API Functions ---

export const getMyFavoriteDancers = async (): Promise<ApiResponse<any[]>> => {
  return fetchApi<any[]>('/api/users/me/favorites', { method: 'GET' }, 'getMyFavoriteDancers');
};

export const addFavoriteDancer = async (userId: string): Promise<ApiResponse> => {
  return fetchApi(`/api/users/${userId}/favorite`, { method: 'POST' }, 'addFavoriteDancer');
};

export const removeFavoriteDancer = async (userId: string): Promise<ApiResponse> => {
  return fetchApi(`/api/users/${userId}/favorite`, { method: 'DELETE' }, 'removeFavoriteDancer');
};

export const getSuggestionsByUsers = async (userIds: string[], limit: number = 5): Promise<ApiResponse<any[]>> => {
  if (!userIds || userIds.length === 0) {
    return { success: true, data: [] }; // Return empty if no IDs provided
  }
  const params = new URLSearchParams({
    userIds: userIds.join(','),
    limit: limit.toString()
  });
  return fetchApi<any[]>(`/api/suggestions/by-users?${params.toString()}`, { method: 'GET' }, 'getSuggestionsByUsers');
};

export const searchApprovedSongs = async (query: string): Promise<Song[]> => {
  const url = `/api/suggestions/search?query=${encodeURIComponent(query)}`;
  const response = await fetchApi(url, { method: 'GET' }, 'searchApprovedSongs');
  if (!response.success) {
    throw new Error(response.message || 'Failed to search songs');
  }
  return response.data;
};

// --- Chart API Functions ---

/**
 * Fetches chart data by chart type and style with pagination support.
 * @param chartType The type of chart (trending, weekly, monthly, top)
 * @param style The dance style, or 'all' for all styles
 * @param page The page number (optional, defaults to 1)
 * @param limit The number of items per page (optional, defaults to 25)
 */
export const getChartSongs = async (
  chartType: string,
  style: string,
  page: number = 1,
  limit: number = 25
): Promise<ApiResponse> => {
  try {
    // Sanitize and validate inputs
    const cleanStyle = style?.trim()?.toLowerCase() || 'all';
    const cleanChartType = chartType?.trim()?.toLowerCase() || 'trending';
    const pageNum = Math.max(1, page); // Ensure page is at least 1
    const limitNum = Math.max(1, Math.min(50, limit)); // Limit between 1-50
    
    // Build the API URL
    const url = `/api/suggestions/charts/${cleanChartType}/${cleanStyle}?page=${pageNum}&limit=${limitNum}`;
    
    logInfo('Requesting chart songs', { chartType: cleanChartType, style: cleanStyle, page: pageNum, limit: limitNum, url });
    
    // Make the API request
    return fetchApi(url, { method: 'GET' }, 'getChartSongs');
  } catch (error: any) {
    logError('Chart songs request failed', error);
    return {
      success: false,
      message: error.message || 'Failed to fetch chart data',
      data: { data: [], pagination: { currentPage: page, totalPages: 0, totalCount: 0, limit } }
    };
  }
};

/**
 * Shares a song by copying its URL to clipboard
 * @param songId The ID of the song to share
 * @param videoId The YouTube video ID
 * @returns Object with success status and URL
 */
export const shareSong = async (songId: string, videoId: string): Promise<{success: boolean, url: string}> => {
  try {
    // Create both a YouTube direct link and an app link if needed
    const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`;
    // You could also create a shareable URL within your app like:
    // const appUrl = `${window.location.origin}/song/${songId}`;
    
    // Use the YouTube URL for now
    await navigator.clipboard.writeText(youtubeUrl);
    return { success: true, url: youtubeUrl };
  } catch (error) {
    console.error('Failed to copy URL:', error);
    return { success: false, url: `https://www.youtube.com/watch?v=${videoId}` };
  }
}; 