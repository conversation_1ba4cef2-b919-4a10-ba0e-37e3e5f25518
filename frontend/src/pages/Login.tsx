import React, { useState, FormEvent, ChangeEvent, useEffect } from 'react';
// Ensure useNavigate is imported
import { useNavigate, Link, useLocation } from 'react-router-dom'; 
import { logInfo, logError } from '../utils/logger';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { Label } from '../components/ui/Label';
import SubmitButtonWithLoading from '../components/ui/SubmitButtonWithLoading'; // Added import
// Remove useAuth hook import
// import { useAuth } from '../hooks/useAuth'; 
import { useAuthContext } from '../context/AuthContext'; // Import the context hook

// Define the expected response structure for a successful login
interface LoginResponse {
  token: string;
  role: 'ADMIN' | 'DANCER'; // Be specific about roles if possible
  userId: string;
  username: string;
  // Removed credits field since credits system was removed
}

const Login = () => {
  // Ensure useNavigate hook is called at the top level
  const navigate = useNavigate(); 
  const location = useLocation(); // Added to get query params
  const { login } = useAuthContext(); // Get login from the context hook
  const [usernameInput, setUsernameInput] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const authError = queryParams.get('error');
    if (authError) {
      if (authError === 'google_auth_failed') {
        setError('Google authentication failed. Please try again or use another method.');
      } else if (authError === 'authentication_failed') {
        setError('Authentication failed. Please check your credentials or try again.');
      } else if (authError === 'server_error') {
        setError('An unexpected server error occurred. Please try again later.');
      }
      // Optional: remove the error from URL so it doesn't persist on refresh without re-triggering
      // navigate(location.pathname, { replace: true });
    }
  }, [location, navigate]);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    logInfo('Login attempt', { username: usernameInput }); // Only log username, not password
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username: usernameInput, password }),
        credentials: 'include', // Ensure cookies are sent/received
      });

      if (!response.ok) {
        let errorMessage = 'Invalid credentials';
        try {
            const errorData = await response.json();
            errorMessage = errorData.message || errorData.error || errorMessage; // Check for errorData.error too
        } catch (parseError) {
            // Ignore parsing error, stick to default message
        }
        logError('Login failed', new Error(errorMessage), { username: usernameInput, status: response.status }); // Only log username, not password
        throw new Error(errorMessage);
      }

      // Explicitly type the response data
      const data = await response.json() as LoginResponse; 
      
      // Basic check to ensure data conforms (including credits)
      if (!data.token || !data.role || !data.userId || !data.username) {
        logError('Login error: Incomplete data received from backend', undefined, { responseData: data });
        setLoading(false);
        throw new Error('Login failed: Incomplete response received.');
      }
      
      logInfo('Login successful', { username: data.username, role: data.role });

      // Prepare user object for context (removed credits)
      const authUser = { 
        id: data.userId, 
        username: data.username, 
        isAdmin: data.role === 'ADMIN'
        // Removed credits field since credits system was removed
      };
      
      // Call the login function from the context
      login(data.token, authUser); 
      
      // Navigate based on role
      const targetPath = data.role === 'ADMIN' ? '/admin' : '/dancer';
      navigate(targetPath, { replace: true });

    } catch (err: any) {
      setError(err.message || 'Invalid username or password');
      logError('Login error', err, { username: usernameInput }); // Re-enabled
      setLoading(false);
    }
  };

  return (
    <main className="min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900">
      <div className="w-full max-w-md space-y-8 bg-white p-8 sm:p-10 rounded-2xl shadow-xl">
        <div className="text-center">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-2">
            Sign In
          </h1>
          <p className="text-md text-gray-600">
            Access your Party Playlist account.
          </p>
        </div>

        <form className="space-y-6" onSubmit={handleSubmit} noValidate>
          <div className="space-y-2">
            <label htmlFor="username" className="text-sm font-medium text-gray-700 block pb-1.5">
              Username
            </label>
            <input
              id="username"
              name="username"
              type="text"
              required
              minLength={3}
              placeholder="Enter your username"
              value={usernameInput}
              onChange={(e: ChangeEvent<HTMLInputElement>) => setUsernameInput(e.target.value)}
              autoComplete="username"
              className="block w-full h-11 px-4 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-base"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium text-gray-700 block pb-1.5">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              required
              minLength={3}
              placeholder="Enter your password"
              value={password}
              onChange={(e: ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
              autoComplete="current-password"
              className="block w-full h-11 px-4 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-base"
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm text-center font-medium bg-red-50 p-3 rounded-md" role="alert">
              {error}
            </div>
          )}

          <div>
            <SubmitButtonWithLoading
              isLoading={loading}
              text={loading ? 'Signing in...' : 'Sign in'}
            />
          </div>

          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center" aria-hidden="true">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Or continue with</span>
            </div>
          </div>

          <div>
            <a
              href="/api/auth/google" // This is the backend route that starts the Google OAuth flow
              className="w-full inline-flex justify-center items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
            >
              <svg className="w-5 h-5 mr-2 -ml-1" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="google" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 488 512"><path fill="currentColor" d="M488 261.8C488 403.3 381.7 512 244 512 110.5 512 0 398.8 0 256S110.5 0 244 0c71.8 0 130.2 27.7 174.2 68.5l-68.5 68.5c-24.3-22.7-56.8-36.7-95.7-36.7-70.3 0-129.3 57.3-129.3 128.3s59 128.3 129.3 128.3c56.5 0 99.3-24.5 116.5-41.2l72.5 72.5c-40.3 38.3-94 60-153.5 60C109.3 480 8 379.3 8 256s101.3-224 236-224c66.8 0 124.8 24.5 166.3 64.9l-67.5 67.5C353.7 141.8 302.3 128 244 128c-68.5 0-124.5 55.8-124.5 124.2s56 124.2 124.5 124.2c41.2 0 73.8-15.3 95.7-36.7l68.5 68.5c-24.3 22.7-56.8 36.7-95.7 36.7-70.3 0-129.3-57.3-129.3-128.3s59-128.3 129.3-128.3c56.5 0 99.3-24.5 116.5-41.2l72.5 72.5C439.8 209.3 412.3 239.8 381.7 261.8z"></path></svg>
              Sign in with Google
            </a>
          </div>

          {/* Facebook Sign-In Button (Initially Disabled) */}
          <div className="mt-3">
            <button
              type="button"
              // href="/api/auth/facebook" // Link to backend route when enabled
              disabled // Keep disabled for now
              onClick={() => { /* Maybe a toast/alert: 'Facebook login is coming soon!' */ }}
              className="w-full inline-flex justify-center items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-400 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200 cursor-not-allowed opacity-60 relative group"
            >
              <svg className="w-5 h-5 mr-2 -ml-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"/></svg>
              Sign in with Facebook
              <span className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                (Temporarily Disabled)
              </span>
            </button>
            {/* <p className="text-xs text-center text-gray-500 mt-1">(Currently disabled)</p> */}
          </div>

          <div className="text-sm text-center mt-6">
            <button
              type="button"
              onClick={() => navigate('/signup')}
              className="font-medium text-primary-600 hover:text-primary-800 transition-colors duration-200 bg-transparent border-none cursor-pointer p-1 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white"
            >
              Don't have an account? <span className="font-semibold">Sign Up</span>
            </button>
          </div>

          <div className="mt-6 border-t border-gray-200 pt-6 text-center space-y-3">
            <button
              type="button"
              className="w-full inline-flex justify-center items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
              onClick={() => navigate('/')}
            >
              Go to Welcome Page
            </button>
            <button
              type="button"
              className="w-full inline-flex justify-center items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
              onClick={() => window.open('/public-playlist', '_blank', 'noopener,noreferrer')}
            >
              Explore the Live Playlist!
            </button>
          </div>
        </form>
      </div>
    </main>
  );
};

export default Login; 