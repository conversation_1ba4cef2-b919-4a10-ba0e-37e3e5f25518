import React, { useEffect, useState, useRef, useCallback } from 'react';
import io, { Socket } from 'socket.io-client';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { logInfo, logError } from '../../utils/logger';
import { getDanceStyles, activatePlaylistTemplate, getPlaylistTemplates, getAdminSettings, updateAdminSettings } from '../../utils/api';
import { formatDistanceToNow } from 'date-fns';
import toast from 'react-hot-toast';
import GeneratedPlaylistHistory from '../../components/admin/GeneratedPlaylistHistory';

// --- Interfaces --- 
interface PlaylistTemplate {
  id: string;
  name: string;
  description?: string;
  styleRotation: any; // Keep as any for flexibility, or define specific structure
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Added AdminSettings interface
interface AdminSettings {
    styleSourcePlaylists?: { [key: string]: string } | null; // Allow null
    styleSequentialExtractionPrefs?: { [key: string]: boolean } | null; // Allow null
    // Add other relevant fields if needed for context
    isYouTubeConnected?: boolean;
}

// DnD Item Structure for UI (unrelated to API/DB models)
interface DndStyleItem {
  id: string;
  style: string;
  count: number;
}

const PlaylistManagement = () => {
  const [templates, setTemplates] = useState<PlaylistTemplate[]>([]);
  const [availableDanceStyles, setAvailableDanceStyles] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingStyles, setLoadingStyles] = useState(true);
  const [form, setForm] = useState({ name: '', description: '', styleRotation: '', isActive: false });
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isMerging, setIsMerging] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState<Record<string, boolean>>({});
  const [isActivating, setIsActivating] = useState<Record<string, boolean>>({});
  const [templateItems, setTemplateItems] = useState<DndStyleItem[]>([]);
  // --- Added State ---
  const [adminSettings, setAdminSettings] = useState<AdminSettings | null>(null);
  const [styleSourceUrls, setStyleSourceUrls] = useState<{ [key: string]: string }>({});
  const [styleSequentialExtraction, setStyleSequentialExtraction] = useState<{ [key: string]: boolean }>({});
  const [isSavingSources, setIsSavingSources] = useState(false);
  const [stylesToGenerate, setStylesToGenerate] = useState<string[]>([]);
  const [isGeneratingPlaylist, setIsGeneratingPlaylist] = useState(false);
  // --- End Added State ---
  // --- Added State for Generation Options ---
  const [maxSongsLimit, setMaxSongsLimit] = useState<number | string>(''); // Default empty
  // --- End Added State for Generation Options ---

  const socketRef = useRef<typeof Socket | null>(null);

  // Correct fetchInitialData to not depend on handlePlaylistUpdate
  const fetchInitialData = useCallback(async () => {
    setLoading(true);
    logInfo('PlaylistManagement API call: fetch initial playlists');
    try {
      const result = await getPlaylistTemplates();
      
      if (result.success && result.data) {
        setTemplates(result.data);
        logInfo('PlaylistManagement API response (initial)', { templates: result.data });
      } else {
        throw new Error(result.message || 'Failed to fetch playlist templates');
      }
    } catch (err: any) {
      toast.error(`Failed to load initial playlists: ${err.message}`);
      logError('PlaylistManagement API error (initial)', err);
      // Set empty state on failure to prevent potential issues
      setTemplates([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchDanceStyles = useCallback(async () => {
    setLoadingStyles(true);
    logInfo('PlaylistManagement: Fetching dance styles');
    const result = await getDanceStyles();
    if (result.success && Array.isArray(result.data)) {
        setAvailableDanceStyles(result.data);
        logInfo('PlaylistManagement: Dance styles fetched', { count: result.data.length });
    } else {
        logError('PlaylistManagement: Failed to fetch dance styles', { message: result.message });
        toast.error('Could not load available dance styles.');
        setAvailableDanceStyles([]);
    }
    setLoadingStyles(false);
  }, []);

  // Added fetchAdminSettings
  const fetchAdminSettings = useCallback(async () => {
    logInfo('PlaylistManagement: Fetching admin settings');
    try {
        const result = await getAdminSettings();
        if (result.success && result.data) {
            setAdminSettings(result.data);
            setStyleSourceUrls(result.data.styleSourcePlaylists || {}); // Initialize local state (handles null/undefined)
            setStyleSequentialExtraction(result.data.styleSequentialExtractionPrefs || {});
            logInfo('PlaylistManagement: Admin settings fetched', { settings: result.data });
        } else {
            throw new Error(result.message || 'Failed to fetch admin settings');
        }
    } catch (err: any) {
        toast.error(`Failed to load admin settings: ${err.message}`);
        logError('PlaylistManagement API error (admin settings)', err);
        setAdminSettings(null); // Clear on error
        setStyleSourceUrls({});
        setStyleSequentialExtraction({});
    }
  }, []);

  useEffect(() => {
    // Setup WebSocket connection
    socketRef.current = io();

    // Listen for events
    const socket = socketRef.current;
    
    // Listen for general updates - playlists, templates, etc
    socket.on('playlist:update', (data: any) => {
      logInfo('Socket event: playlist:update', data);
      
      // Update templates if provided
      if (data.templates) {
        setTemplates(data.templates);
      }

      // Remove activePlaylist handling
    });

    // Listen for sync results
    socket.on('playlist:synced', (data: any) => {
      logInfo('Socket event: playlist:synced', data);
      toast.success(data.message || 'Playlist sync complete!');
      
      // Remove discrepancies handling
    });

    // Listen for playback state updates for public display
    socket.on('player:state:update', (data: any) => {
      logInfo('Socket event: player:state:update', data);
      
      // Remove activePlaylist handling
    });

    // Cleanup function
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  // Add the fetchDanceStyles useEffect
  useEffect(() => {
    fetchInitialData();
    fetchDanceStyles();
    fetchAdminSettings(); // Fetch admin settings on mount
  }, [fetchInitialData, fetchDanceStyles, fetchAdminSettings]);

  // Added effect to pre-select dance styles that have source URLs defined
  useEffect(() => {
    if (adminSettings?.styleSourcePlaylists && Object.keys(adminSettings.styleSourcePlaylists).length > 0) {
      // Find styles that have URLs set
      const stylesWithUrls = Object.entries(adminSettings.styleSourcePlaylists)
        .filter(([style, url]) => !!url)
        .map(([style]) => style);
      
      if (stylesWithUrls.length > 0) {
        logInfo('Pre-selecting styles with defined URLs', { count: stylesWithUrls.length, styles: stylesWithUrls });
        setStylesToGenerate(stylesWithUrls);
      }
    }
  }, [adminSettings?.styleSourcePlaylists]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    logInfo('PlaylistManagement input changed', { field: e.target.name, value: e.target.value });
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    logInfo(editingId ? 'Update template attempt' : 'Create template attempt', form);
    try {
      let parsedStyleRotation;
      if (templateItems.length === 0) {
          toast.error('Template Rotation cannot be empty. Drag styles to the builder area.');
        setIsSaving(false);
        return;
      }
      parsedStyleRotation = templateItems.map(item => ({
        style: item.style,
        count: Math.max(1, item.count || 1)
      }));

      const body = {
        name: form.name,
        description: form.description,
        styleRotation: parsedStyleRotation,
        isActive: !!form.isActive
      };

      const url = editingId ? `/api/playlists/${editingId}` : '/api/playlists';
      const method = editingId ? 'PUT' : 'POST';

      logInfo(`PlaylistManagement API call: ${method} ${url}`, { body });

      const res = await fetch(url, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body)
      });

      logInfo('PlaylistManagement API response', { status: res.status, editingId });
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({ message: 'Failed to parse error response' }));
        logError(`Save template failed (${method})`, { status: res.status, response: errorData }, body);
        toast.error(`Failed to save playlist template: ${errorData.message || res.statusText}`);
        throw new Error('Failed to save playlist template');
      }

      setForm({ name: '', description: '', styleRotation: '', isActive: false });
      setEditingId(null);
      setTemplateItems([]); // Clear DND items on successful save/create
      toast.success(`Playlist template ${editingId ? 'updated' : 'created'} successfully.`);
      logInfo(`Save template success (${method})`, body);
      
      // Refetch the list after successful save/create
      fetchInitialData(); 

    } catch (err: any) {
      // Use the specific error message if available, otherwise generic message
      const errorMessage = err?.message || 'An unexpected error occurred while saving the playlist template.';
      toast.error(`Save failed: ${errorMessage}`, { id: 'save-error' });
      // Refined log message
      logError('Save template submission caught an error', err, form);
    } finally {
       setIsSaving(false);
    }
  };

  const handleEdit = (tpl: PlaylistTemplate) => {
    setEditingId(tpl.id);
    setForm({
      name: tpl.name,
      description: tpl.description || '',
      styleRotation: JSON.stringify(tpl.styleRotation, null, 2), // Keep for potential future raw edit?
      isActive: tpl.isActive
    });
    // Populate DND items from template's styleRotation
    if (Array.isArray(tpl.styleRotation)) {
        setTemplateItems(tpl.styleRotation.map((item, index) => ({ // Ensure unique IDs
            id: `tpl-item-${item.style}-${index}-${Date.now()}`,
            style: item.style,
            count: item.count || 1 // Default count to 1 if missing
        })));
    } else {
        setTemplateItems([]); // Clear if rotation data is invalid
    }
    logInfo('Start editing playlist template', tpl);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this playlist template?')) return;
    setIsDeleting(prev => ({ ...prev, [id]: true }));
    logInfo('Delete template attempt', { id });
    try {
      const res = await fetch(`/api/playlists/${id}`, {
        method: 'DELETE',
      });
      logInfo('PlaylistManagement API response (DELETE)', { status: res.status, id });
      if (!res.ok) {
         const errorData = await res.json().catch(() => ({ message: 'Failed to parse error response' }));
         logError('Delete template failed', { status: res.status, id, response: errorData });
         toast.error(`Failed to delete template: ${errorData.message || res.statusText}`);
      } else {
         toast.success('Playlist template deleted successfully.');
         logInfo('Delete template success', { id });
         // If deleting the template being edited, clear the form
         if (editingId === id) {
             setEditingId(null);
             setForm({ name: '', description: '', styleRotation: '', isActive: false });
             setTemplateItems([]);
         }
         // Refetch the template list after successful deletion
         fetchInitialData(); 
      }
    } catch (err: any) {
       toast.error('An unexpected error occurred while deleting the template.');
       logError('Delete template error (catch)', err, { id });
    } finally {
       setIsDeleting(prev => ({ ...prev, [id]: false }));
    }
  };

  const formatDateTime = (dateString: string | null | undefined) => {
    if (!dateString) return 'Never';
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (e) {
      return dateString; // Fallback to raw string
    }
  };

  const onDragEnd = (result: DropResult) => {
    const { source, destination } = result;
    logInfo('DND: onDragEnd', { source, destination });

    if (!destination) {
      return;
    }

    if (source.droppableId === 'availableStyles' && destination.droppableId === 'templateList') {
      const styleToAdd = availableDanceStyles[source.index];
      const newItem: DndStyleItem = {
        id: `tpl-${styleToAdd}-${Date.now()}`, // Ensure unique ID
        style: styleToAdd,
        count: 1
      };
      const newTemplateItems = Array.from(templateItems);
      newTemplateItems.splice(destination.index, 0, newItem);
      setTemplateItems(newTemplateItems);
      logInfo('DND: Added style to template', { style: styleToAdd, index: destination.index });
    }
    else if (source.droppableId === 'templateList' && destination.droppableId === 'templateList') {
      if (source.index === destination.index) {
        return;
      }
      const items = Array.from(templateItems);
      const [reorderedItem] = items.splice(source.index, 1);
      items.splice(destination.index, 0, reorderedItem);
      setTemplateItems(items);
      logInfo('DND: Reordered styles in template', { fromIndex: source.index, toIndex: destination.index });
    }
  };

  // Map of dance styles to specific HSL colors
  const styleColorMap: { [key: string]: string } = {
    'Bachata': 'hsl(100, 70%, 85%)', 
    'Salsa': 'hsl(180, 70%, 80%)', 
    'Kizomba': 'hsl(60, 85%, 80%)', 
    'Zouk': 'hsl(310, 70%, 88%)', 
    'Cha Cha': 'hsl(20, 85%, 85%)'
  };

  const getStyleColor = (style: string): string => {
      // Check if the style exists in the explicit map
      if (styleColorMap[style]) {
          return styleColorMap[style];
      }

      // Fallback for styles not in the map (generates a consistent color based on hash)
      let hash = 0;
      for (let i = 0; i < style.length; i++) {
          hash = style.charCodeAt(i) + ((hash << 5) - hash);
          hash = hash & hash; // Convert to 32bit integer
      }
      const hue = (hash * 137.5) % 360;
      // Use slightly different fallback saturation/lightness than map examples for distinction
      const fallbackColor = `hsl(${hue < 0 ? hue + 360 : hue}, 70%, 85%)`; 
      return fallbackColor;
  };

  const handleActivateTemplate = async (id: string) => {
    setIsSaving(true);
    logInfo('Activate template attempt', { id });
    try {
      const res = await fetch(`/api/playlists/${id}/activate`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      logInfo('PlaylistManagement API response (Activate)', { status: res.status, id });

      if (!res.ok) {
        throw new Error(res.statusText || 'Failed to activate template');
      }

      toast.success('Template activated successfully!');
      logInfo('Activate template success', { id });
    } catch (err: any) {
      toast.error(`Activation failed: ${err.message}`);
      logError('Activate template error', err, { id });
    } finally {
      setIsSaving(false);
    }
  };

  // --- Added Handlers ---
  const handleSourceUrlChange = (style: string, url: string) => {
    setStyleSourceUrls(prev => ({ ...prev, [style]: url }));
    logInfo('PlaylistManagement: Source URL changed', { style, url });
  };

  const handleSequentialExtractionChange = (style: string, isSequential: boolean) => {
    setStyleSequentialExtraction(prev => ({ ...prev, [style]: isSequential }));
    logInfo('PlaylistManagement: Sequential extraction preference changed', { style, isSequential });
  };

  const handleSaveSourceUrls = async () => {
    setIsSavingSources(true);
    logInfo('PlaylistManagement: Saving style source URLs and extraction preferences attempt', { 
      sources: styleSourceUrls,
      sequentialPrefs: styleSequentialExtraction
    });

    // Basic URL validation (simple check) - enhance on backend
    const invalidUrls = Object.entries(styleSourceUrls).filter(([style, url]) => url && !url.startsWith('https://www.youtube.com/playlist?list='));
    if (invalidUrls.length > 0) {
        toast.error(`Invalid YouTube Playlist URL format for: ${invalidUrls.map(([style]) => style).join(', ')}. Must start with https://www.youtube.com/playlist?list=`);
        setIsSavingSources(false);
        return;
    }

    try {
        const result = await updateAdminSettings({ 
          styleSourcePlaylists: styleSourceUrls,
          styleSequentialExtractionPrefs: styleSequentialExtraction
        });
        if (result.success) {
            toast.success('Dance style source URLs and extraction preferences saved successfully!');
            setAdminSettings(prev => ({ 
              ...prev, 
              styleSourcePlaylists: styleSourceUrls, 
              styleSequentialExtractionPrefs: styleSequentialExtraction
            })); // Update main settings state too
            logInfo('PlaylistManagement: Style source URLs and extraction preferences saved successfully');
        } else {
            throw new Error(result.message || 'Failed to save source URLs and extraction preferences');
        }
    } catch (err: any) {
        toast.error(`Failed to save settings: ${err.message}`);
        logError('PlaylistManagement API error (save settings)', err, { styleSourceUrls, styleSequentialExtraction });
    } finally {
        setIsSavingSources(false);
    }
  };

  const handleStyleSelectionChange = (style: string, isSelected: boolean) => {
    setStylesToGenerate(prev =>
      isSelected ? [...prev, style] : prev.filter(s => s !== style)
    );
  };

  const handleGeneratePartyPlaylist = async () => {
    if (stylesToGenerate.length === 0) {
        toast.error('Please select at least one dance style to include in the party playlist.');
        return;
    }
    const maxSongs = typeof maxSongsLimit === 'number' && maxSongsLimit > 0 ? maxSongsLimit : null;
    setIsGeneratingPlaylist(true);
    logInfo('PlaylistManagement: Generate party playlist attempt', { styles: stylesToGenerate, maxSongs });
    try {
        const res = await fetch('/api/playlists/party-playlist/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                styles: stylesToGenerate, 
                maxSongs: maxSongs // Send null if not set or invalid
            }),
        });

        logInfo('PlaylistManagement API response (Generate Party Playlist)', { status: res.status });

        if (!res.ok) {
            const errorData = await res.json().catch(() => ({ message: 'Failed to parse error response' }));
            logError('Generate party playlist failed', { status: res.status, response: errorData }, { styles: stylesToGenerate, maxSongs });
            toast.error(`Failed to generate party playlist: ${errorData.message || res.statusText}`);
            throw new Error('Failed to generate party playlist');
        }

        const resultData = await res.json();
        if (resultData.success && resultData.data?.playlistUrl) {
            toast.success(
                <span>
                    Party playlist generated successfully!{' '}
                    <a href={resultData.data.playlistUrl} target="_blank" rel="noopener noreferrer" className="underline text-blue-600 hover:text-blue-800">
                        View on YouTube
                    </a>
                </span>,
                { duration: 10000 } // Keep toast longer
            );
             logInfo('Generate party playlist success', { url: resultData.data.playlistUrl });
             setStylesToGenerate([]); // Clear selection after success
        } else {
             throw new Error(resultData.message || 'Playlist generated, but no URL returned.');
        }

    } catch (err: any) {
        toast.error(`Generation failed: ${err.message || 'An unexpected error occurred.'}`, { id: 'generate-error' });
        logError('Generate party playlist caught an error', err, { styles: stylesToGenerate, maxSongs });
    } finally {
        setIsGeneratingPlaylist(false);
    }
  };
  // --- End Added Handlers ---

  // --- RENDER --- 

  const isAnyActionLoading = isSaving || Object.values(isDeleting).some(v => v) || Object.values(isActivating).some(v => v);

  return (
    <div className="min-h-screen flex flex-col items-center bg-gradient-to-br from-gray-50 to-gray-100 py-6 sm:py-10 px-2 sm:px-4 md:px-6">
      <div className="w-full max-w-6xl space-y-8">
        <h2 className="text-3xl font-bold text-gray-800 text-center tracking-tight">Playlist Management</h2>

        {/* Template Form Section */}
        <form className="space-y-6 bg-white rounded-xl shadow-lg p-4 sm:p-6" onSubmit={handleSubmit} autoComplete="off">
           {/* ... (Rest of the form remains the same: Name, Description, DND Builder, Activate Checkbox, Save/Cancel buttons) ... */}
             <h3 className="text-xl font-semibold text-gray-700 border-b pb-3 mb-5">{editingId ? 'Edit' : 'Create'} Playlist Template</h3>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="name">Template Name</label>
            <input
              id="name"
              name="name"
              value={form.name}
              onChange={handleChange}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-3 py-2 min-h-[44px]"
              required
              minLength={2}
              aria-label="Playlist name"
              placeholder="e.g., Weekend Mix"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="description">Description (Optional)</label>
            <textarea
              id="description"
              name="description"
              value={form.description}
              onChange={handleChange}
              rows={2}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-3 py-2 min-h-[44px]"
              aria-label="Playlist description"
              placeholder="Describe the vibe or rotation..."
            />
          </div>
          
          <div className="border-t pt-6 mt-6">
            <h4 className="font-medium text-gray-800 mb-3">Style Rotation Builder</h4>
             <DragDropContext onDragEnd={onDragEnd}>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-1 bg-gray-50 p-3 rounded-lg border border-gray-200">
                        <label className="block text-sm font-medium text-gray-600 mb-2">Available Dance Styles</label>
                        <Droppable droppableId="availableStyles">
                        {(provided, snapshot) => (
                            <div
                            ref={provided.innerRef}
                            {...provided.droppableProps}
                            className={`p-2 border-2 ${snapshot.isDraggingOver ? 'border-blue-400 border-dashed' : 'border-gray-300'} rounded-md min-h-[250px] bg-white`}
                            >
                            {loadingStyles ? (
                                <p className="text-xs text-gray-400 italic p-2">Loading styles...</p>
                            ) : availableDanceStyles.length === 0 ? (
                                <p className="text-xs text-gray-400 italic p-2">No styles found.</p>
                            ) : (
                                availableDanceStyles.map((style, index) => (
                                <Draggable key={style} draggableId={`style-${style}`} index={index}>
                                    {(providedDraggable, snapshotDraggable) => (
                                    <div
                                        ref={providedDraggable.innerRef}
                                        {...providedDraggable.draggableProps}
                                        {...providedDraggable.dragHandleProps}
                                        className={`py-2 px-3 mb-2 rounded-md text-sm shadow transition-all cursor-grab ${snapshotDraggable.isDragging ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-sm'}`}
                                        style={{
                                            ...providedDraggable.draggableProps.style,
                                            backgroundColor: getStyleColor(style),
                                            opacity: snapshotDraggable.isDragging ? 0.9 : 1,
                                        }}
                                    >
                                        {style}
                                    </div>
                                    )}
                                </Draggable>
                                ))
                            )}
                            {provided.placeholder}
                            </div>
                        )}
                        </Droppable>
                        <p className="text-xs text-gray-500 mt-2">Drag styles to the template area.</p>
                    </div>

                    <div className="lg:col-span-2 bg-gradient-to-br from-green-50 to-emerald-50 p-3 rounded-lg border border-gray-200">
                        <label className="block text-sm font-medium text-gray-600 mb-2">Template Rotation (Drag & Drop Here)</label>
                        <Droppable droppableId="templateList">
                        {(provided, snapshot) => (
                            <div
                            ref={provided.innerRef}
                            {...provided.droppableProps}
                            className={`p-2 border-2 ${snapshot.isDraggingOver ? 'border-green-500 border-dashed bg-green-100' : 'border-gray-300'} rounded-md min-h-[250px] bg-white space-y-2`}
                            >
                            {templateItems.length === 0 ? (
                                <p className="text-sm text-gray-400 italic p-6 text-center">Drop styles here to build the rotation</p>
                            ) : (
                                templateItems.map((item, index) => (
                                <Draggable key={item.id} draggableId={item.id} index={index}>
                                    {(providedDraggable, snapshotDraggable) => (
                                    <div
                                        ref={providedDraggable.innerRef}
                                        {...providedDraggable.draggableProps}
                                        className={`flex items-center justify-between py-2 px-3 rounded-md shadow transition-all ${snapshotDraggable.isDragging ? 'ring-2 ring-green-500 shadow-lg' : 'shadow-sm'}`}
                                        style={{
                                            ...providedDraggable.draggableProps.style,
                                            backgroundColor: getStyleColor(item.style),
                                            opacity: snapshotDraggable.isDragging ? 0.9 : 1,
                                        }}
                                    >
                                        <div {...providedDraggable.dragHandleProps} className="flex items-center cursor-grab mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
                                            <span className="text-sm font-medium text-gray-800">{item.style}</span>
                                        </div>
                                        <div className="flex items-center">
                                            <label htmlFor={`count-${item.id}`} className="sr-only">Count for {item.style}</label>
                                            <input
                                                id={`count-${item.id}`}
                                                type="number"
                                                value={isNaN(item.count) ? '' : item.count}
                                                onChange={(e) => {
                                                    const rawValue = e.target.value;
                                                    const newCountParsed = parseInt(rawValue, 10); 
                                                    const newItems = [...templateItems];
                                                    newItems[index] = { ...item, count: newCountParsed }; 
                                                    setTemplateItems(newItems);
                                                }}
                                                onBlur={(e) => {
                                                    const currentCount = templateItems[index]?.count;
                                                    if (isNaN(currentCount) || currentCount < 1) {
                                                         const newItems = [...templateItems];
                                                         newItems[index] = { ...item, count: 1 };
                                                         setTemplateItems(newItems);
                                                         logInfo('DND Item Count validated on blur', { itemId: item.id, newCount: 1 });
                                                    }
                                                }}
                                                onClick={(e) => e.stopPropagation()}
                                                className="w-12 text-center border rounded bg-gray-700 border-gray-600 text-white ml-2 focus:outline-none focus:ring-1 focus:ring-indigo-500 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                            />
                                            <button 
                                                type="button"
                                                onClick={() => {
                                                    const newItems = Array.from(templateItems);
                                                    newItems.splice(index, 1);
                                                    setTemplateItems(newItems);
                                                    logInfo('DND: Removed item from template', { itemId: item.id });
                                                }}
                                                className="ml-2 p-1 text-red-500 rounded-full hover:bg-red-100 focus:outline-none focus:ring-1 focus:ring-red-400 focus:ring-offset-1"
                                                aria-label={`Remove ${item.style}`}
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                                                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    )}
                                </Draggable>
                                ))
                            )}
                            {provided.placeholder}
                            </div>
                        )}
                        </Droppable>
                        <p className="text-xs text-gray-500 mt-2">Drag items to reorder. Set song count per block. Click X to remove.</p>
                    </div>
                </div>
            </DragDropContext>
          </div>

          <div className="flex items-center justify-between border-t pt-5 mt-6">
             <div className="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              name="isActive"
              checked={!!form.isActive}
              onChange={e => {
                setForm({ ...form, isActive: e.target.checked });
                logInfo('PlaylistManagement input changed', { field: 'isActive', value: e.target.checked });
              }}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              aria-label="Set as active playlist template"
            />
                <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">Set as Active Template</label>
          </div>
             <div className="flex gap-3">
            {editingId && (
              <button
                type="button"
                        className="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 min-h-[44px]"
                onClick={() => {
                  setEditingId(null);
                  setForm({ name: '', description: '', styleRotation: '', isActive: false });
                            setTemplateItems([]);
                  logInfo('Cancel editing playlist template');
                }}
                aria-label="Cancel editing"
              >
                Cancel
              </button>
            )}
                <button
                    type="submit"
                    className="inline-flex justify-center py-2 px-5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 min-h-[44px] disabled:opacity-60"
                    aria-label={editingId ? 'Update playlist template' : 'Create playlist template'}
                    disabled={isSaving || loadingStyles}
                >
                    {isSaving ? 'Saving...' : (editingId ? 'Update Template' : 'Create Template')}
                </button>
             </div>
          </div>
        </form>

        {/* Existing Templates List */}
        <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6">
          <h3 className="text-xl font-semibold text-gray-700 border-b pb-2 mb-5">Existing Playlist Templates</h3>
          {loading ? (
            <div className="text-center p-6 text-gray-500 italic">Loading templates...</div>
          ) : templates.length === 0 ? (
            <div className="text-center text-gray-500 p-6 italic">No playlist templates found. Create one above!</div>
          ) : (
            <ul className="space-y-4">
              {templates.map(tpl => (
                <li key={tpl.id} className={`border rounded-lg p-4 transition-shadow hover:shadow-md ${tpl.isActive ? 'border-green-300 bg-green-50' : 'border-gray-200 bg-white'}`}>
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
                    <div className="flex-grow">
                        <div className="flex items-center gap-3 mb-1">
                            <span className="font-semibold text-lg text-gray-800">{tpl.name}</span>
                            {tpl.isActive && <span className="inline-block py-0.5 px-2.5 rounded-full text-xs font-medium bg-green-200 text-green-800 shadow-sm">Active</span>}
                        </div>
                        {tpl.description && <p className="text-sm text-gray-500 mb-2">{tpl.description}</p>}
                        <div className="flex flex-wrap items-center gap-1.5">
                          <span className="text-xs font-medium text-gray-500 mr-1">Rotation:</span>
                          {Array.isArray(tpl.styleRotation) && tpl.styleRotation.map((item, idx) => (
                            <span key={idx} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium leading-4" 
                                  style={{ backgroundColor: getStyleColor(item.style) }}>
                              {item.style} <span className="ml-1 font-normal text-gray-700 opacity-80">({item.count})</span>
                            </span>
                          ))}
                          {(!Array.isArray(tpl.styleRotation) || tpl.styleRotation.length === 0) && 
                            <span className="text-xs text-gray-400 italic">Empty rotation</span>}
                    </div>
                    </div>
                    <div className="flex-shrink-0 flex items-center gap-2 mt-2 sm:mt-0 self-end sm:self-center">
                       { !tpl.isActive && (
                            <button
                                className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-green-500 min-h-[40px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                onClick={() => handleActivateTemplate(tpl.id)}
                                aria-label={`Activate playlist template ${tpl.name}`}
                                disabled={isSaving || isActivating[tpl.id] || Object.values(isActivating).some(v => v)}
                            >
                                {isActivating[tpl.id] ? '...' : 'Activate'} {/* Simplified text, button only shows when !isActive */}
                            </button>
                       )}
                    <button
                        className="inline-flex items-center justify-center px-3 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-indigo-500 min-h-[40px] transition-colors disabled:opacity-50"
                      onClick={() => handleEdit(tpl)}
                      aria-label={`Edit playlist template ${tpl.name}`}
                        disabled={isSaving || isDeleting[tpl.id] || isActivating[tpl.id]}
                        > Edit
                    </button>
                    <button
                        className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-red-500 min-h-[40px] transition-colors disabled:opacity-50"
                      onClick={() => handleDelete(tpl.id)}
                      aria-label={`Delete playlist template ${tpl.name}`}
                        disabled={isSaving || isDeleting[tpl.id] || isActivating[tpl.id] || tpl.isActive}
                        title={tpl.isActive ? "Cannot delete the active template" : ""}
                        > {isDeleting[tpl.id] ? '...' : 'Delete'}
                    </button>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>

        {/* --- Added Manage Dance Style Sources Section --- */}
        <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6 space-y-5">
            <h3 className="text-xl font-semibold text-gray-700 border-b pb-2 mb-4">Manage Dance Style Sources</h3>
            <p className="text-sm text-gray-600">
                Provide YouTube Playlist URLs for each dance style. These playlists will be used as an additional source
                when generating the final party playlist, alongside approved user suggestions for that style.
                Select the styles you want to include in the next generated party playlist.
            </p>
            {loadingStyles || !adminSettings ? (
                <div className="text-center p-6 text-gray-500 italic">Loading styles and settings...</div>
            ) : availableDanceStyles.length === 0 ? (
                 <div className="text-center text-gray-500 p-6 italic">No dance styles configured.</div>
            ) : (
                <div className="space-y-4">
                    {availableDanceStyles.map(style => (
                        <div key={style} className="flex flex-col sm:flex-row items-start sm:items-center gap-3 border-b border-gray-100 pb-3 last:border-b-0 last:pb-0">
                            <div className="flex-shrink-0 w-full sm:w-auto flex items-center gap-3">
                                <input
                                    type="checkbox"
                                    id={`gen-style-${style}`}
                                    checked={stylesToGenerate.includes(style)}
                                    onChange={(e) => handleStyleSelectionChange(style, e.target.checked)}
                                    className="h-5 w-5 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                    disabled={isGeneratingPlaylist || isSavingSources}
                                />
                                <label htmlFor={`gen-style-${style}`} className="font-medium text-gray-700 min-w-[80px]">{style}</label>
                             </div>
                            <div className="flex-grow w-full">
                                <label htmlFor={`source-url-${style}`} className="sr-only">Source URL for {style}</label>
                                <input
                                    id={`source-url-${style}`}
                                    type="url"
                                    placeholder="Paste YouTube Playlist URL (https://www.youtube.com/playlist?list=...)"
                                    value={styleSourceUrls[style] || ''}
                                    onChange={(e) => handleSourceUrlChange(style, e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-3 py-2 min-h-[40px]"
                                    disabled={isGeneratingPlaylist || isSavingSources}
                                />
                                {styleSourceUrls[style] && (
                                    <div className="mt-2 flex items-center">
                                        <input
                                            type="checkbox"
                                            id={`sequential-${style}`}
                                            checked={!!styleSequentialExtraction[style]}
                                            onChange={(e) => handleSequentialExtractionChange(style, e.target.checked)}
                                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                            disabled={isGeneratingPlaylist || isSavingSources}
                                        />
                                        <label htmlFor={`sequential-${style}`} className="ml-2 text-sm text-gray-600">
                                            Extract sequentially from source (otherwise random)
                                        </label>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
             <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-4 border-t mt-5">
                 {/* --- Generation Options Re-added --- */}
                 <div className="flex flex-col sm:flex-row items-center gap-4 w-full sm:w-auto">
                     <div className="flex items-center gap-2 w-full sm:w-auto">
                         <label htmlFor="max-songs" className="text-sm font-medium text-gray-600 whitespace-nowrap">Max Songs:</label>
                         <input
                             id="max-songs"
                             type="number"
                             min="1"
                             step="1"
                             value={maxSongsLimit}
                             onChange={(e) => setMaxSongsLimit(e.target.value === '' ? '' : parseInt(e.target.value, 10))}
                             placeholder="(All)"
                             className="w-20 px-2 py-1 border border-gray-300 rounded-md shadow-sm sm:text-sm focus:ring-indigo-500 focus:border-indigo-500 min-h-[36px] [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                             disabled={isGeneratingPlaylist || isSavingSources}
                         />
                     </div>
                 </div>
                 <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                     <button
                         type="button"
                         onClick={handleSaveSourceUrls}
                         className="w-full sm:w-auto inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 min-h-[40px] disabled:opacity-60"
                         disabled={isSavingSources || isGeneratingPlaylist || loadingStyles || !adminSettings}
                         aria-label="Save all style source URLs"
                     >
                         {isSavingSources ? 'Saving URLs...' : 'Save Source URLs'}
                     </button>
                     <button
                         type="button"
                         onClick={handleGeneratePartyPlaylist}
                         className="w-full sm:w-auto inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 min-h-[40px] disabled:opacity-60"
                         disabled={isGeneratingPlaylist || isSavingSources || loadingStyles || !adminSettings || stylesToGenerate.length === 0 || !(adminSettings?.isYouTubeConnected)}
                         title={!(adminSettings?.isYouTubeConnected) ? "YouTube connection required" : stylesToGenerate.length === 0 ? "Select styles first" : ""}
                         aria-label="Generate party playlist from selected styles"
                     >
                         {isGeneratingPlaylist ? 'Generating...' : `Generate Party Playlist (${stylesToGenerate.length} styles)`}
                     </button>
                 </div>
             </div>
             {!(adminSettings?.isYouTubeConnected) && (
                 <p className="text-xs text-center text-red-600 mt-2">
                    YouTube account is not connected. Please connect it in the Admin Settings page to enable playlist generation.
                 </p>
             )}
        </div>
        {/* --- End Added Section --- */}

        {/* Add Generated Playlist History */}
        <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6 mt-6">
          <h3 className="text-xl font-semibold text-gray-700 border-b pb-2 mb-4">Generated Playlist History</h3>
          <p className="text-sm text-gray-600 mb-4">
            Previously generated YouTube playlists. Click "Open" to view any playlist on YouTube.
          </p>
          <GeneratedPlaylistHistory />
        </div>

      </div>
    </div>
  );
};

export default PlaylistManagement;