import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import io, { Socket } from 'socket.io-client';
import { logInfo, logError, logWarn } from '../../utils/logger';
import { 
  fetchAdminSuggestions, 
  approveSuggestion, 
  rejectSuggestion, 
  updateSuggestionLock, 
  undoRejectSuggestion,
  undoApproveSuggestion,
  getDanceStyles,
  updateSuggestionStyle
} from '../../utils/api';
import { LockClosedIcon, LockOpenIcon, ArrowUturnLeftIcon, CheckCircleIcon, XCircleIcon, ClockIcon, ChevronLeftIcon, ChevronRightIcon, MusicalNoteIcon } from '@heroicons/react/24/solid';
import toast from 'react-hot-toast';

interface Suggestion {
  id: string;
  user: { id: string; username: string };
  youtubeVideoId: string;
  title: string;
  channelTitle?: string;
  thumbnailUrl?: string;
  danceStyle: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  votes: number;
  isLocked: boolean;
  createdAt: string;
  updatedAt?: string;
}

type SortOption = 'newest' | 'oldest';
type StatusFilterOption = 'All' | 'PENDING' | 'APPROVED' | 'REJECTED';

const ITEMS_PER_PAGE = 20;

const SuggestionOversight = () => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [actionLoading, setActionLoading] = useState<Record<string, boolean>>({});
  const [availableDanceStyles, setAvailableDanceStyles] = useState<string[]>([]);
  const [selectedStyle, setSelectedStyle] = useState<string>('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<StatusFilterOption>('PENDING');
  const [sortBy, setSortBy] = useState<SortOption>('oldest');
  
  // New state for style change modal
  const [styleChangeModalOpen, setStyleChangeModalOpen] = useState(false);
  const [currentSuggestionId, setCurrentSuggestionId] = useState<string | null>(null);
  const [newDanceStyle, setNewDanceStyle] = useState<string>('');

  const socketRef = useRef<typeof Socket | null>(null);

  const fetchSuggestionsPage = useCallback(async (
    page: number, 
    limit: number,
    status: StatusFilterOption,
    style: string,
    search: string,
    sort: SortOption
  ) => {
    setLoading(true);
    setError('');
    logInfo('SuggestionOversight API call: fetch suggestions page', { page, limit, status, style, search, sort });
    try {
      const result = await fetchAdminSuggestions({ 
        page, 
        limit,
        status: status === 'All' ? undefined : status, // Send undefined if 'All'
        danceStyle: style === 'All' ? undefined : style, // Send undefined if 'All'
        searchTerm: search || undefined, // Send undefined if empty
        sortBy: sort
      });
      
      if (!result.success || !Array.isArray(result.data)) {
        logError('Fetch suggestions error: Invalid data received', { result });
        throw new Error(result.message || 'Invalid data format received');
      }

      setSuggestions(result.data || []);
      
      if (result.pagination) {
        setCurrentPage(result.pagination.currentPage || page);
        setTotalPages(result.pagination.totalPages || 0);
        setTotalCount(result.pagination.totalCount || 0);
      } else {
        setTotalPages(Math.ceil((result.data?.length || 0) / limit));
        setTotalCount(result.data?.length || 0);
        logWarn('SuggestionOversight: Pagination data missing from API response', { page, limit });
      }
      
      logInfo('Fetch suggestions page success', { 
        count: result.data?.length || 0, 
        currentPage: result.pagination?.currentPage,
        totalPages: result.pagination?.totalPages,
        totalCount: result.pagination?.totalCount
      });

    } catch (err: any) {
      setError(`Failed to load suggestions: ${err.message}`);
      logError('Fetch suggestions page error', err, { page, limit });
      setSuggestions([]);
      setTotalPages(0);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchStyles = useCallback(async () => {
    logInfo('SuggestionOversight: Fetching dance styles');
    try {
      const result = await getDanceStyles();
      if (result.success && Array.isArray(result.data)) {
        setAvailableDanceStyles(result.data);
        logInfo('SuggestionOversight: Dance styles fetched', { count: result.data.length });
      } else {
        throw new Error(result.message || 'Invalid data format for styles');
      }
    } catch (err: any) {
      setError(`Failed to load dance styles: ${err.message}`);
      logError('SuggestionOversight: Fetch styles error', err);
      setAvailableDanceStyles([]);
      toast.error('Could not load dance styles. Filtering may not work.');
    }
  }, []);

  useEffect(() => {
    const storedStyle = sessionStorage.getItem('suggestionOversightStyleFilter');
    if (storedStyle) {
      setSelectedStyle(storedStyle);
    }
  }, []);

  useEffect(() => {
    sessionStorage.setItem('suggestionOversightStyleFilter', selectedStyle);
  }, [selectedStyle]);
  
  const performSuggestionAction = async (
    actionName: string,
    suggestionId: string,
    apiCall: () => Promise<any>,
  ) => {
    const loadingKey = `${suggestionId}-${actionName}`;
    setActionLoading(prev => ({ ...prev, [loadingKey]: true }));
    setError('');
    logInfo(`SuggestionOversight Action: ${actionName}`, { id: suggestionId });
    
    try {
      const result = await apiCall();
      if (!result || !result.success) {
        throw new Error(result?.message || `Failed to ${actionName} suggestion.`);
      }
      logInfo(`SuggestionOversight Action Success: ${actionName}`, { id: suggestionId, data: result.data });
      await fetchSuggestionsPage(currentPage, ITEMS_PER_PAGE, statusFilter, selectedStyle, searchTerm, sortBy);
      toast.success(`${actionName.charAt(0).toUpperCase() + actionName.slice(1)} successful!`);
      
    } catch (err: any) {
      setError(`Failed to ${actionName} suggestion: ${err.message}`);
      logError(`SuggestionOversight Action Error: ${actionName}`, err, { id: suggestionId });
      
      setActionLoading(prev => {
        const newState = { ...prev };
        delete newState[loadingKey];
        return newState;
      });
    }
  };

  const handleSuggestionUpdate = useCallback((updatedSuggestion: Suggestion) => {
    logInfo('SuggestionOversight: WebSocket received suggestion update/create', updatedSuggestion);
    
    fetchSuggestionsPage(currentPage, ITEMS_PER_PAGE, statusFilter, selectedStyle, searchTerm, sortBy);

    setActionLoading(prev => {
      const newState = { ...prev };
      Object.keys(newState).forEach(key => {
        if (key.startsWith(`${updatedSuggestion.id}-`)) {
          delete newState[key];
        }
      });
      return newState;
    });
    setError('');
  }, [fetchSuggestionsPage, currentPage, statusFilter, selectedStyle, searchTerm, sortBy]);

  const handleSuggestionDeleted = useCallback(({ id }: { id: string }) => {
    logInfo('SuggestionOversight: WebSocket received suggestion:deleted', { id });
    
    fetchSuggestionsPage(currentPage, ITEMS_PER_PAGE, statusFilter, selectedStyle, searchTerm, sortBy);

    setActionLoading(prev => {
      const newState = { ...prev };
      Object.keys(newState).forEach(key => {
        if (key.startsWith(`${id}-`)) {
          delete newState[key];
        }
      });
      return newState;
    });
  }, [fetchSuggestionsPage, currentPage, statusFilter, selectedStyle, searchTerm, sortBy]);

  useEffect(() => {
    logInfo('SuggestionOversight: useEffect triggered for data fetch', {currentPage, statusFilter, selectedStyle, searchTerm, sortBy});
    fetchSuggestionsPage(currentPage, ITEMS_PER_PAGE, statusFilter, selectedStyle, searchTerm, sortBy);
  }, [currentPage, statusFilter, selectedStyle, searchTerm, sortBy, fetchSuggestionsPage]);

  useEffect(() => {
    if (!socketRef.current) {
      const token = localStorage.getItem('token');
      const newSocket = io('/', { auth: { token }, path: '/socket.io' });
      socketRef.current = newSocket;

      logInfo('SuggestionOversight: Establishing WebSocket...');
      newSocket.on('connect', () => logInfo('SuggestionOversight: WebSocket connected'));
      newSocket.on('disconnect', (reason: any) => {
        logInfo('SuggestionOversight: WebSocket disconnected', { reason });
        if (reason === 'io server disconnect') socketRef.current = null;
      });
      newSocket.on('connect_error', (err: any) => {
        logError('SuggestionOversight: WebSocket connection error', err);
        setError('WebSocket connection failed.');
        socketRef.current = null;
      });

      newSocket.on('suggestion:updated', handleSuggestionUpdate);
      newSocket.on('suggestion:deleted', handleSuggestionDeleted);
    }

    fetchStyles();

    return () => {
      if (socketRef.current) {
        logInfo('SuggestionOversight: Cleaning up WebSocket');
        socketRef.current.off('suggestion:updated', handleSuggestionUpdate);
        socketRef.current.off('suggestion:deleted', handleSuggestionDeleted);
      }
    };
  }, [fetchStyles, handleSuggestionUpdate, handleSuggestionDeleted]);

  const handleApprove = (id: string) => {
    performSuggestionAction('approve', id, () => approveSuggestion(id));
  };

  const handleReject = (id: string) => {
    performSuggestionAction('reject', id, () => rejectSuggestion(id));
  };

  const handleLockToggle = (id: string, currentLockStatus: boolean) => {
    const actionName = currentLockStatus ? 'unlock' : 'lock';
    performSuggestionAction(actionName, id, () => updateSuggestionLock(id, !currentLockStatus));
  };

  const handleUndoReject = (id: string) => {
    performSuggestionAction('undo-reject', id, () => undoRejectSuggestion(id));
  };

  const handleUndoApprove = (id: string) => {
    performSuggestionAction('undo-approve', id, () => undoApproveSuggestion(id));
  };

  const handleChangeStyle = (id: string, currentStyle: string) => {
    setCurrentSuggestionId(id);
    setNewDanceStyle(currentStyle);
    setStyleChangeModalOpen(true);
  };

  const handleStyleChangeSubmit = async () => {
    if (!currentSuggestionId || !newDanceStyle) {
      toast.error('Please select a dance style');
      return;
    }

    const styleChangeLoadingKey = `${currentSuggestionId}-change-style`;
    setActionLoading(prev => ({ ...prev, [styleChangeLoadingKey]: true }));
    setError('');
    logInfo('SuggestionOversight Action: change-style', { id: currentSuggestionId, newStyle: newDanceStyle });
    
    try {
      const result = await updateSuggestionStyle(currentSuggestionId, newDanceStyle);
      if (!result || !result.success) {
        throw new Error(result?.message || 'Failed to update dance style.');
      }
      
      logInfo('SuggestionOversight Action Success: change-style', { id: currentSuggestionId, data: result.data });
      await fetchSuggestionsPage(currentPage, ITEMS_PER_PAGE, statusFilter, selectedStyle, searchTerm, sortBy);
      toast.success('Dance style updated successfully!');
      
      // Close the modal
      setStyleChangeModalOpen(false);
      setCurrentSuggestionId(null);
      
    } catch (err: any) {
      setError(`Failed to update dance style: ${err.message}`);
      logError('SuggestionOversight Action Error: change-style', err, { id: currentSuggestionId });
      toast.error(err.message);
    } finally {
      setActionLoading(prev => {
        const newState = { ...prev };
        delete newState[styleChangeLoadingKey];
        return newState;
      });
    }
  };

  const filteredAndSortedSuggestions = useMemo(() => {
    let processedSuggestions = suggestions;

    if (selectedStyle !== 'All') {
      processedSuggestions = processedSuggestions.filter(s => s.danceStyle === selectedStyle);
    }

    if (statusFilter !== 'All') {
      processedSuggestions = processedSuggestions.filter(s => s.status === statusFilter);
    }

    if (searchTerm.trim() !== '') {
      const lowerSearchTerm = searchTerm.toLowerCase();
      processedSuggestions = processedSuggestions.filter(s => 
        s.user.username.toLowerCase().includes(lowerSearchTerm)
      );
    }

    processedSuggestions.sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return sortBy === 'newest' ? dateB - dateA : dateA - dateB;
    });

    return processedSuggestions;
  }, [suggestions, selectedStyle, statusFilter, searchTerm, sortBy]);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };
  
  const PaginationControls = () => {
    if (totalPages <= 1) return null;

    const pageNumbers = [];
    const MAX_VISIBLE_PAGES = 5;
    let startPage = Math.max(1, currentPage - Math.floor(MAX_VISIBLE_PAGES / 2));
    let endPage = Math.min(totalPages, startPage + MAX_VISIBLE_PAGES - 1);

    if (endPage - startPage + 1 < MAX_VISIBLE_PAGES) {
        startPage = Math.max(1, endPage - MAX_VISIBLE_PAGES + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return (
      <div className="flex items-center justify-between mt-6 py-3 bg-white px-4 sm:px-6 rounded-lg shadow border-t border-gray-200">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            Previous
          </button>
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            Next
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-gray-700">
              Showing <span className="font-medium">{(currentPage - 1) * ITEMS_PER_PAGE + 1}</span>
              {' '}to <span className="font-medium">{Math.min(currentPage * ITEMS_PER_PAGE, totalCount)}</span>
              {' '}of <span className="font-medium">{totalCount}</span> results
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                <span className="sr-only">Previous</span>
                <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
              </button>
              {startPage > 1 && (
                <>
                  <button
                    onClick={() => handlePageChange(1)}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    1
                  </button>
                  {startPage > 2 && <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>}
                </>
              )}
              {pageNumbers.map((number) => (
                <button
                  key={number}
                  onClick={() => handlePageChange(number)}
                  aria-current={currentPage === number ? 'page' : undefined}
                  className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${
                    currentPage === number ? 'z-10 bg-primary-50 border-primary-500 text-primary-600' : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {number}
                </button>
              ))}
              {endPage < totalPages && (
                <>
                  {endPage < totalPages - 1 && <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>}
                  <button
                    onClick={() => handlePageChange(totalPages)}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    {totalPages}
                  </button>
                </>
              )}
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                <span className="sr-only">Next</span>
                <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
              </button>
            </nav>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen flex flex-col items-center bg-gray-100 py-8 px-2 sm:px-4">
      <div className="w-full max-w-6xl space-y-6">
        <h2 className="text-3xl font-extrabold text-primary-800 text-center">Suggestion Oversight</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end bg-white p-4 rounded-lg shadow">
          <div className="col-span-1">
            <label htmlFor="usernameSearch" className="block text-sm font-medium text-gray-700 mb-1">
              Search by Submitter
            </label>
            <input
              id="usernameSearch"
              type="text"
              placeholder="Enter username..."
              value={searchTerm}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" 
            />
          </div>

          <div className="col-span-1">
            <label htmlFor="statusFilter" className="block text-sm font-medium text-gray-700 mb-1">
              Filter by Status
            </label>
            <select
              id="statusFilter"
              value={statusFilter}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setStatusFilter(e.target.value as StatusFilterOption)}
              className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white"
            >
              <option value="PENDING">Pending</option>
              <option value="All">All Statuses</option>
              <option value="APPROVED">Approved</option>
              <option value="REJECTED">Rejected</option>
            </select>
          </div>

          <div className="col-span-1">
            <label htmlFor="sortBy" className="block text-sm font-medium text-gray-700 mb-1">
              Sort By
            </label>
            <select
              id="sortBy"
              value={sortBy}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setSortBy(e.target.value as SortOption)}
              className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white"
            >
              <option value="oldest">Oldest First</option>
              <option value="newest">Newest First</option>
            </select>
          </div>
        </div>

        <div className="border-b border-gray-200 bg-white p-2 rounded-lg shadow">
          <nav className="-mb-px flex space-x-2 sm:space-x-4 overflow-x-auto pb-1 scrollbar-hide" aria-label="Tabs">
            <button
              key="All"
              onClick={() => setSelectedStyle('All')}
              className={`whitespace-nowrap py-2 px-3 sm:py-3 sm:px-4 border-b-2 font-medium text-xs sm:text-sm transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-primary-300 focus:ring-offset-1 rounded-t-md ${ 
                selectedStyle === 'All' 
                ? 'border-primary-500 text-primary-600' 
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' 
              }`}
            >
              All
            </button>
            {availableDanceStyles.map((style) => (
              <button
                key={style}
                onClick={() => setSelectedStyle(style)}
                className={`whitespace-nowrap py-2 px-3 sm:py-3 sm:px-4 border-b-2 font-medium text-xs sm:text-sm transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-primary-300 focus:ring-offset-1 rounded-t-md ${ 
                  selectedStyle === style 
                  ? 'border-primary-500 text-primary-600' 
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' 
                }`}
                aria-current={selectedStyle === style ? 'page' : undefined}
              >
                {style}
              </button>
            ))}
          </nav>
        </div>

        {error && <div className="text-red-700 p-3 border border-red-300 bg-red-100 rounded shadow-sm" role="alert"><strong>Error:</strong> {error}</div>}
        
        {loading ? (
          <div className="text-center p-6">Loading suggestions...</div>
        ) : filteredAndSortedSuggestions.length === 0 ? (
          <div className="text-center text-gray-500 p-6 bg-white rounded-lg shadow">
            No suggestions match the current filters.
          </div>
        ) : (
          <div className="space-y-4">
            <div className="hidden md:grid md:grid-cols-12 gap-4 p-3 bg-primary-600 text-white rounded-t-lg shadow font-semibold text-sm">
                <div className="md:col-span-5">Song / Submitter</div>
                <div className="md:col-span-2">Style</div>
                <div className="md:col-span-1 text-center">Status</div>
                <div className="md:col-span-1 text-center">Votes</div>
                <div className="md:col-span-3 text-center">Actions</div>
            </div>

            {filteredAndSortedSuggestions.map((s) => {
              const approveLoadingKey = `${s.id}-approve`;
              const rejectLoadingKey = `${s.id}-reject`;
              const lockLoadingKey = `${s.id}-${s.isLocked ? 'unlock' : 'lock'}`;
              const undoRejectLoadingKey = `${s.id}-undo-reject`;
              const undoApproveLoadingKey = `${s.id}-undo-approve`;
              const styleChangeLoadingKey = `${s.id}-change-style`;

              return (
                <div key={s.id} className="bg-white shadow rounded-lg p-4 border border-gray-200 flex flex-col md:grid md:grid-cols-12 md:gap-4 md:items-center">
                  
                  <div className="md:col-span-5 flex items-start space-x-3 mb-3 md:mb-0">
                    {s.thumbnailUrl && (
                      <img src={s.thumbnailUrl} alt="Video thumbnail" className="w-16 h-auto object-cover rounded flex-shrink-0" />
                    )}
                    <div className="flex-grow">
                      <a 
                        href={`https://www.youtube.com/watch?v=${s.youtubeVideoId}`} 
                        target="_blank" 
                        rel="noopener noreferrer" 
                        className="font-semibold text-primary-700 hover:text-primary-900 break-words text-sm sm:text-base"
                      >
                        {s.title}
                      </a>
                       {s.channelTitle && <p className="text-xs text-gray-500 mt-1">By: {s.channelTitle}</p>}
                       <p className="text-xs text-gray-500 mt-1">
                         Submitted By: <span className="font-medium">{s.user.username}</span> 
                         {' on '} 
                         {new Date(s.createdAt).toLocaleDateString()}
                       </p>
                    </div>
                  </div>

                  <div className="md:col-span-2 mb-2 md:mb-0">
                    <span className="font-medium text-gray-700 md:hidden">Style: </span>
                    <span className="text-sm">{s.danceStyle}</span>
                    {/* Change style button - always visible */}
                    <button
                      onClick={() => handleChangeStyle(s.id, s.danceStyle)}
                      disabled={actionLoading[styleChangeLoadingKey]}
                      className="flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition duration-150"
                    >
                      <MusicalNoteIcon className="h-4 w-4 mr-1" /> Change Style
                      {actionLoading[styleChangeLoadingKey] && <span className="loader ml-2"></span>}
                    </button>
                  </div>

                   <div className="md:col-span-1 text-left md:text-center mb-2 md:mb-0">
                    <span className="font-medium text-gray-700 md:hidden">Status: </span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      s.status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                      s.status === 'REJECTED' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {s.status}
                    </span>
                  </div>

                  <div className="md:col-span-1 text-left md:text-center mb-3 md:mb-0">
                     <span className="font-medium text-gray-700 md:hidden">Votes: </span>
                     <span className="text-sm font-medium">{s.votes}</span>
                  </div>

                  <div className="md:col-span-3 flex flex-wrap gap-2 justify-start md:justify-center items-center border-t pt-3 md:border-none md:pt-0">
                    {s.status === 'PENDING' && (
                      <>
                        <button
                          onClick={() => handleApprove(s.id)}
                          disabled={actionLoading[approveLoadingKey]}
                          className="flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 transition duration-150"
                        >
                           <CheckCircleIcon className="h-4 w-4 mr-1" /> Approve
                          {actionLoading[approveLoadingKey] && <span className="loader ml-2"></span>}
                        </button>
                        <button
                          onClick={() => handleReject(s.id)}
                          disabled={actionLoading[rejectLoadingKey]}
                           className="flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 transition duration-150"
                        >
                          <XCircleIcon className="h-4 w-4 mr-1" /> Reject
                           {actionLoading[rejectLoadingKey] && <span className="loader ml-2"></span>}
                        </button>
                      </>
                    )}
                    {s.status === 'APPROVED' && (
                      <button
                          onClick={() => handleUndoApprove(s.id)}
                          disabled={actionLoading[undoApproveLoadingKey]}
                          className="flex items-center justify-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 transition duration-150"
                        >
                          <ArrowUturnLeftIcon className="h-4 w-4 mr-1"/> Undo Approval
                          {actionLoading[undoApproveLoadingKey] && <span className="loader ml-2"></span>}
                        </button>
                    )}
                     {s.status === 'REJECTED' && (
                      <button
                          onClick={() => handleUndoReject(s.id)}
                          disabled={actionLoading[undoRejectLoadingKey]}
                           className="flex items-center justify-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 transition duration-150"
                        >
                          <ArrowUturnLeftIcon className="h-4 w-4 mr-1"/> Undo Rejection
                          {actionLoading[undoRejectLoadingKey] && <span className="loader ml-2"></span>}
                        </button>
                    )}
                    <button
                      onClick={() => handleLockToggle(s.id, s.isLocked)}
                      disabled={actionLoading[lockLoadingKey]}
                       className={`flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${s.isLocked ? 'bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-400' : 'bg-gray-500 hover:bg-gray-600 focus:ring-gray-400'} focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 transition duration-150`}
                    >
                      {s.isLocked ? <LockClosedIcon className="h-4 w-4 mr-1" /> : <LockOpenIcon className="h-4 w-4 mr-1" />} {s.isLocked ? 'Locked' : 'Lock'}
                      {actionLoading[lockLoadingKey] && <span className="loader ml-2"></span>}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
        <PaginationControls />
      </div>

      {/* Style Change Modal */}
      {styleChangeModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">
              Change Dance Style
            </h3>
            <p className="mb-4 text-gray-600">
              Select a new dance style for this song:
            </p>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 mb-4"
              value={newDanceStyle}
              onChange={(e) => setNewDanceStyle(e.target.value)}
              autoFocus
            >
              <option value="">Select a dance style</option>
              {availableDanceStyles.map(style => (
                <option key={style} value={style}>{style}</option>
              ))}
            </select>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setStyleChangeModalOpen(false);
                  setCurrentSuggestionId(null);
                }}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleStyleChangeSubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                disabled={!newDanceStyle}
              >
                Update Style
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SuggestionOversight;