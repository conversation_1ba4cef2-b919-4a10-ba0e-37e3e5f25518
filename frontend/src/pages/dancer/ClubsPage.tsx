import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { MagnifyingGlassIcon, MapPinIcon, GlobeAltIcon, ClockIcon } from '@heroicons/react/24/outline';
import { logInfo, logError } from '../../utils/logger';
import { useNavigate, Link } from 'react-router-dom';
// import LoadingSpinner from '../../components/LoadingSpinner'; // Assuming you have this component

// Types
interface OpeningHours {
  [day: string]: {
    open: string;
    close: string;
  } | null;
}

interface Location {
  name: string;
  address: string;
  city: string;
  country: string;
  description?: string;
  openingHours?: OpeningHours;
  phone?: string;
}

interface DanceClub {
  id: string;
  name: string;
  description: string;
  address: string;
  city: string;
  state?: string;
  country: string;
  website?: string;
  phone?: string;
  photoUrl?: string;
  logoUrl?: string;
  danceStyles: string[];
  openingHours?: OpeningHours;
  featured: boolean;
  facilities?: string[];
  achievements?: string[];
  additionalLocations?: Location[];
  priorityOrder?: number;
}

const mockClubsData: DanceClub[] = [
  {
    id: '1', // Preserving original ID for Ritmo
    name: 'Ritmo Dance Studio',
    description: 'The largest dance school in Studentski Grad and one of the most successful in Bulgaria! With 16+ years of experience, our strategic locations attract students from all universities in Sofia. We offer classes in Salsa, Bachata, Kizomba, Merengue, Cha Cha, and Bulgarian Folk Dance with professional instructors.',
    address: 'Studentski Grad, ul. Yordan Yosifov 8B, et.1',
    city: 'Sofia',
    country: 'Bulgaria',
    website: 'https://ritmo.bg',
    phone: '+359 883 413 706',
    photoUrl: '/img/clubs/ritmo-studio.jpg', // User's original path
    logoUrl: '/img/clubs/ritmo-logo.jpg',   // User's original path
    danceStyles: ['Salsa', 'Bachata', 'Kizomba', 'Merengue', 'Cha Cha', 'Bulgarian Folk Dance'],
    openingHours: {
      'Monday': { open: '18:15', close: '22:30' },
      'Tuesday': { open: '18:15', close: '22:30' },
      'Wednesday': { open: '18:15', close: '22:30' },
      'Thursday': { open: '18:15', close: '22:30' },
      'Friday': { open: '18:15', close: '22:30' },
      'Saturday': null,
      'Sunday': null,
    },
    additionalLocations: [
      {
        name: 'Ritmo Dance Studio - NDK',
        address: 'NDK, ploshtad "Bulgaria" 1, Dance Station',
        city: 'Sofia',
        country: 'Bulgaria',
        description: 'Our second location in the center of Sofia at NDK. We offer Salsa and Bachata classes for beginners, intermediate and advanced levels.',
        phone: '+359 883 413 706',
        openingHours: {
          'Monday': { open: '18:30', close: '21:30' },
          'Tuesday': { open: '18:30', close: '21:30' },
          'Wednesday': { open: '18:30', close: '21:30' },
          'Thursday': { open: '18:30', close: '21:30' },
          'Friday': null,
          'Saturday': null,
          'Sunday': null,
        }
      }
    ],
    facilities: [
      'Multiple Dance Halls (200m², 120m², 200m²)',
      'Professional Dance Floor (Laminate)',
      'Full-length Mirrors',
      'Air Conditioning & Ventilation',
      'Male/Female Changing Rooms',
      'Professional Sound System',
      'Reception & Waiting Area',
      'Water Dispenser'
    ],
    achievements: [
      '16+ years of experience',
      '1st place in Bulgaria (Salsa & Bachata Teams)',
      '4th place worldwide (Bachata Couples Cabaret WLDC 2016)',
      'Over 4,500 students trained',
      'Over 280 unique wedding choreographies',
      'Regularly host events and parties'
    ],
    featured: true,
    priorityOrder: 1
  },
  {
    id: 'sofia-salsa-de-fuego',
    name: 'Salsa de Fuego',
    description: 'A leading dance school in Sofia offering classes in Salsa, Bachata, and Kizomba. Known for its energetic atmosphere, quality teaching, and regular parties.',
    address: 'бул. Черни Връх 32, ет. 3, Lozenets',
    city: 'Sofia',
    country: 'Bulgaria',
    website: 'http://salsadefuego.net/',
    phone: '+359 885 070 801',
    photoUrl: '/img/clubs/generic-club-photo-1.jpg',
    logoUrl: '/img/clubs/generic-logo-1.png',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba'],
    openingHours: {
      'Monday': { open: '19:00', close: '22:00' },
      'Tuesday': { open: '19:00', close: '22:00' },
      'Wednesday': { open: '19:00', close: '22:00' },
      'Thursday': { open: '19:00', close: '22:00' },
      'Friday': { open: '19:00', close: '23:00' },
      'Saturday': null,
      'Sunday': { open: '18:00', close: '21:00' },
    },
    featured: true,
    facilities: ['Spacious dance hall', 'Mirrors', 'Professional Sound system', 'Changing rooms', 'Bar area'],
    achievements: ['One of Sofia\'s top Latin dance schools', 'Hosts international artists', 'Regular social parties'],
    additionalLocations: []
  },
  {
    id: 'sofia-latin-force',
    name: 'Latin Force Dance Studio',
    description: 'One of the largest dance centers in Sofia with multiple locations. Offers a wide variety of dance styles including Salsa, Bachata, Kizomba, and Zouk with highly qualified instructors.',
    address: 'ul. Tsar Samuil 50, et. 2 (Pliska)', // One of their main locations
    city: 'Sofia',
    country: 'Bulgaria',
    website: 'https://latinforce.bg',
    phone: '+359 887 427 050',
    photoUrl: '/img/clubs/generic-club-photo-2.jpg',
    logoUrl: '/img/clubs/generic-logo-2.png',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba', 'Zouk', 'Cha Cha', 'Argentine Tango'],
    openingHours: { // General for most halls
      'Monday': { open: '18:00', close: '22:30' },
      'Tuesday': { open: '18:00', close: '22:30' },
      'Wednesday': { open: '18:00', close: '22:30' },
      'Thursday': { open: '18:00', close: '22:30' },
      'Friday': { open: '18:00', close: '22:30' },
      'Saturday': { open: '10:00', close: '18:00' },
      'Sunday': { open: '10:00', close: '18:00' },
    },
    featured: true,
    facilities: ['Multiple modern dance halls', 'Air conditioning', 'Professional flooring', 'Spacious changing rooms', 'Reception area'],
    achievements: ['Hosts Sofia Touch Team', 'Organizes major workshops & events', 'Wide range of dance styles'],
    additionalLocations: [
      { name: 'Latin Force Hall 2 (Center)', address: 'ul. Osogovo 2 (Mall of Sofia)', city: 'Sofia', country: 'Bulgaria' },
      { name: 'Latin Force Hall 3 (Friends)', address: 'Graf Ignatiev 5 (Friends building)', city: 'Sofia', country: 'Bulgaria' }
    ]
  },
  {
    id: 'sofia-royce-dance',
    name: 'Royce Dance Studio',
    description: 'Royce Dance Studio offers authentic training in Salsa, Bachata, Kizomba, Zouk, and Argentine Tango. Focus on quality instruction and social dancing.',
    address: 'ul. "Солунска" 45',
    city: 'Sofia',
    country: 'Bulgaria',
    website: 'https://roycedance.com',
    phone: '+359 888 805 171',
    photoUrl: '/img/clubs/generic-club-photo-3.jpg',
    logoUrl: '/img/clubs/generic-logo-3.png',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba', 'Zouk', 'Argentine Tango'],
    openingHours: {
      'Monday': { open: '18:30', close: '22:00' },
      'Tuesday': { open: '18:30', close: '22:00' },
      'Wednesday': { open: '18:30', close: '22:00' },
      'Thursday': { open: '18:30', close: '22:00' },
      'Friday': { open: '18:30', close: '22:00' },
      'Saturday': null,
      'Sunday': null,
    },
    featured: false,
    facilities: ['Central location', 'Dedicated dance space', 'Experienced instructors'],
    achievements: ['Specializes in multiple Latin dances', 'Focus on authentic technique'],
  },
  {
    id: 'sofia-palante',
    name: 'PaLante Dance Studio',
    description: 'PaLante offers dynamic Salsa and Bachata classes in Sofia, focusing on musicality, technique, and fun. Welcoming atmosphere for all levels.',
    address: 'бул. "Сливница" 182, ет. 2 (близо до Лъвов мост)',
    city: 'Sofia',
    country: 'Bulgaria',
    website: 'https://palante.bg',
    phone: '+359 899 929 100',
    photoUrl: '/img/clubs/generic-club-photo-4.jpg',
    logoUrl: '/img/clubs/generic-logo-4.png',
    danceStyles: ['Salsa', 'Bachata'],
    openingHours: { /* Typical evening class times */
      'Monday': { open: '19:00', close: '22:00' },
      'Tuesday': { open: '19:00', close: '22:00' },
      'Wednesday': { open: '19:00', close: '22:00' },
      'Thursday': { open: '19:00', close: '22:00' },
      'Friday': null,
      'Saturday': null,
      'Sunday': null,
    },
    featured: false,
    facilities: ['Dance hall', 'Mirrors', 'Sound system'],
    achievements: ['Strong focus on Salsa and Bachata', 'Regular classes and social events'],
  },
  {
    id: 'sofia-un-beso',
    name: 'Un Beso Dance',
    description: 'Un Beso provides classes in Salsa, Bachata, and Kizomba, with a friendly approach. Located at Dom na kulturata "Sredets".',
    address: 'ул. "Китен" 1 (Дом на културата "Средец")',
    city: 'Sofia',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/UnBesitoDance/', // Facebook as primary
    phone: '+359 888 079 192',
    photoUrl: '/img/clubs/generic-club-photo-5.jpg',
    logoUrl: '/img/clubs/generic-logo-5.png',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba'],
    openingHours: { /* Typical evening class times */
      'Monday': { open: '19:00', close: '22:00' },
      'Tuesday': { open: '19:00', close: '22:00' },
      'Wednesday': { open: '19:00', close: '22:00' },
      'Thursday': { open: '19:00', close: '22:00' },
      'Friday': null,
      'Saturday': null,
      'Sunday': null,
    },
    featured: false,
    facilities: ['Dance studio space', 'Community focused'],
    achievements: ['Offers core Latin styles', 'Active in Sofia'],
  },
  {
    id: 'plovdiv-casa-de-baile',
    name: 'Casa de Baile',
    description: 'Plovdiv\'s premier Latin dance school, offering Salsa, Bachata, and Kizomba. Known for quality instruction and a vibrant community.',
    address: 'ул. "Петко Д. Петков" 43',
    city: 'Plovdiv',
    country: 'Bulgaria',
    website: 'https://casadebaile.bg',
    phone: '+359 888 800 124',
    photoUrl: '/img/clubs/generic-club-photo-6.jpg',
    logoUrl: '/img/clubs/generic-logo-6.png',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba', 'Cha Cha'],
    openingHours: {
      'Monday': { open: '18:30', close: '22:00' },
      'Tuesday': { open: '18:30', close: '22:00' },
      'Wednesday': { open: '18:30', close: '22:00' },
      'Thursday': { open: '18:30', close: '22:00' },
      'Friday': { open: '18:30', close: '22:00' },
      'Saturday': null,
      'Sunday': null,
    },
    featured: true,
    facilities: ['Multiple dance halls', 'Professional instructors', 'Regular parties and events', 'Central location'],
    achievements: ['Long-standing school in Plovdiv', 'Strong performance teams', 'Hosts workshops'],
  },
  {
    id: 'plovdiv-encanto',
    name: 'Encanto Dance Plovdiv',
    description: 'Encanto Dance offers classes in Salsa, Bachata, and Kizomba in Plovdiv. Focus on creating a fun and supportive learning environment.',
    address: 'бул. "Христо Ботев" 82 (Дом на техниката)',
    city: 'Plovdiv',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/EncantoDancePlovdiv/', // Facebook
    phone: '+359 897 860 080',
    photoUrl: '/img/clubs/generic-club-photo-7.jpg',
    logoUrl: '/img/clubs/generic-logo-7.png',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba'],
    openingHours: { /* Typical evening class times */
      'Monday': { open: '19:00', close: '22:00' },
      'Tuesday': { open: '19:00', close: '22:00' },
      'Wednesday': { open: '19:00', close: '22:00' },
      'Thursday': { open: '19:00', close: '22:00' },
      'Friday': null,
      'Saturday': null,
      'Sunday': null,
    },
    featured: false,
    facilities: ['Dance hall', 'Experienced teachers', 'Social events'],
    achievements: ['Active in Plovdiv dance community', 'Classes for different levels'],
  },
  {
    id: 'plovdiv-millennium',
    name: 'Millennium Dance Center',
    description: 'A dance center in Plovdiv offering various styles, including Salsa and Bachata. Caters to different age groups and skill levels.',
    address: 'бул. "Никола Вапцаров" 9',
    city: 'Plovdiv',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/MillenniumPlovdiv/', // Facebook
    phone: '+359 896 726 930',
    photoUrl: '/img/clubs/generic-club-photo-8.jpg',
    logoUrl: '/img/clubs/generic-logo-8.png',
    danceStyles: ['Salsa', 'Bachata', 'Folk Dances', 'Modern Dances'],
    openingHours: { /* Based on class schedule, evenings */
      'Monday': { open: '18:00', close: '21:00' },
      'Tuesday': { open: '18:00', close: '21:00' },
      'Wednesday': { open: '18:00', close: '21:00' },
      'Thursday': { open: '18:00', close: '21:00' },
      'Friday': { open: '18:00', close: '21:00' },
      'Saturday': null,
      'Sunday': null,
    },
    featured: false,
    facilities: ['Dance studio', 'Variety of classes'],
    achievements: ['Offers diverse dance styles'],
  },
  {
    id: 'varna-club-fuego',
    name: 'Club Fuego Varna',
    description: 'Part of the Fuego dance school family, Club Fuego Varna is a key spot for Salsa, Bachata, and Kizomba lovers in the city.',
    address: 'бул. "Княз Борис I-ви" 115',
    city: 'Varna',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/clubfuegovarna/', // Facebook
    phone: '+359 895 150 363',
    photoUrl: '/img/clubs/generic-club-photo-9.jpg',
    logoUrl: '/img/clubs/generic-logo-9.png',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba'],
    openingHours: { /* Typical evening class times and weekend parties */
      'Monday': { open: '19:00', close: '22:00' },
      'Tuesday': { open: '19:00', close: '22:00' },
      'Wednesday': { open: '19:00', close: '22:00' },
      'Thursday': { open: '19:00', close: '22:00' },
      'Friday': { open: '20:00', close: '02:00' }, // Party night
      'Saturday': { open: '20:00', close: '02:00' },// Party night
      'Sunday': null,
    },
    featured: true,
    facilities: ['Dance hall', 'Bar', 'Regular social events', 'Central location'],
    achievements: ['Popular venue in Varna', 'Hosts parties and workshops'],
  },
  {
    id: 'varna-malambo',
    name: 'Malambo Dance School',
    description: 'Malambo Dance School in Varna offers professional training in Salsa, Bachata, Kizomba, and other dance styles. Committed to quality and passion for dance.',
    address: 'ул. "Д-р Пискюлиев" 42',
    city: 'Varna',
    country: 'Bulgaria',
    website: 'https://malambo.bg',
    phone: '+359 888 539 188',
    photoUrl: '/img/clubs/generic-club-photo-10.jpg',
    logoUrl: '/img/clubs/generic-logo-10.png',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba', 'Cha Cha', 'Reggaeton'],
    openingHours: { /* From website schedule */
      'Monday': { open: '18:00', close: '22:00' },
      'Tuesday': { open: '18:00', close: '22:00' },
      'Wednesday': { open: '18:00', close: '22:00' },
      'Thursday': { open: '18:00', close: '22:00' },
      'Friday': { open: '18:00', close: '21:00' },
      'Saturday': null,
      'Sunday': { open: '17:00', close: '20:00' },
    },
    featured: false,
    facilities: ['Professional dance studio', 'Experienced instructors', 'Variety of classes'],
    achievements: ['Established dance school in Varna', 'Offers diverse dance programs'],
  },
  {
    id: 'varna-ds-amigos',
    name: 'DS Amigos',
    description: 'DS Amigos in Varna provides Salsa and Bachata classes in a friendly environment. Located in Mall Varna.',
    address: 'ул. "Академик Андрей Сахаров" 2 (Mall Varna, ет.1)',
    city: 'Varna',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/DSAAmigos/', // Facebook
    phone: '+359 899 953 779',
    photoUrl: '/img/clubs/generic-club-photo-11.jpg',
    logoUrl: '/img/clubs/generic-logo-11.png',
    danceStyles: ['Salsa', 'Bachata'],
    openingHours: { /* Typical evening class times */
      'Monday': { open: '19:00', close: '21:30' },
      'Tuesday': { open: '19:00', close: '21:30' },
      'Wednesday': { open: '19:00', close: '21:30' },
      'Thursday': { open: '19:00', close: '21:30' },
      'Friday': null,
      'Saturday': null,
      'Sunday': null,
    },
    featured: false,
    facilities: ['Located in Mall Varna', 'Dance classes for different levels'],
    achievements: ['Accessible location', 'Focus on Salsa and Bachata'],
  },
  {
    id: 'burgas-salsa-club-de-fuego',
    name: 'Salsa Club De Fuego Burgas',
    description: 'The Burgas branch of the De Fuego family, offering Salsa, Bachata, and Kizomba classes and parties.',
    address: 'ул. "Цар Симеон I-ви" 72',
    city: 'Burgas',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/SalsaClubDeFuegoBurgas/', // Facebook
    phone: '+359 896 769 031',
    photoUrl: '/img/clubs/generic-club-photo-12.jpg',
    logoUrl: '/img/clubs/generic-logo-12.png',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba'],
    openingHours: { /* Evenings and party nights */
      'Monday': { open: '19:00', close: '22:00' },
      'Tuesday': { open: '19:00', close: '22:00' },
      'Wednesday': { open: '19:00', close: '22:00' },
      'Thursday': { open: '19:00', close: '22:00' },
      'Friday': { open: '21:00', close: '01:00' }, // Party
      'Saturday': null,
      'Sunday': null,
    },
    featured: true,
    facilities: ['Dance hall', 'Regular classes and parties', 'Part of De Fuego network'],
    achievements: ['Popular Latin dance spot in Burgas'],
  },
  {
    id: 'burgas-ds-caribe',
    name: 'DS Caribe Burgas',
    description: 'DS Caribe offers quality instruction in Salsa, Bachata, and Kizomba in Burgas, creating a passionate dance community.',
    address: 'ул. "Св. св. Кирил и Методий" 24',
    city: 'Burgas',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/DSCaribeBurgas/', // Facebook
    phone: '+359 888 837 600',
    photoUrl: '/img/clubs/generic-club-photo-13.jpg',
    logoUrl: '/img/clubs/generic-logo-13.png',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba'],
    openingHours: { /* Typical evening class times */
      'Monday': { open: '18:30', close: '21:30' },
      'Tuesday': { open: '18:30', close: '21:30' },
      'Wednesday': { open: '18:30', close: '21:30' },
      'Thursday': { open: '18:30', close: '21:30' },
      'Friday': null,
      'Saturday': null,
      'Sunday': null,
    },
    featured: false,
    facilities: ['Dance studio', 'Experienced instructors', 'Social dance events'],
    achievements: ['Established school in Burgas', 'Focus on Latin dances'],
  },
  {
    id: 'burgas-incansable',
    name: 'Incansable Dance Studio',
    description: 'Incansable Dance Studio in Burgas provides Salsa and Bachata classes, aiming to share the joy of dance.',
    address: 'ул. "Пробуда" 26',
    city: 'Burgas',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/IncansableDanceStudio/', // Facebook
    phone: '+359 882 777 870',
    photoUrl: '/img/clubs/generic-club-photo-14.jpg',
    logoUrl: '/img/clubs/generic-logo-14.png',
    danceStyles: ['Salsa', 'Bachata'],
    openingHours: { /* Typical evening class times */
      'Monday': { open: '19:00', close: '21:00' },
      'Wednesday': { open: '19:00', close: '21:00' },
      // Check their FB for exact schedule
    },
    featured: false,
    facilities: ['Dance classes'],
    achievements: ['Promoting Latin dance in Burgas'],
  },
  {
    id: 'ruse-un-beso',
    name: 'Un Beso Ruse',
    description: 'The Ruse branch of Un Beso dance school, offering Salsa, Bachata, and Kizomba classes to the local community.',
    address: 'ул. "Райко Даскалов" 1, ет.3',
    city: 'Ruse',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/UnBesoRuse/', // Facebook
    phone: '+359 888 079 192', // Same as Sofia Un Beso
    photoUrl: '/img/clubs/generic-club-photo-15.jpg',
    logoUrl: '/img/clubs/generic-logo-15.png',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba'],
    openingHours: { /* Evening classes */
      'Tuesday': { open: '19:00', close: '21:00' },
      'Thursday': { open: '19:00', close: '21:00' },
    },
    featured: false,
    facilities: ['Dance instruction', 'Branch of Sofia school'],
    achievements: ['Bringing Latin dances to Ruse'],
  },
  {
    id: 'stara-zagora-estrella',
    name: 'Estrella Dance School',
    description: 'Estrella Dance School in Stara Zagora offers classes in Salsa and Bachata, fostering a local dance scene.',
    address: 'ул. "Христо Ботев" 121',
    city: 'Stara Zagora',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/EstrellaDanceSchool/', // Facebook
    phone: '+359 899 988 572',
    photoUrl: '/img/clubs/generic-club-photo-16.jpg',
    logoUrl: '/img/clubs/generic-logo-16.png',
    danceStyles: ['Salsa', 'Bachata'],
    openingHours: { /* Evening classes */
      'Monday': { open: '19:00', close: '21:00' },
      'Wednesday': { open: '19:00', close: '21:00' },
    },
    featured: false,
    facilities: ['Dance studio', 'Local classes'],
    achievements: ['Active in Stara Zagora'],
  },
  {
    id: 'veliko-tarnovo-flamingo',
    name: 'Salsa Club Flamingo VT',
    description: 'Salsa Club Flamingo brings Latin dance to Veliko Tarnovo with Salsa and Bachata classes.',
    address: 'ул. "Независимост" 17',
    city: 'Veliko Tarnovo',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/SalsaClubFlamingoVT/', // Facebook
    phone: '+359 889 287 569',
    photoUrl: '/img/clubs/generic-club-photo-17.jpg',
    logoUrl: '/img/clubs/generic-logo-17.png',
    danceStyles: ['Salsa', 'Bachata'],
    openingHours: { /* Evening classes */
      'Tuesday': { open: '19:00', close: '21:00' },
      'Thursday': { open: '19:00', close: '21:00' },
    },
    featured: false,
    facilities: ['Dance instruction'],
    achievements: ['Promoting Latin dance in Veliko Tarnovo'],
  },
  {
    id: 'blagoevgrad-suerte',
    name: 'Salsa Club Suerte',
    description: 'Salsa Club Suerte offers Salsa and Bachata classes in Blagoevgrad, creating a space for dance enthusiasts.',
    address: 'ул. "Тодор Александров" 25',
    city: 'Blagoevgrad',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/salsa.suerte/', // Facebook
    phone: '+359 897 957 111',
    photoUrl: '/img/clubs/generic-club-photo-18.jpg',
    logoUrl: '/img/clubs/generic-logo-18.png',
    danceStyles: ['Salsa', 'Bachata'],
    openingHours: { /* Evening classes */
      'Monday': { open: '19:30', close: '21:30' },
      'Wednesday': { open: '19:30', close: '21:30' },
    },
    featured: false,
    facilities: ['Dance classes'],
    achievements: ['Building a dance community in Blagoevgrad'],
  },
  {
    id: 'shumen-savena',
    name: 'Dance Club Savena',
    description: 'Dance Club Savena in Shumen offers various dance styles including Latin dances like Salsa. Focuses on both social and competitive dancing.',
    address: 'бул. "Симеон Велики" 48 (Младежки дом)',
    city: 'Shumen',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/DanceClubSavena/', // Facebook
    phone: '+359 899 857 837',
    photoUrl: '/img/clubs/generic-club-photo-19.jpg',
    logoUrl: '/img/clubs/generic-logo-19.png',
    danceStyles: ['Salsa', 'Cha Cha', 'Latin Dances', 'Standard Dances'],
    openingHours: { /* Check FB for specific Latin class times */
      'Monday': { open: '18:00', close: '21:00' },
      'Tuesday': { open: '18:00', close: '21:00' },
      'Wednesday': { open: '18:00', close: '21:00' },
      'Thursday': { open: '18:00', close: '21:00' },
      'Friday': { open: '18:00', close: '21:00' },
    },
    featured: false,
    facilities: ['Training hall', 'Competitive focus'],
    achievements: ['Participates in dance competitions'],
  },
  {
    id: 'salsa-club-al-compas-pleven',
    name: 'Salsa Club Al Compás',
    description: 'Latin dance club in Pleven featuring dance fitness, Zumba, salsa, bachata and kizomba classes for all levels.',
    address: 'Ploshtad Republika 2, Pleven',
    city: 'Pleven',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/profile.php?id=100049759402218',
    phone: '+359 89 664 4393',
    photoUrl: '/img/clubs/generic-club-photo-new.jpg',
    logoUrl: '/img/clubs/generic-club-logo-new.jpg',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba', 'Zumba'],
    openingHours: {},
    featured: false
  },
  {
    id: 'street-salseros-pleven',
    name: 'Salsa Club Street Salseros (Pleven)',
    description: 'Latin dance studio in Pleven offering salsa, bachata and cha-cha classes and hosting regular social dance events.',
    address: 'ul. Stefan Karadzha 20 (Chitalishte "Izvor"), Pleven',
    city: 'Pleven',
    country: 'Bulgaria',
    phone: '+359 885 724 778',
    photoUrl: '/img/clubs/generic-club-photo-new.jpg',
    logoUrl: '/img/clubs/generic-club-logo-new.jpg',
    danceStyles: ['Salsa', 'Bachata', 'Cha Cha'],
    openingHours: {},
    featured: false
  },
  {
    id: 'trifonov-dance-academy-pleven',
    name: 'Trifonov Dance Academy',
    description: 'Dance academy in Pleven (led by Trifon “Tito” Trifonov) offering Latin dance courses in salsa, bachata, kizomba, cha-cha and Zumba, alongside folk and modern dance programs.',
    address: 'ul. Vardar 1 (Raiffeisen Bank Bldg, City Garden side), Pleven',
    city: 'Pleven',
    country: 'Bulgaria',
    website: 'https://trifonov-dance.com',
    phone: '+359 877 808 063',
    photoUrl: '/img/clubs/generic-club-photo-new.jpg',
    logoUrl: '/img/clubs/generic-club-logo-new.jpg',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba', 'Cha Cha', 'Zumba'],
    openingHours: {},
    featured: false
  },
  {
    id: 'steffi-dance-latino-club-gabrovo',
    name: 'Steffi Dance Latino Club',
    description: 'The premier Latin dance school in Gabrovo, offering Cuban salsa (rueda de casino), bachata and other Latin dance classes for beginners and organizing local Latin parties.',
    address: 'ul. Orlovska 79A (Zala Body Art, bl. Dunav), Gabrovo',
    city: 'Gabrovo',
    country: 'Bulgaria',
    phone: '+359 894 686 804',
    photoUrl: '/img/clubs/generic-club-photo-new.jpg',
    logoUrl: '/img/clubs/generic-club-logo-new.jpg',
    danceStyles: ['Salsa', 'Bachata', 'Rueda'],
    openingHours: {},
    featured: false
  },
  {
    id: 'latin-dance-club-estrella-haskovo',
    name: 'Latin Dance Club Estrella',
    description: 'A popular Latin dance club in Haskovo, offering group classes in salsa, bachata, kizomba and cha-cha, with regular workshops and social dance parties.',
    address: 'Ploshtad Obshtinski 2, Haskovo',
    city: 'Haskovo',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/estrella.haskovo',
    phone: '+359 887 700 614',
    photoUrl: '/img/clubs/generic-club-photo-new.jpg',
    logoUrl: '/img/clubs/generic-club-logo-new.jpg',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba', 'Cha Cha'],
    openingHours: {},
    featured: false
  },
  {
    id: 'salsa-club-daniva-dimitrovgrad',
    name: 'Salsa Club Daniva',
    description: 'Latin dance club in Dimitrovgrad that introduced the local community to salsa and bachata, offering classes and social events to spread the passion of Latin dance.',
    address: 'ul. Hristo G. Danov 8, Dimitrovgrad',
    city: 'Dimitrovgrad',
    country: 'Bulgaria',
    photoUrl: '/img/clubs/generic-club-photo-new.jpg',
    logoUrl: '/img/clubs/generic-club-logo-new.jpg',
    danceStyles: ['Salsa', 'Bachata'],
    openingHours: {},
    featured: false
  },
  {
    id: 'just-el-dance-vratsa',
    name: 'Just El Dance Studio',
    description: 'A social dance studio in Vratsa that believes dance is for everyone, offering salsa, bachata and kizomba classes for beginners and advanced dancers and organizing monthly Latin parties.',
    address: 'ul. Polkovnik Lukashov 5 (Old Souvenir Palace), Vratsa',
    city: 'Vratsa',
    country: 'Bulgaria',
    website: 'https://www.facebook.com/JustElDance',
    phone: '+359 878 336 641',
    photoUrl: '/img/clubs/generic-club-photo-new.jpg',
    logoUrl: '/img/clubs/generic-club-logo-new.jpg',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba'],
    openingHours: {},
    featured: false
  },
  {
    id: 'salsa-club-desita-dobrich',
    name: 'Salsa Club Desita',
    description: 'Long-running Latin school led by Desislava “Desita” Todorova, offering Salsa, Bachata and Brazilian Zouk classes in Dobrich and neighbouring cities.',
    address: 'ul. “Nezavisimost” 5 (Luxor City Center)',
    city: 'Dobrich',
    country: 'Bulgaria',
    website: 'https://www.salsaclubdesita.com',
    phone: '+359 895 286 130',
    photoUrl: '/img/clubs/generic-club-photo-new.jpg',
    logoUrl: '/img/clubs/generic-club-logo-new.jpg',
    danceStyles: ['Salsa', 'Bachata', 'Zouk'],
    openingHours: {
      'Wednesday': { open: '17:30', close: '21:30' },
      'Friday': { open: '18:30', close: '21:30' }
    },
    featured: false,
    additionalLocations: [
      { name: 'Desita – Varna Branch', address: 'bul. “Tsar Osvoboditel” 263 (et. 2)', city: 'Varna', country: 'Bulgaria' },
      { name: 'Desita – Shumen Branch', address: 'ul. “Belasitsa” 1', city: 'Shumen', country: 'Bulgaria' },
      { name: 'Desita – Targovishte Branch', address: 'ul. “P. R. Slaveykov” 4 (NTS 210)', city: 'Targovishte', country: 'Bulgaria' }
    ]
  },
  {
    id: 'danza-de-pasion-yambol',
    name: 'Latino Club Danza de Pasión',
    description: 'Yambol’s leading Latin dance school (instructor Anelia Gospodinova) running Salsa, Bachata and Kizomba courses plus monthly parties.',
    address: 'Mladezhki Dom “G. Bratanov”, pl. Gradska Gradina (ul. Zlaten Rog 2)',
    city: 'Yambol',
    country: 'Bulgaria',
    website: 'https://danzadepasion.eu',
    phone: '+359 896 033 536',
    photoUrl: '/img/clubs/generic-club-photo-new.jpg',
    logoUrl: '/img/clubs/generic-club-logo-new.jpg',
    danceStyles: ['Salsa', 'Bachata', 'Kizomba'],
    openingHours: {
      'Tuesday': { open: '18:30', close: '21:00' },
      'Thursday': { open: '18:30', close: '21:00' }
    },
    featured: false
  }
];


const ClubsPage: React.FC = () => {
  const [clubs, setClubs] = useState<DanceClub[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedStyle, setSelectedStyle] = useState<string>('');
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const navigate = useNavigate();

  // Dance styles for filtering
  const danceStylesOptions = ['Salsa', 'Bachata', 'Kizomba', 'Zouk', 'Cha Cha', 'Merengue', 'Argentine Tango', 'Bulgarian Folk Dance'];

  // Locations for filtering - updated based on findings
  const locationOptions = [
    'Sofia', 'Plovdiv', 'Varna', 'Burgas', 'Ruse',
    'Stara Zagora', 'Veliko Tarnovo', 'Blagoevgrad', 'Shumen',
    'Pleven', 'Gabrovo', 'Dobrich', 'Haskovo', 'Yambol',
    'Dimitrovgrad', 'Vratsa'
  ].sort();


  const fetchClubs = useCallback(async () => {
    setLoading(true);
    try {
      logInfo('Fetching clubs data');
      // Simulate API call delay for mock data as well for consistent UX
      await new Promise(resolve => setTimeout(resolve, 500));

      // Fallback to mock data
      console.log('ClubsPage: Setting mock clubs data (updated list)');
      setClubs(mockClubsData);
      setError(null);
    } catch (err) {
      logError('Error fetching clubs', err);
      // Fallback to mock data in case of API error - for development
      console.log('ClubsPage: API error, setting mock clubs data (updated list)');
      setClubs(mockClubsData);
      setError(`Failed to load clubs. Displaying available data.`); // Informative error
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchClubs();
  }, [fetchClubs]);

  const filteredClubs = clubs.filter(club => {
    const matchesSearch = club.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      club.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (club.city && club.city.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStyle = selectedStyle === '' || club.danceStyles.includes(selectedStyle);
    const matchesLocation = selectedLocation === '' || club.city === selectedLocation;
    return matchesSearch && matchesStyle && matchesLocation;
  });

  const sortedClubs = [...filteredClubs].sort((a, b) => {
    // First, prioritize by priorityOrder if present
    if (a.priorityOrder !== undefined && b.priorityOrder !== undefined) {
      return a.priorityOrder - b.priorityOrder;
    }
    if (a.priorityOrder !== undefined && b.priorityOrder === undefined) {
      return -1; // a comes first
    }
    if (a.priorityOrder === undefined && b.priorityOrder !== undefined) {
      return 1; // b comes first
    }

    // Then, prioritize by featured status
    if (a.featured && !b.featured) return -1;
    if (!a.featured && b.featured) return 1;

    // Finally, sort by name
    return a.name.localeCompare(b.name);
  });

  const containerVariants = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.05 } } };
  const itemVariants = { hidden: { y: 20, opacity: 0 }, visible: { y: 0, opacity: 1, transition: { duration: 0.4, ease: 'easeOut' } } };

  const formatOpeningHours = (hours: OpeningHours | undefined) => {
    if (!hours) return 'Hours not available';
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
    const todayHours = hours[today];
    if (todayHours === undefined) return 'Hours not available for today'; // Explicitly check undefined
    if (todayHours === null) return `Closed today (${today})`;
    return `Open today: ${todayHours.open} - ${todayHours.close}`;
  };

  const handleViewDetails = (clubId: string) => {
    console.log('ClubsPage: Navigating to club details with ID:', clubId);
    navigate(`/clubs/${clubId}`);
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setSelectedStyle('');
    setSelectedLocation('');
  };

  return (
    <div className="bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 min-h-screen py-8 md:py-12 px-4 sm:px-6 lg:px-8 text-white">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <Link
            to="/"
            className="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white font-semibold rounded-lg shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-opacity-50"
          >
            Go to Welcome Page
          </Link>
        </div>
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
        >
          <h1 className="text-4xl sm:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-pink-300">
            Dance Clubs
          </h1>
          <p className="mt-4 text-xl text-gray-300 max-w-3xl mx-auto">
            Discover the best Latin dance venues in Bulgaria
          </p>
        </motion.div>

        <motion.form
          className="mb-8 bg-white/5 backdrop-blur-sm p-6 rounded-lg shadow-lg ring-1 ring-white/10"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          onSubmit={(e) => e.preventDefault()}
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 items-end">
            <div className="sm:col-span-2 lg:col-span-1">
              <label htmlFor="search-clubs" className="block text-sm font-medium text-gray-200 mb-1">
                Search Clubs
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                </div>
                <input
                  type="text"
                  name="search-clubs"
                  id="search-clubs"
                  className="focus:ring-pink-500 focus:border-pink-500 block w-full pl-10 sm:text-sm border-white/10 bg-white/5 rounded-md py-2 px-3 text-white placeholder-gray-400"
                  placeholder="Name, city, or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div>
              <label htmlFor="dance-style" className="block text-sm font-medium text-gray-200 mb-1">
                Dance Styles
              </label>
              <select
                id="dance-style"
                name="dance-style"
                className="focus:ring-pink-500 focus:border-pink-500 block w-full sm:text-sm border-white/10 bg-white/5 rounded-md py-2 px-3 text-white"
                value={selectedStyle}
                onChange={(e) => setSelectedStyle(e.target.value)}
              >
                <option value="" className="text-gray-700">All Styles</option>
                {danceStylesOptions.map(style => (
                  <option key={style} value={style} className="text-gray-700">{style}</option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-200 mb-1">
                Location
              </label>
              <select
                id="location"
                name="location"
                className="focus:ring-pink-500 focus:border-pink-500 block w-full sm:text-sm border-white/10 bg-white/5 rounded-md py-2 px-3 text-white"
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
              >
                <option value="" className="text-gray-700">All Locations</option>
                {locationOptions.map(loc => (
                  <option key={loc} value={loc} className="text-gray-700">{loc}</option>
                ))}
              </select>
            </div>
            <button
              type="button"
              onClick={handleClearFilters}
              className="w-full lg:w-auto justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-pink-600 hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 focus:ring-offset-gray-800"
            >
              Clear Filters
            </button>
          </div>
        </motion.form>

        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-pink-500"></div>
            {/* Or use your <LoadingSpinner /> component */}
          </div>
        ) : error && clubs.length === 0 ? ( // Show error only if no data could be loaded at all
          <div className="text-center py-10 bg-red-900/20 backdrop-blur-sm ring-1 ring-red-500/20 rounded-lg p-6">
            <p className="text-red-300 text-lg">Error: {error.replace('Failed to load clubs. Displaying available data.', '')}</p>
            <p className="text-yellow-300 text-md mt-2">Could not connect to server. Displaying cached data if available.</p>
            <button
              className="mt-6 px-6 py-2 bg-pink-600 text-white font-semibold rounded-md hover:bg-pink-700 transition-colors focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-opacity-50"
              onClick={fetchClubs}
            >
              Try Again
            </button>
          </div>
        ) : sortedClubs.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-xl text-gray-300">No clubs match your criteria. Try adjusting your filters.</p>
          </div>
        ) : (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {error && clubs.length > 0 && ( // Display non-blocking error if mock data is shown
              <div className="md:col-span-2 lg:col-span-3 text-center py-4 bg-yellow-800/30 backdrop-blur-sm ring-1 ring-yellow-600/30 rounded-lg p-4">
                <p className="text-yellow-200 text-sm">{error}</p>
              </div>
            )}
            {sortedClubs.map((club) => (
              <motion.article
                key={club.id}
                variants={itemVariants}
                className={`rounded-lg shadow-lg overflow-hidden transition-all duration-300 ease-in-out hover:shadow-2xl bg-white/5 backdrop-blur-sm ring-1 ring-white/10 hover:ring-white/20 ${club.featured ? 'ring-2 ring-pink-500' : ''}`}
              >
                <div className="relative">
                  <img
                    className="h-48 w-full object-cover"
                    src={club.photoUrl || 'https://via.placeholder.com/300x200?text=Club+Image'}
                    alt={club.name}
                  />
                  {club.featured && (
                    <div className="absolute top-2 right-2 bg-pink-500 text-white text-xs font-semibold px-2 py-1 rounded-full shadow-md">
                      Featured
                    </div>
                  )}
                  {club.logoUrl && (
                    <img
                      src={club.logoUrl}
                      alt={`${club.name} logo`}
                      className="absolute bottom-2 left-2 h-12 w-auto max-w-[100px] object-contain bg-black/30 backdrop-blur-sm p-1 rounded-md"
                    />
                  )}
                </div>

                <div className="p-5">
                  <h3 className="text-2xl font-bold text-white truncate mb-2">{club.name}</h3>
                  <p className="text-gray-300 text-sm mb-3 line-clamp-3 h-[3.75rem]">{club.description}</p>

                  <div className="space-y-2 text-sm mb-4">
                    <div className="flex items-center text-gray-300">
                      <MapPinIcon className="h-4 w-4 mr-2 text-teal-400 flex-shrink-0" />
                      <span>{club.address}, {club.city}</span>
                    </div>
                    {club.additionalLocations && club.additionalLocations.length > 0 && (
                      <div className="flex items-start text-gray-300 mt-1">
                        <MapPinIcon className="h-4 w-4 mr-2 text-pink-400 flex-shrink-0 mt-0.5" />
                        <span className="text-pink-300 font-medium">
                          {club.additionalLocations.length} more location{club.additionalLocations.length > 1 ? 's' : ''}
                        </span>
                      </div>
                    )}
                    {club.phone && (
                      <div className="flex items-center text-gray-300">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-teal-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        <span>{club.phone}</span>
                      </div>
                    )}
                    {club.website && (
                      <div className="flex items-center text-gray-300">
                        <GlobeAltIcon className="h-4 w-4 mr-2 text-teal-400 flex-shrink-0" />
                        <a href={club.website} target="_blank" rel="noopener noreferrer" className="hover:text-pink-400 truncate">
                          {club.website.replace(/^(https?:\/\/)?(www\.)?/, '').replace(/\/$/, '')}
                        </a>
                      </div>
                    )}
                    <div className="flex items-center text-gray-300">
                      <ClockIcon className="h-4 w-4 mr-2 text-teal-400 flex-shrink-0" />
                      <span>{formatOpeningHours(club.openingHours)}</span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-4">
                    {club.danceStyles.slice(0, 5).map(style => ( // Show up to 5 styles
                      <span key={style} className="inline-block px-2 py-0.5 bg-white/10 text-pink-300 text-xs rounded-full ring-1 ring-pink-500/30">
                        {style}
                      </span>
                    ))}
                    {club.danceStyles.length > 5 && (
                      <span className="inline-block px-2 py-0.5 bg-white/10 text-pink-300 text-xs rounded-full ring-1 ring-pink-500/30">
                        + {club.danceStyles.length - 5} more
                      </span>
                    )}
                  </div>

                  <div className="pt-4 border-t border-white/10 flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0 sm:space-x-2">
                    {club.website && (
                      <a
                        href={club.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full sm:w-auto justify-center text-center px-3 py-1.5 bg-white/20 text-white rounded hover:bg-white/30 transition-colors text-sm font-medium focus:outline-none focus:ring-2 focus:ring-pink-500"
                      >
                        Visit Website
                      </a>
                    )}
                    <button
                      onClick={() => handleViewDetails(club.id)}
                      className="w-full sm:w-auto justify-center text-center px-3 py-1.5 bg-pink-600 text-white rounded hover:bg-pink-700 transition-colors text-sm font-medium focus:outline-none focus:ring-2 focus:ring-pink-500"
                    >
                      View Details
                    </button>
                  </div>
                </div>
              </motion.article>
            ))}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default ClubsPage;