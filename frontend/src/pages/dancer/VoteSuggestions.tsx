import React, { useEffect, useState, useRef, useCallback, useMemo, memo } from 'react';
// Use namespace import for socket.io-client
// import * as SocketIOClient from 'socket.io-client'; // No longer needed here
import { logInfo, logError } from '../../utils/logger';
import { useWebSocket } from '../../context/WebSocketContext'; // Import the custom hook
// Import API functions
import { 
  getPublicSuggestions, 
  voteSuggestion, 
  unvoteSuggestion,
  getCurrentUser,
  getDanceStyles
} from '../../utils/api'; 
import toast from 'react-hot-toast'; // Import toast
import { FaThumbsUp, FaSpinner } from 'react-icons/fa'; // Re-added import
import { useAuthContext } from '../../context/AuthContext'; // Fixed: import the hook directly
import SuggestionItem from '../../components/dancer/SuggestionItem'; // Now this component exists

// Define SortOption locally
type SortOption = 'votes' | 'newest' | 'oldest';

// Reuse Suggestion interface
interface Suggestion {
  id: string;
  user?: { id: string; username: string };
  youtubeVideoId: string;
  title: string;
  channelTitle?: string;
  thumbnailUrl?: string;
  danceStyle: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  votes: number;
  isLocked?: boolean;
  createdAt: string;
  updatedAt?: string;
  hasVoted: boolean;
  durationSeconds?: number | null;
  voters: Array<{
    id: string;
    username: string;
    profile?: {
      displayName?: string;
      avatarUrl?: string;
    };
  }>;
}

// Define User interface
interface User {
    id: string;
    username: string;
    role: string;
    credits: number;
}

// Define props for VoteSuggestions - removed filterStyle prop
interface VoteSuggestionsProps {
  // No props needed anymore
}

// Workaround for TS2786: Assert FaSpinner as React.ElementType
const FaSpinnerIcon = FaSpinner as React.ElementType;

// Helper: Format relative time (e.g., "5 minutes ago")
const timeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const secondsPast = (now.getTime() - date.getTime()) / 1000;

  if (secondsPast < 60) return `${Math.floor(secondsPast)}s ago`;
  if (secondsPast < 3600) return `${Math.floor(secondsPast / 60)}m ago`;
  if (secondsPast <= 86400) return `${Math.floor(secondsPast / 3600)}h ago`;
  const days = Math.floor(secondsPast / 86400);
  if (days < 7) return `${days}d ago`;
  
  // For older dates, show the actual date
  const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric' };
  return date.toLocaleDateString(undefined, options);
};

// Helper to format duration
const formatDuration = (seconds: number | null | undefined): string => {
  if (seconds === null || seconds === undefined || seconds <= 0) return '--:--';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

interface SuggestionWithVoteStatus extends Suggestion {
  hasVoted: boolean;
}

const VoteSuggestions: React.FC<VoteSuggestionsProps> = () => {
  const { user: currentUser } = useAuthContext();
  const { socket, isConnected } = useWebSocket();
  const [suggestions, setSuggestions] = useState<SuggestionWithVoteStatus[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [voteLoading, setVoteLoading] = useState<Record<string, boolean>>({});
  const [sortBy, setSortBy] = useState<SortOption>('votes');

  // Function to fetch suggestions - removed style filtering
  const fetchSuggestions = useCallback(async (currentSortBy: SortOption) => {
    setIsLoading(true);
    setError(null);
    logInfo('VoteSuggestions API call: fetch suggestions', { data: { sortBy: currentSortBy, filterBy: 'othersSuggestions' }});
    try {
      const result = await getPublicSuggestions({
        sortBy: currentSortBy,
        filterBy: 'othersSuggestions' // Show all suggestions from other users
      });
      if (result.success && Array.isArray(result.data)) {
        setSuggestions(result.data);
        logInfo(`VoteSuggestions: Fetched ${result.data.length} suggestions`, { data: { sortBy: currentSortBy, filterBy: 'othersSuggestions' }});
        if (result.data.length === 0) {
            setError('No suggestions found matching the criteria.');
        }
      } else {
        throw new Error(result.message || 'Failed to fetch suggestions');
      }
    } catch (err: any) {
      setError(`Error fetching suggestions: ${err.message}`);
      logError('VoteSuggestions: Error fetching suggestions', err);
      toast.error(`Error fetching suggestions: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Effect to refetch when sort changes
  useEffect(() => {
    fetchSuggestions(sortBy);
  }, [sortBy, fetchSuggestions]);

  // Refetch suggestions after voting/unvoting
  const refetchAfterAction = (actionVerb: string) => {
      logInfo(`${actionVerb} successful: Triggering manual refetch of suggestions`, { data: { currentSortBy: sortBy } });
      fetchSuggestions(sortBy);
  };

  // Vote handler (unchanged core logic, uses refetchAfterAction)
  const handleVote = useCallback(async (suggestionId: string) => {
    if (!currentUser || voteLoading[suggestionId]) return;
    setVoteLoading(prev => ({ ...prev, [suggestionId]: true }));
    logInfo('VoteSuggestions: Vote attempt', { data: { suggestionId, userId: currentUser.id }});
    try {
      const result = await voteSuggestion(suggestionId);
      if (result.success) {
        toast.success('Vote counted!');
        // Update state optimistically or refetch
        refetchAfterAction('Vote');
      } else {
        toast.error(result.message || 'Failed to vote.');
        logError('VoteSuggestions: Vote failed', { data: { suggestionId, userId: currentUser.id, error: result.message }});
      }
    } catch (err: any) {
      toast.error('Vote failed: Network error.');
      logError('VoteSuggestions: Vote network error', err, { suggestionId, userId: currentUser.id });
    } finally {
      setVoteLoading(prev => ({ ...prev, [suggestionId]: false }));
    }
  // Ensure sortBy and fetchSuggestions are included if refetch relies on them
  }, [currentUser, voteLoading, sortBy, fetchSuggestions]); 

  // Unvote handler (unchanged core logic, uses refetchAfterAction)
  const handleUnvote = useCallback(async (suggestionId: string) => {
    if (!currentUser || voteLoading[suggestionId]) return;
    setVoteLoading(prev => ({ ...prev, [suggestionId]: true }));
    logInfo('VoteSuggestions: Unvote attempt', { data: { suggestionId, userId: currentUser.id }});
    try {
      const result = await unvoteSuggestion(suggestionId);
      if (result.success) {
        toast.success('Vote removed.');
        // Update state optimistically or refetch
        refetchAfterAction('Unvote');
      } else {
        toast.error(result.message || 'Failed to remove vote.');
        logError('VoteSuggestions: Unvote failed', { data: { suggestionId, userId: currentUser.id, error: result.message }});
      }
    } catch (err: any) {
      toast.error('Unvote failed: Network error.');
      logError('VoteSuggestions: Unvote network error', err, { suggestionId, userId: currentUser.id });
    } finally {
      setVoteLoading(prev => ({ ...prev, [suggestionId]: false }));
    }
  // Ensure sortBy and fetchSuggestions are included if refetch relies on them
  }, [currentUser, voteLoading, sortBy, fetchSuggestions]);


  // WebSocket listener (unchanged)
  useEffect(() => {
    if (socket && isConnected) {
      logInfo('VoteSuggestions: Setting up WebSocket listeners');

      const handleSuggestionUpdate = (updatedSuggestion: Suggestion) => {
        logInfo('VoteSuggestions: Received suggestion:updated via WS', updatedSuggestion);
        setSuggestions(prevSuggestions => {
          // If the updated suggestion matches the current style filter, update or add it
          const index = prevSuggestions.findIndex(s => s.id === updatedSuggestion.id);
          if (index !== -1) {
            // Update existing
            const newSuggestions = [...prevSuggestions];
            // Preserve hasVoted status if possible, recalculate based on new data if needed
            const existingHasVoted = newSuggestions[index].hasVoted;
            newSuggestions[index] = { ...updatedSuggestion, hasVoted: existingHasVoted }; // Apply update
            return newSuggestions;
          } else {
            // Add new suggestion if it's in a votable state (PENDING/APPROVED)
            if (updatedSuggestion.status === 'PENDING' || updatedSuggestion.status === 'APPROVED') {
               return [...prevSuggestions, { ...updatedSuggestion, hasVoted: false }]; // Add with hasVoted = false
            }
          }
          return prevSuggestions; // No change
        });
        // TODO: Consider re-sorting or re-fetching if the update could affect order
      };

      const handleSuggestionCreated = (newSuggestion: Suggestion) => {
          logInfo('VoteSuggestions: Received suggestion:created via WS', newSuggestion);
          // Check if the new suggestion matches the current view criteria
          if (newSuggestion.user?.id !== currentUser?.id) {
            logInfo('VoteSuggestions: Adding new suggestion from WS to view', newSuggestion);
            // Add with hasVoted = false, assuming user hasn't voted yet
            setSuggestions(prev => [...prev, { ...newSuggestion, hasVoted: false }]);
             // TODO: Re-sort or re-fetch might be needed depending on sort order
          }
      };

      const handleSuggestionVoteUpdate = (voteUpdate: { suggestionId: string; votes: number }) => {
          logInfo('VoteSuggestions: Received suggestion:vote via WS', voteUpdate);
          setSuggestions(prevSuggestions =>
              prevSuggestions.map(s =>
                  s.id === voteUpdate.suggestionId ? { ...s, votes: voteUpdate.votes } : s
              )
          );
          // Consider re-sorting if sorted by votes
          if (sortBy === 'votes') {
             // Debounce or simply trigger a sort on the existing data or refetch
             // Simple refetch for now to ensure order:
             // fetchSuggestions(sortBy);
          }
      };

      socket.on('suggestion:updated', handleSuggestionUpdate);
      socket.on('suggestion:created', handleSuggestionCreated);
      socket.on('suggestion:vote', handleSuggestionVoteUpdate);

      return () => {
        logInfo('VoteSuggestions: Cleaning up WebSocket listeners');
        socket.off('suggestion:updated', handleSuggestionUpdate);
        socket.off('suggestion:created', handleSuggestionCreated);
        socket.off('suggestion:vote', handleSuggestionVoteUpdate);
      };
    }
  }, [socket, isConnected, sortBy, currentUser]);

  return (
    <div className="w-full">
      <div className="bg-gradient-to-r from-primary-500 to-accent-500 p-4 sm:p-6 rounded-t-lg shadow-md">
        <h2 className="text-2xl sm:text-3xl font-bold text-white text-center tracking-tight">
          Vote on Suggestions
        </h2>
      </div>

      <div className="bg-white p-4 rounded-b-lg shadow-md border border-gray-200">
        {/* Filters Section */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 px-2 py-3 bg-gray-50 rounded-md border">
          {/* Sort Dropdown */}
          <div className="flex items-center">
            <label htmlFor="sort" className="text-sm font-medium text-gray-700 mr-2 whitespace-nowrap">Sort by:</label>
            <select
              id="sort"
              value={sortBy}
              onChange={(e) => {
                const newValue = e.target.value as SortOption;
                logInfo('VoteSuggestions: sortBy select changed', { data: { newValue } });
                setSortBy(newValue);
              }}
              className="block w-full pl-3 pr-8 py-1.5 text-base border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="votes">Popularity</option>
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
            </select>
          </div>
        </div>

        {/* Suggestions List */}
        <div className="space-y-3">
          {isLoading && (
            <div className="flex justify-center items-center py-10">
              <FaSpinnerIcon className="animate-spin h-6 w-6 text-primary-600 mr-3" />
              <span className="text-gray-500">Loading suggestions...</span>
            </div>
          )}
          {error && !isLoading && (
             <div className="text-center py-6 px-4 bg-yellow-50 border border-yellow-200 rounded-md">
                 <p className="text-sm text-yellow-700">{error}</p>
                 {/* Optional: Add a retry button */}
                 <button 
                     onClick={() => fetchSuggestions(sortBy)}
                     className="mt-2 px-3 py-1 text-xs font-medium text-white bg-primary-600 rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-primary-500"
                 >
                     Retry
                 </button>
             </div>
          )}
          {!isLoading && !error && suggestions.length === 0 && (
            <p className="text-center text-gray-500 py-6">No suggestions available to vote on.</p>
          )}
          {!isLoading && !error && suggestions.map((suggestion) => (
            <SuggestionItem
              key={suggestion.id}
              suggestion={suggestion}
              onVote={handleVote}
              onUnvote={handleUnvote}
              currentUserId={currentUser?.id || null}
              voteLoading={voteLoading}
              voters={suggestion.voters}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default VoteSuggestions; 