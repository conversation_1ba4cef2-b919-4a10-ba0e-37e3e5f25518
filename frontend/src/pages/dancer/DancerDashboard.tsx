import React, { useState, useEffect, useRef, useCallback, FC, ReactNode } from 'react';
import { Routes, Route, NavLink, useLocation, useParams, Link } from 'react-router-dom';
import { useWebSocket } from '../../context/WebSocketContext';
import { useAuthContext } from '../../context/AuthContext';
import SuggestSong from './SuggestSong';
import VoteSuggestions from './VoteSuggestions';
import Leaderboard from '../../components/dancer/Leaderboard';
import TopVotedUsers from '../../components/dancer/TopVotedUsers';
import CurrentPlaying from '../../components/dancer/CurrentPlaying';
import StyleRotationDisplay from '../../components/dancer/StyleRotationDisplay';
import DanceStyleDiscoveries from '../../components/dancer/DanceStyleDiscoveries';
import SongTrendInsights from '../../components/dancer/SongTrendInsights';
import SongSearch from '../../components/dancer/SongSearch';
import {
  MusicalNoteIcon,
  HandThumbUpIcon,
  PlusCircleIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  ArrowPathIcon,
  UsersIcon,
  ChevronRightIcon,
  ArrowLeftIcon,
  ChartBarIcon,
  UserMinusIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';
import { getActiveTemplateRotation, getUserDisplayProfile, getMyFavoriteDancers, addFavoriteDancer, removeFavoriteDancer, getUserSuggestions, voteSuggestion, unvoteSuggestion } from '../../utils/api';
import { logInfo, logError } from '../../utils/logger';
import toast from 'react-hot-toast';

// --- Add Playlist State Interface --- 
interface CurrentSong {
  title: string;
  youtubeVideoId: string;
  durationSeconds: number;
  startedAt: string;
}

interface PlaylistState {
  currentSong: CurrentSong | null;
  isPlaying: boolean;
}

// --- Rotation State Interface ---
interface RotationBlock {
  style: string;
  count: number;
}

// Added DisplayProfileData interface definition
export interface DisplayProfileData {
  displayName: string | null;
  avatarUrl: string | null;
}

interface FavoriteDancerDisplayItem {
  id: string;
  username: string;
  displayName: string | null;
  avatarUrl: string | null;
}

interface UserProfileProps {
  userInfo: any;
  displayProfile: DisplayProfileData | null;
  onAvatarUpdate: () => void;
  isOwnProfile?: boolean;
  targetUserId?: string;
}

const UserProfile: FC<UserProfileProps> = ({
  userInfo,
  displayProfile,
  onAvatarUpdate,
  isOwnProfile = true,
  targetUserId
}) => {
  const [favoriteDancers, setFavoriteDancers] = useState<FavoriteDancerDisplayItem[]>([]);
  const [loadingFavorites, setLoadingFavorites] = useState(false);

  const fetchFavoriteDancersList = useCallback(async () => {
    if (!isOwnProfile || !userInfo) return;
    setLoadingFavorites(true);
    try {
      const result = await getMyFavoriteDancers();
      if (result.success && Array.isArray(result.data)) {
        setFavoriteDancers(result.data);
        logInfo('UserProfile: Fetched favorite dancers list', { userId: userInfo.id, count: result.data.length });
      } else {
        logError('UserProfile: Failed to fetch favorite dancers', { userId: userInfo.id, message: result.message });
        setFavoriteDancers([]);
      }
    } catch (err) {
      logError('UserProfile: Error fetching favorite dancers', { userId: userInfo.id, error: err });
      setFavoriteDancers([]);
    } finally {
      setLoadingFavorites(false);
    }
  }, [isOwnProfile, userInfo]);

  useEffect(() => {
    if (isOwnProfile) {
      fetchFavoriteDancersList();
    }
  }, [isOwnProfile, fetchFavoriteDancersList]);

  const handleUnfavoriteDancer = async (dancerIdToUnfavorite: string) => {
    if (!isOwnProfile) return;
    logInfo('UserProfile: Attempting to unfavorite dancer', { currentUserId: userInfo.id, dancerIdToUnfavorite });
    const result = await removeFavoriteDancer(dancerIdToUnfavorite);
    if (result.success) {
      toast.success('Dancer removed from favorites.');
      fetchFavoriteDancersList();
    } else {
      toast.error(result.message || 'Failed to remove favorite.');
      logError('UserProfile: Failed to unfavorite dancer', { message: result.message });
    }
  };

  const renderUserProfileHeader = () => {
    return (
      <div className="text-center mb-3">
        {displayProfile?.avatarUrl && <img src={displayProfile.avatarUrl} alt="avatar" className="w-20 h-20 rounded-full mx-auto mb-2 border-2 border-gray-300 shadow-sm" />}
        <h3 className="text-lg font-semibold text-gray-800">{displayProfile?.displayName || userInfo?.username}</h3>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center p-4 bg-white rounded-lg shadow-md border border-gray-200 space-y-4">
      {renderUserProfileHeader()}

      {/* Favorite Dancers Section - Only show for own profile */}
      {isOwnProfile && (
        <div className="w-full">
          <h4 className="text-sm font-semibold text-gray-700 mb-2 text-center border-b pb-2">
            <UsersIcon className="h-4 w-4 inline-block mr-1" />
            Favorite Dancers
          </h4>
          {loadingFavorites ? (
            <p className="text-xs text-gray-500 text-center py-2">Loading favorites...</p>
          ) : favoriteDancers.length > 0 ? (
            <div className="space-y-2">
              {favoriteDancers.map((dancer) => (
                <div
                  key={dancer.id}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded-lg text-sm"
                >
                  <Link
                    to={`/dancer/profile/${dancer.id}`}
                    className="flex items-center flex-1 min-w-0"
                  >
                    {dancer.avatarUrl ? (
                      <img
                        src={dancer.avatarUrl}
                        alt="avatar"
                        className="w-6 h-6 rounded-full mr-2 border border-gray-200"
                      />
                    ) : (
                      <div className="w-6 h-6 rounded-full mr-2 bg-gray-200 flex items-center justify-center text-gray-500 text-xs">
                        ?
                      </div>
                    )}
                    <span className="truncate text-gray-700 hover:text-primary-600">
                      {dancer.displayName || dancer.username}
                    </span>
                  </Link>
                  <button
                    onClick={() => handleUnfavoriteDancer(dancer.id)}
                    className="ml-2 text-gray-400 hover:text-red-500"
                    title="Remove from favorites"
                  >
                    <UserMinusIcon className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-xs text-gray-500 text-center py-2">No favorite dancers yet.</p>
          )}
        </div>
      )}
    </div>
  );
};

const DancerDashboard: FC = () => {
  const { user: authUser, isLoading: authLoading } = useAuthContext();
  const { socket, isConnected } = useWebSocket();

  const [currentView, setCurrentView] = useState<'suggest' | 'vote'>('vote');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userDisplayProfile, setUserDisplayProfile] = useState<DisplayProfileData | null>(null);
  const [playlistState, setPlaylistState] = useState<PlaylistState>({ currentSong: null, isPlaying: false });
  const [rotation, setRotation] = useState<RotationBlock[]>([]);
  const [rotationError, setRotationError] = useState<string | null>(null);

  const refetchUserProfile = useCallback(async () => {
    if (!authUser?.id) return;
    logInfo('DancerDashboard: Refetching user display profile data...');
    try {
      const profileResult = await getUserDisplayProfile();
      if (profileResult.success && profileResult.data) {
        setUserDisplayProfile(profileResult.data);
        logInfo('DancerDashboard: User display profile refetched', profileResult.data);
      } else {
        logError('DancerDashboard: Failed to refetch user display profile', { message: profileResult.message });
      }
    } catch (err) {
      logError('DancerDashboard: Error refetching user display profile', err);
    }
  }, [authUser?.id]);

  useEffect(() => {
    const fetchPageSpecificData = async () => {
      if (!authUser) {
        setLoading(false);
        return;
      }
      setLoading(true);
      setError(null);
      setRotationError(null);
      logInfo('DancerDashboard: Fetching page-specific data');

      try {
        const results = await Promise.allSettled([
          getUserDisplayProfile(),
          getActiveTemplateRotation()
        ]);

        const displayProfileResult = results[0];
        const rotationResult = results[1];

        if (displayProfileResult.status === 'fulfilled' && displayProfileResult.value.success) {
          setUserDisplayProfile(displayProfileResult.value.data);
        } else {
          const message = displayProfileResult.status === 'fulfilled' ? displayProfileResult.value.message : displayProfileResult.reason?.message;
          logError('DancerDashboard: Failed to fetch user display profile', { message });
        }

        if (rotationResult.status === 'fulfilled' && rotationResult.value.success) {
          const rotationData = rotationResult.value.data;
          if (Array.isArray(rotationData)) {
            setRotation(rotationData);
          } else {
            logError('DancerDashboard: Rotation data is not an array', { data: rotationData });
            setRotation([]);
          }
        } else {
          const message = rotationResult.status === 'fulfilled' ? rotationResult.value.message : rotationResult.reason?.message;
          logError('DancerDashboard: Failed to fetch rotation', { message });
          setRotation([]);
          setRotationError(message || 'Failed to load style rotation.');
        }

      } catch (err: any) {
        logError('DancerDashboard: Unexpected error during page-specific data fetch', err);
        setError('An unexpected error occurred.');
      } finally {
        setLoading(false);
      }
    };

    if (!authLoading) {
      fetchPageSpecificData();
    }
  }, [authLoading, authUser]);

  // WebSocket Listener Effect
  useEffect(() => {
    if (socket && isConnected) {
      logInfo('DancerDashboard: Setting up shared socket listeners');

      const handlePlayerStateUpdate = (data: any) => {
        setPlaylistState(data);
      };

      const handleConnect = () => {
        logInfo('DancerDashboard: WebSocket connected (reconnected)', { socketId: socket.id });
      };

      socket.on('player:state:update', handlePlayerStateUpdate);
      socket.on('connect', handleConnect);
      socket.on('reconnect', handleConnect);

      return () => {
        logInfo('DancerDashboard: Cleaning up shared socket listeners');
        socket.off('player:state:update', handlePlayerStateUpdate);
        socket.off('connect', handleConnect);
        socket.off('reconnect', handleConnect);
      };
    }
  }, [socket, isConnected]);

  const handleVote = async (songId: string): Promise<void> => {
    try {
      await voteSuggestion(songId);
      logInfo('DancerDashboard: Vote successful', { songId });
    } catch (err: any) {
      logError('DancerDashboard: Vote failed', err);
      throw err;
    }
  };

  const handleUnvote = async (songId: string): Promise<void> => {
    try {
      await unvoteSuggestion(songId);
      logInfo('DancerDashboard: Unvote successful', { songId });
    } catch (err: any) {
      logError('DancerDashboard: Unvote failed', err);
      throw err;
    }
  };

  if (authLoading || loading) {
    return <div className="flex justify-center items-center h-screen"><p>Loading dashboard...</p></div>;
  }

  if (!authUser) {
    return <div className="flex justify-center items-center h-screen"><p>Please log in to view the dashboard.</p></div>;
  }

  if (error) {
    return <div className="text-red-500 p-4">Error loading dashboard data: {error}</div>;
  }

  return (
    <div className="px-4 py-6 md:p-8 max-w-5xl mx-auto">
      <div className="mb-8 grid grid-cols-1 gap-5">
        {/* Header Section with User Info */}
        <div className="bg-white shadow-sm p-4 sm:p-6 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">
                Welcome, {userDisplayProfile?.displayName || authUser?.username || 'Dancer'}!
              </h1>
              <p className="text-sm text-gray-500">Suggest songs, vote, and climb the leaderboard.</p>
            </div>
            <div className="flex flex-col sm:flex-row items-center gap-4">
              {/* User Profile Component */}
              <UserProfile
                userInfo={authUser}
                displayProfile={userDisplayProfile}
                onAvatarUpdate={refetchUserProfile}
              />
            </div>
          </div>

          {/* Quick Actions Section */}
          <div className="bg-white shadow-sm rounded-xl p-4 mb-4">
            <h2 className="text-lg font-semibold mb-3">Quick Actions</h2>
            <div className="flex flex-wrap gap-2">
              <Link
                to="/charts"
                className="flex items-center px-3 py-2 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
              >
                <ChartBarIcon className="h-5 w-5 mr-1" />
                <span>Charts</span>
              </Link>

              <Link
                to="/leaderboard/all"
                className="flex items-center px-3 py-2 bg-amber-50 text-amber-700 rounded-lg hover:bg-amber-100 transition-colors"
              >
                <TrophyIcon className="h-5 w-5 mr-1" />
                <span>Leaderboard</span>
              </Link>

              <Link
                to="/dancer/suggestion-history"
                className="flex items-center px-3 py-2 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"
              >
                <ClockIcon className="h-5 w-5 mr-1" />
                <span>History</span>
              </Link>
            </div>
          </div>

          {/* Main Action Buttons */}
          <div className="px-4 sm:px-6 py-3 bg-white border-b border-gray-200">
            <div className="flex justify-around items-center gap-4">
              <button
                onClick={() => setCurrentView('suggest')}
                className={`flex-1 flex flex-col items-center justify-center gap-1 py-3 px-4 rounded-lg text-sm sm:text-base font-medium transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-1
                   ${currentView === 'suggest'
                    ? 'bg-primary-600 text-white shadow-sm hover:bg-primary-700'
                    : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
                  }
                 `}
                disabled={authLoading}
              >
                <PlusCircleIcon className="h-6 w-6" />
                <span className="text-center">Suggest Song</span>
              </button>
              
              <button
                onClick={() => setCurrentView('vote')}
                className={`flex-1 flex flex-col items-center justify-center gap-1 py-3 px-4 rounded-lg text-sm sm:text-base font-medium transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-secondary-400 focus:ring-offset-1
                   ${currentView === 'vote'
                    ? 'bg-primary-600 text-white shadow-sm hover:bg-primary-700'
                    : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
                  }
                 `}
                disabled={authLoading || loading}
              >
                <HandThumbUpIcon className="h-6 w-6" />
                <span className="text-center">Vote on Songs</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <main className="flex-grow px-0 py-4 sm:p-6">
          <div className="mb-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 bg-white rounded-xl shadow-md p-4 sm:p-6">
              <div className="space-y-4">
                <SongSearch
                  onVote={handleVote}
                  onUnvote={handleUnvote}
                />

                {currentView === 'suggest' ? (
                  <SuggestSong />
                ) : (
                  <VoteSuggestions />
                )}
              </div>
            </div>
            <div className="lg:col-span-1 space-y-6">
              <div className="bg-white rounded-xl shadow-md p-4 sm:p-6">
                {!loading && (
                  <>
                    <Leaderboard />
                    <TopVotedUsers limit={5} className="mt-6" />
                  </>
                )}
              </div>
              {rotation && rotation.length > 0 && (
                <div className="bg-white rounded-xl shadow-md p-4 sm:p-6">
                  <StyleRotationDisplay rotation={rotation} error={rotationError} isLoading={loading} />
                </div>
              )}
            </div>
          </div>
        </main>
      </div>

      {/* Add conditional rendering for DanceStyleDiscoveries too */}
      {!loading && <DanceStyleDiscoveries />}

      {/* Add conditional rendering for SongTrendInsights */}
      {!loading && <SongTrendInsights className="mt-8" />}

      {/* Dance Battle Hero Feature */}
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 my-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-amber-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l-9-5 9-5 9 5-9 5z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14v10" />
            </svg>
            <h3 className="text-lg font-bold text-amber-800">Dance Battle Heroes</h3>
          </div>
          <Link 
            to="/dancer/dance-hero"
            className="px-4 py-2 bg-amber-600 hover:bg-amber-700 text-white font-semibold rounded-md shadow-sm transition-colors"
          >
            Play Now
          </Link>
        </div>
        <p className="text-amber-700 mt-2">
          Create your dance hero, battle other dancers, and complete quests to level up your skills!
        </p>
      </div>
    </div>
  );
};

export default DancerDashboard; 