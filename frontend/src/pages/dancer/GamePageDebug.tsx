import React, { useState, useEffect } from 'react';

const GamePageDebug: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [hero, setHero] = useState<any>(null);
  const [quests, setQuests] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Starting data fetch...');
        
        // Test hero fetch
        const heroResponse = await fetch('/api/game/hero', { credentials: 'include' });
        const heroData = await heroResponse.json();
        console.log('Hero data:', heroData);
        
        if (heroData.success) {
          setHero(heroData.hero);
        }
        
        // Test quests fetch
        const questsResponse = await fetch('/api/game/quests', { credentials: 'include' });
        const questsData = await questsResponse.json();
        console.log('Quests data:', questsData);
        
        if (questsData.success) {
          setQuests(questsData.quests);
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error in fetchData:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <div>Loading debug page...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="p-4">
      <h1>Game Debug Page</h1>
      
      <div className="mb-4">
        <h2>Hero Data:</h2>
        <pre className="bg-gray-100 p-2 text-xs overflow-auto">
          {JSON.stringify(hero, null, 2)}
        </pre>
      </div>
      
      <div className="mb-4">
        <h2>Quests Data:</h2>
        <pre className="bg-gray-100 p-2 text-xs overflow-auto">
          {JSON.stringify(quests, null, 2)}
        </pre>
      </div>
      
      <div className="mb-4">
        <h2>Quest Rendering Test:</h2>
        {quests && quests.length > 0 ? (
          quests.map((quest, index) => (
            <div key={quest.id || index} className="border p-2 mb-2">
              <div>Title: {quest.title || 'No title'}</div>
              <div>Progress: {quest.progress || 0} / {quest.targetValue || 0}</div>
              <div>Target Value: {quest.targetValue}</div>
              <div>Type: {quest.type}</div>
            </div>
          ))
        ) : (
          <div>No quests found</div>
        )}
      </div>
    </div>
  );
};

export default GamePageDebug; 