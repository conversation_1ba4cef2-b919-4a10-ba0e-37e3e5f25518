// Dance Battle Heroes Game Page - Cache Bust: 2025-05-24T06:05:00Z
import React, { useState, useEffect, useCallback } from 'react';
import { useAuthContext } from '../../context/AuthContext';
import { useWebSocket } from '../../context/WebSocketContext';
import WorldMap from '../../components/dancer/battle/WorldMap';
import ComboTrainer from '../../components/dancer/battle/ComboTrainer';
import {
  Sword,
  Shield,
  Zap,
  Heart,
  Star,
  Trophy,
  ShoppingBag,
  Target,
  Crown,
  Flame,
  Music,
  Users,
  Award,
  TrendingUp,
  Clock,
  Gift,
  Package,
  Sparkles,
  Map,
  Gamepad2,
  Edit,
  ShoppingCart,
  RotateCcw,
  Bot
} from 'lucide-react';

// Simple UI Components using Tailwind CSS
const Card: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = '' }) => (
  <div className={`bg-white rounded-lg shadow-md border border-gray-200 ${className}`}>
    {children}
  </div>
);

const CardHeader: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="px-6 py-4 border-b border-gray-200">
    {children}
  </div>
);

const CardTitle: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = '' }) => (
  <h3 className={`text-lg font-semibold text-gray-900 ${className}`}>
    {children}
  </h3>
);

const CardContent: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = '' }) => (
  <div className={`px-6 py-4 ${className}`}>
    {children}
  </div>
);

const Button: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'secondary';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}> = ({
  children,
  onClick,
  disabled = false,
  variant = 'default',
  size = 'default',
  className = ''
}) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';

  const variantClasses = {
    default: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',
    secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-500'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    default: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
    >
      {children}
    </button>
  );
};

const Badge: React.FC<{
  children: React.ReactNode;
  variant?: 'default' | 'outline' | 'secondary';
  className?: string;
}> = ({ children, variant = 'default', className = '' }) => {
  const variantClasses = {
    default: 'bg-blue-100 text-blue-800',
    outline: 'border border-gray-300 text-gray-700',
    secondary: 'bg-gray-100 text-gray-800'
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variantClasses[variant]} ${className}`}>
      {children}
    </span>
  );
};

const Progress: React.FC<{ value: number; className?: string }> = ({ value, className = '' }) => (
  <div className={`w-full bg-gray-200 rounded-full ${className}`}>
    <div
      className="bg-blue-600 h-full rounded-full transition-all duration-300"
      style={{ width: `${Math.min(100, Math.max(0, value))}%` }}
    />
  </div>
);

const Tabs: React.FC<{
  value: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => (
  <div className={`${className}`}>
    {children}
  </div>
);

const TabsList: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => (
  <div className={`grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 p-4 bg-gray-100 rounded-lg ${className}`}>
    {children}
  </div>
);

const TabsTrigger: React.FC<{
  value: string;
  children: React.ReactNode;
  className?: string;
  activeTab: string;
  onTabChange: (value: string) => void;
}> = ({ value, children, className = '', activeTab, onTabChange }) => (
  <button
    onClick={() => onTabChange(value)}
    className={`flex flex-col items-center justify-center gap-2 p-4 text-center transition-all rounded-lg border-2 min-h-[80px] ${
      activeTab === value
        ? 'bg-white text-gray-900 shadow-lg border-blue-500 scale-105'
        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 border-gray-300 hover:border-gray-400'
    } ${className}`}
  >
    {children}
  </button>
);

const TabsContent: React.FC<{
  value: string;
  children: React.ReactNode;
  className?: string;
  activeTab: string;
}> = ({ value, children, className = '', activeTab }) => {
  if (activeTab !== value) return null;
  return <div className={`mt-6 ${className}`}>{children}</div>;
};

interface Hero {
  id: string;
  name: string;
  level: number;
  experience: number;
  experienceToNext: number;
  energy: number;
  health: number;
  mana: number;
  attack: number;
  defense: number;
  speed: number;
  luck: number;
  primaryStyle: string;
  secondaryStyle?: string;
  stylePoints: Record<string, number>;
  skills: Record<string, number>;
  traits: string[];
  wins: number;
  losses: number;
  draws: number;
  winStreak: number;
  globalRank?: number;
  styleRanks: Record<string, number>;
  trophies: number;
  titles: string[];
  skillPoints: number;
  traitPoints: number;
  coins: number;
}

interface BattleMove {
  id: string;
  name: string;
  description: string;
  danceStyle: string;
  power: number;
  accuracy: number;
  speed: number;
  energyCost: number;
  manaCost: number;
  unlockLevel: number;
  rarity: string;
  effects: string[];
  audioFile?: string;
}

interface Combo {
  id: string;
  name: string;
  description: string;
  danceStyle: string;
  sequence: string[];
  beatCount: number;
  timingWindow: number;
  difficulty: number;
  power: number;
  energyCost: number;
  manaCost?: number;
  unlockLevel: number;
  masteryLevel: number;
  usageCount: number;
}

interface Equipment {
  id: string;
  name: string;
  description: string;
  type: string;
  rarity: string;
  price: number;
  statBonuses: Record<string, number>;
  skillBonuses: Record<string, number>;
  specialEffects: string[];
  requiredLevel: number;
  danceStyle?: string;
}

interface Quest {
  id: string;
  title: string;
  description: string;
  type: string;
  category: string;
  targetValue: number;
  progress: number;
  completed: boolean;
  claimedAt?: string;
  rewards: {
    experience?: number;
    coins?: number;
    skillPoints?: number;
    traitPoints?: number;
    equipment?: string[];
  };
  expiresAt?: string;
}

interface Battle {
  id: string;
  status: string;
  winner?: string;
  round?: number;
  opponent?: {
    id: string;
    name: string;
    level: number;
    health: number;
  };
}

const GamePage: React.FC = () => {
  const { user } = useAuthContext();
  const { socket } = useWebSocket();

  // State management
  const [hero, setHero] = useState<Hero | null>(null);
  const [battleMoves, setBattleMoves] = useState<BattleMove[]>([]);
  const [combos, setCombos] = useState<Combo[]>([]);
  const [opponents, setOpponents] = useState<any[]>([]);
  const [equipment, setEquipment] = useState<Equipment[]>([]);
  const [inventory, setInventory] = useState<Equipment[]>([]);
  const [quests, setQuests] = useState<Quest[]>([]);
  const [leaderboard, setLeaderboard] = useState<any[]>([]);
  const [currentBattle, setCurrentBattle] = useState<Battle | null>(null);
  const [selectedMove, setSelectedMove] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('hero');
  const [editingHeroName, setEditingHeroName] = useState(false);
  const [newHeroName, setNewHeroName] = useState('');
  const [discoveredVenues, setDiscoveredVenues] = useState<string[]>([]);
  const [activeBattle, setActiveBattle] = useState<any>(null);
  const [battleResult, setBattleResult] = useState<any>(null);
  const [selectedCombo, setSelectedCombo] = useState<any>(null);
  const [comboSequence, setComboSequence] = useState<string[]>([]);
  const [timingAccuracy, setTimingAccuracy] = useState<number>(85);
  const [isTimingActive, setIsTimingActive] = useState(false);
  const [lastHitAccuracy, setLastHitAccuracy] = useState<string | null>(null);
  const [lastHitTiming, setLastHitTiming] = useState<number | null>(null);
  const [comboAccuracy, setComboAccuracy] = useState(0);
  const [speedMultiplier, setSpeedMultiplier] = useState(1);
  const [comboMultiplier, setComboMultiplier] = useState(1);
  const [calculatedDamage, setCalculatedDamage] = useState(0);
  const [battleStats, setBattleStats] = useState({
    perfect: 0,
    great: 0,
    good: 0,
    totalScore: 0,
    averageAccuracy: 0,
    coinsEarned: 0
  });

  // Fetch hero data
  const fetchHero = useCallback(async () => {
    try {
      const response = await fetch('/api/game/hero', {
        credentials: 'include'
      });
      const data = await response.json();

      if (data.success) {
        setHero(data.hero);
      } else {
        setError(data.error || 'Failed to load hero');
      }
    } catch (err) {
      setError('Failed to connect to server');
    }
  }, []);

  // Fetch battle moves
  const fetchBattleMoves = useCallback(async () => {
    try {
      const response = await fetch('/api/game/battle/moves', {
        credentials: 'include'
      });
      const data = await response.json();

      if (data.success) {
        setBattleMoves(data.moves);
      }
    } catch (err) {
      console.error('Failed to fetch battle moves:', err);
    }
  }, []);

  // Fetch combos
  const fetchCombos = useCallback(async () => {
    try {
      const response = await fetch('/api/game/combos', {
        credentials: 'include'
      });
      const data = await response.json();

      if (data.success) {
        setCombos(data.combos);
      }
    } catch (err) {
      console.error('Failed to fetch combos:', err);
    }
  }, []);

  // Fetch AI opponents
  const fetchOpponents = useCallback(async () => {
    try {
      const response = await fetch('/api/game/battle/opponents', {
        credentials: 'include'
      });
      const data = await response.json();

      if (data.success) {
        setOpponents(data.opponents);
      }
    } catch (err) {
      console.error('Failed to fetch opponents:', err);
    }
  }, []);

  // Fetch equipment shop
  const fetchEquipment = useCallback(async () => {
    try {
      const response = await fetch('/api/game/equipment/shop', {
        credentials: 'include'
      });
      const data = await response.json();

      if (data.success) {
        setEquipment(data.equipment);
      }
    } catch (err) {
      console.error('Failed to fetch equipment:', err);
    }
  }, []);

  // Fetch inventory
  const fetchInventory = useCallback(async () => {
    try {
      const response = await fetch('/api/game/equipment/inventory', {
        credentials: 'include'
      });
      const data = await response.json();

      if (data.success) {
        setInventory(data.equipment);
      }
    } catch (err) {
      console.error('Failed to fetch inventory:', err);
    }
  }, []);

  // Fetch quests
  const fetchQuests = useCallback(async () => {
    try {
      const response = await fetch('/api/game/quests', {
        credentials: 'include'
      });
      const data = await response.json();

      if (data.success) {
        setQuests(data.quests);
      }
    } catch (err) {
      console.error('Failed to fetch quests:', err);
    }
  }, []);

  // Fetch leaderboard
  const fetchLeaderboard = useCallback(async () => {
    try {
      const response = await fetch('/api/game/leaderboard?type=level&limit=10');
      const data = await response.json();

      if (data.success) {
        setLeaderboard(data.leaderboard);
      }
    } catch (err) {
      console.error('Failed to fetch leaderboard:', err);
    }
  }, []);

  // Fetch discovered venues
  const fetchDiscoveredVenues = useCallback(async () => {
    try {
      const response = await fetch('/api/game/venues/discovered', {
        credentials: 'include'
      });
      const data = await response.json();
      
      if (data.success) {
        setDiscoveredVenues(data.discoveredVenues.map((v: any) => v.venueId));
      }
    } catch (err) {
      console.error('Failed to fetch discovered venues:', err);
    }
  }, []);

  // Handle body part press with timing mechanics
  const handleBodyPartPress = useCallback((bodyPart: string) => {
    if (!selectedCombo || !isTimingActive) return;

    const currentTime = Date.now();
    const expectedStep = selectedCombo.sequence?.[comboSequence.length];
    
    // Calculate timing accuracy (simulated timing window)
    const timingWindow = 100; // 100ms window
    const randomTiming = Math.random() * timingWindow - (timingWindow / 2); // -50ms to +50ms
    
    // Determine accuracy based on timing
    let accuracy = 'BAD';
    let timingScore = 0;
    
    if (Math.abs(randomTiming) <= 16) {
      accuracy = 'PERFECT';
      timingScore = 100;
    } else if (Math.abs(randomTiming) <= 33) {
      accuracy = 'GREAT';
      timingScore = 85;
    } else if (Math.abs(randomTiming) <= 116) {
      accuracy = 'GOOD';
      timingScore = 70;
    } else {
      accuracy = 'BAD';
      timingScore = 30;
    }

    // Check if correct body part
    const isCorrect = bodyPart === expectedStep;
    if (!isCorrect) {
      accuracy = 'BAD';
      timingScore = 0;
    }

    // Update battle stats
    setBattleStats(prev => ({
      ...prev,
      [accuracy.toLowerCase()]: prev[accuracy.toLowerCase() as keyof typeof prev] + (isCorrect ? 1 : 0)
    }));

    // Update combo sequence
    setComboSequence(prev => [...prev, bodyPart]);
    setLastHitAccuracy(accuracy);
    setLastHitTiming(Math.round(randomTiming));

    // Calculate combo accuracy
    const newAccuracy = ((comboSequence.length * comboAccuracy + timingScore) / (comboSequence.length + 1));
    setComboAccuracy(newAccuracy);

    // Update multipliers
    if (accuracy === 'PERFECT') {
      setSpeedMultiplier(prev => Math.min(prev + 0.1, 2.0));
      setComboMultiplier(prev => prev + 1);
    } else if (accuracy === 'GREAT') {
      setSpeedMultiplier(prev => Math.min(prev + 0.05, 2.0));
    } else {
      setSpeedMultiplier(1.0);
      setComboMultiplier(1);
    }

    // Calculate damage
    const baseDamage = selectedCombo.power || 50;
    const damage = Math.round(baseDamage * speedMultiplier * (timingScore / 100));
    setCalculatedDamage(damage);

    // Simulate timing window
    setIsTimingActive(false);
    setTimeout(() => {
      if (comboSequence.length + 1 < (selectedCombo.sequence?.length || 0)) {
        setIsTimingActive(true);
      }
    }, 500);
  }, [selectedCombo, isTimingActive, comboSequence, comboAccuracy, speedMultiplier]);

  // Start battle function
  const startBattle = useCallback(async (battleType: string) => {
    setLoading(true);
    try {
      // Select a random opponent for the battle
      if (!opponents || opponents.length === 0) {
        setError('No opponents available');
        setLoading(false);
        return;
      }
      
      const randomOpponent = opponents[Math.floor(Math.random() * opponents.length)];
      const danceStyle = hero?.primaryStyle || 'SALSA';
      
      const response = await fetch('/api/game/battle/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ 
          opponentId: randomOpponent.id,
          danceStyle: danceStyle
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setCurrentBattle(data.battle);
        setIsTimingActive(true);
        setBattleStats({
          perfect: 0,
          great: 0,
          good: 0,
          totalScore: 0,
          averageAccuracy: 0,
          coinsEarned: 0
        });
      } else {
        setError(data.message || 'Failed to start battle');
      }
    } catch (err) {
      setError('Failed to start battle');
      console.error('Battle start error:', err);
    } finally {
      setLoading(false);
    }
  }, [opponents, hero]);

  // Execute combo function
  const executeCombo = useCallback(async () => {
    if (!selectedCombo || !currentBattle) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/game/battle/${currentBattle.id}/combo`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          comboId: selectedCombo.id,
          sequence: comboSequence,
          accuracy: comboAccuracy,
          timing: speedMultiplier
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setCurrentBattle(data.battle);
        setBattleStats(prev => ({
          ...prev,
          totalScore: prev.totalScore + calculatedDamage,
          averageAccuracy: (prev.averageAccuracy + comboAccuracy) / 2,
          coinsEarned: prev.coinsEarned + Math.round(calculatedDamage / 10)
        }));
        
        // Reset combo state
        setSelectedCombo(null);
        setComboSequence([]);
        setLastHitAccuracy(null);
        setLastHitTiming(null);
        setComboAccuracy(0);
        setSpeedMultiplier(1);
        setComboMultiplier(1);
        setCalculatedDamage(0);
        setIsTimingActive(false);
      } else {
        setError(data.message || 'Failed to execute combo');
      }
    } catch (err) {
      setError('Failed to execute combo');
      console.error('Combo execution error:', err);
    } finally {
      setLoading(false);
    }
  }, [selectedCombo, currentBattle, comboSequence, comboAccuracy, speedMultiplier, calculatedDamage]);

  // Initialize data
  useEffect(() => {
    const initializeGame = async () => {
      setLoading(true);
      try {
        await Promise.all([
          fetchHero(),
          fetchBattleMoves(),
          fetchCombos(),
          fetchOpponents(),
          fetchEquipment(),
          fetchInventory(),
          fetchQuests(),
          fetchLeaderboard(),
          fetchDiscoveredVenues()
        ]);
      } catch (err) {
        setError('Failed to initialize game');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      initializeGame();
    }
  }, [user, fetchHero, fetchBattleMoves, fetchCombos, fetchOpponents, fetchEquipment, fetchInventory, fetchQuests, fetchLeaderboard, fetchDiscoveredVenues]);

  // Purchase equipment
  const purchaseEquipment = async (equipmentId: string) => {
    try {
      const response = await fetch('/api/game/equipment/purchase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ equipmentId })
      });

      const data = await response.json();

      if (data.success) {
        fetchHero(); // Refresh hero data (coins)
        fetchInventory(); // Refresh inventory
      } else {
        setError(data.error || 'Failed to purchase equipment');
      }
    } catch (err) {
      setError('Failed to purchase equipment');
    }
  };

  // Claim quest reward
  const claimQuestReward = async (questId: string) => {
    try {
      const response = await fetch(`/api/game/quests/${questId}/claim`, {
        method: 'POST',
        credentials: 'include'
      });
      const data = await response.json();

      if (data.success) {
        await fetchQuests();
        await fetchHero(); // Refresh hero to update rewards
      }
    } catch (err) {
      console.error('Failed to claim quest reward:', err);
    }
  };

  // Rename hero
  const renameHero = async () => {
    if (!newHeroName.trim() || !hero) return;

    try {
      const response = await fetch(`/api/game/hero/name`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ name: newHeroName.trim() })
      });
      const data = await response.json();

      if (data.success) {
        setHero(data.hero);
        setEditingHeroName(false);
        setNewHeroName('');
      } else {
        setError(data.error || 'Failed to rename hero');
      }
    } catch (err) {
      setError('Failed to rename hero');
    }
  };

  // Get rarity color
  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'common': return 'text-gray-600';
      case 'rare': return 'text-blue-600';
      case 'epic': return 'text-purple-600';
      case 'legendary': return 'text-orange-600';
      case 'mythic': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  // Get dance style icon
  const getDanceStyleIcon = (style: string) => {
    switch (style.toLowerCase()) {
      case 'salsa': return <Flame className="w-4 h-4" />;
      case 'bachata': return <Heart className="w-4 h-4" />;
      case 'kizomba': return <Music className="w-4 h-4" />;
      case 'zouk': return <Star className="w-4 h-4" />;
      case 'chacha': return <Zap className="w-4 h-4" />;
      default: return <Music className="w-4 h-4" />;
    }
  };

  const handleVenueSelect = (venue: any) => {
    console.log('Selected venue:', venue);
    // TODO: Implement venue selection logic
  };

  const handleComboMastered = (comboId: string) => {
    console.log('Combo mastered:', comboId);
    // TODO: Implement combo mastery logic
  };

  // Add venue battle handler
  const handleVenueBattle = async (venueId: string, danceStyle: string) => {
    try {
      setLoading(true);
      const response = await fetch('/api/game/battle/venue/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ venueId, danceStyle })
      });

      const data = await response.json();
      if (data.success) {
        setActiveBattle(data.battle);
        setActiveTab('battle'); // Switch to battle tab
        alert(`🥊 Battle started at ${data.venue.name}!`);
      } else {
        alert(data.error || 'Failed to start venue battle');
      }
    } catch (error) {
      console.error('Error starting venue battle:', error);
      alert('Failed to start venue battle');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-lg">Loading Dance Battle Heroes...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!hero) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <p className="mb-4">No hero found. Please create a hero first.</p>
            <Button onClick={fetchHero}>
              Refresh
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-center mb-2 text-white drop-shadow-lg shadow-black">
          Dance Battle Heroes
        </h1>
        <p className="text-center text-white text-lg font-medium drop-shadow-md shadow-black">
          Master the art of dance combat and become a legend!
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="w-full">
          <TabsTrigger value="hero" className="" activeTab={activeTab} onTabChange={setActiveTab}>
            <Crown className="w-6 h-6 text-purple-600" />
            <div className="text-sm font-bold">Hero Profile</div>
            <div className="text-xs text-gray-500">Stats & Progress</div>
          </TabsTrigger>
          <TabsTrigger value="battle" className="" activeTab={activeTab} onTabChange={setActiveTab}>
            <Sword className="w-6 h-6 text-red-600" />
            <div className="text-sm font-bold">Battle Arena</div>
            <div className="text-xs text-gray-500">Fight & Compete</div>
          </TabsTrigger>
          <TabsTrigger value="world-map" className="" activeTab={activeTab} onTabChange={setActiveTab}>
            <Map className="w-6 h-6 text-green-600" />
            <div className="text-sm font-bold">World Map</div>
            <div className="text-xs text-gray-500">Explore Venues</div>
          </TabsTrigger>
          <TabsTrigger value="combo-trainer" className="" activeTab={activeTab} onTabChange={setActiveTab}>
            <Gamepad2 className="w-6 h-6 text-blue-600" />
            <div className="text-sm font-bold">Combo Trainer</div>
            <div className="text-xs text-gray-500">Practice Moves</div>
          </TabsTrigger>
          <TabsTrigger value="equipment" className="" activeTab={activeTab} onTabChange={setActiveTab}>
            <ShoppingBag className="w-6 h-6 text-orange-600" />
            <div className="text-sm font-bold">Equipment Shop</div>
            <div className="text-xs text-gray-500">Buy Gear</div>
          </TabsTrigger>
          <TabsTrigger value="quests" className="" activeTab={activeTab} onTabChange={setActiveTab}>
            <Target className="w-6 h-6 text-yellow-600" />
            <div className="text-sm font-bold">Quests</div>
            <div className="text-xs text-gray-500">Missions & Tasks</div>
          </TabsTrigger>
          <TabsTrigger value="leaderboard" className="" activeTab={activeTab} onTabChange={setActiveTab}>
            <Trophy className="w-6 h-6 text-yellow-500" />
            <div className="text-sm font-bold">Rankings</div>
            <div className="text-xs text-gray-500">Top Players</div>
          </TabsTrigger>
          <TabsTrigger value="inventory" className="" activeTab={activeTab} onTabChange={setActiveTab}>
            <Gift className="w-6 h-6 text-pink-600" />
            <div className="text-sm font-bold">Inventory</div>
            <div className="text-xs text-gray-500">Your Items</div>
          </TabsTrigger>
        </TabsList>

        {/* Hero Tab */}
        <TabsContent value="hero" className="space-y-6" activeTab={activeTab}>
          {/* Hero Header Section */}
          <div className="bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-lg p-6 border border-purple-500">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Hero Avatar & Basic Info */}
              <div className="lg:col-span-1">
                <div className="text-center">
                  {/* Hero Avatar */}
                  <div className="relative mx-auto w-32 h-32 mb-4">
                    <div className="w-full h-full bg-gradient-to-br from-yellow-400 via-orange-500 to-red-600 rounded-full flex items-center justify-center border-4 border-yellow-300 shadow-lg">
                      <Crown className="w-16 h-16 text-white drop-shadow-lg" />
                    </div>
                    {/* Level Badge */}
                    <div className="absolute -bottom-2 -right-2 bg-purple-600 text-white rounded-full w-12 h-12 flex items-center justify-center border-4 border-white font-bold text-lg shadow-lg">
                      {hero.level}
                    </div>
                    {/* Rank Badge */}
                    {hero.globalRank && hero.globalRank <= 10 && (
                      <div className="absolute -top-2 -left-2 bg-yellow-500 text-black rounded-full w-8 h-8 flex items-center justify-center border-2 border-white font-bold text-sm shadow-lg">
                        #{hero.globalRank}
                      </div>
                    )}
                  </div>

                  {/* Hero Name & Title */}
                  <div className="mb-4">
                    {editingHeroName ? (
                      <div className="flex flex-col items-center gap-2">
                        <input
                          type="text"
                          value={newHeroName}
                          onChange={(e) => setNewHeroName(e.target.value)}
                          className="px-3 py-2 border rounded-lg text-center bg-white text-black font-bold text-xl"
                          placeholder={hero.name}
                          onKeyPress={(e) => e.key === 'Enter' && renameHero()}
                          autoFocus
                        />
                        <div className="flex gap-2">
                          <Button size="sm" onClick={renameHero} disabled={!newHeroName.trim()}>
                            Save
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => {
                            setEditingHeroName(false);
                            setNewHeroName('');
                          }}>
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <h1 className="text-3xl font-bold text-white flex items-center justify-center gap-2">
                          {hero.name}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setEditingHeroName(true);
                              setNewHeroName(hero.name);
                            }}
                            className="ml-2"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </h1>
                        {hero.titles && hero.titles.length > 0 && (
                          <div className="text-yellow-300 font-semibold">
                            "{hero.titles[0]}"
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 gap-3 text-center">
                    <div className="bg-black bg-opacity-50 rounded-lg p-3">
                      <div className="text-2xl font-bold text-yellow-400">{hero.coins || 0}</div>
                      <div className="text-xs text-gray-300">Coins</div>
                    </div>
                    <div className="bg-black bg-opacity-50 rounded-lg p-3">
                      <div className="text-2xl font-bold text-purple-400">{hero.trophies || 0}</div>
                      <div className="text-xs text-gray-300">Trophies</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Experience & Energy */}
              <div className="lg:col-span-1 space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-green-400" />
                    Progress
                  </h3>

                  {/* Experience */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-300">Experience</span>
                      <span className="text-white font-semibold">{hero.experience} / {hero.experienceToNext || 1000}</span>
                    </div>
                    <div className="relative">
                      <Progress
                        value={(hero.experience / (hero.experienceToNext || 1000)) * 100}
                        className="h-3"
                      />
                      <div className="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
                        {Math.round((hero.experience / (hero.experienceToNext || 1000)) * 100)}%
                      </div>
                    </div>
                  </div>

                  {/* Energy */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-300">Energy</span>
                      <span className="text-white font-semibold">{hero.energy} / 10</span>
                    </div>
                    <div className="relative">
                      <Progress
                        value={(hero.energy / 10) * 100}
                        className="h-3"
                      />
                      <div className="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
                        {hero.energy}/10
                      </div>
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      ⚡ Regenerates every hour
                    </div>
                  </div>

                  {/* Available Points */}
                  <div className="grid grid-cols-2 gap-2">
                    <div className="bg-green-900 bg-opacity-50 rounded-lg p-2 text-center border border-green-500">
                      <div className="text-lg font-bold text-green-400">{hero.skillPoints}</div>
                      <div className="text-xs text-green-300">Skill Points</div>
                    </div>
                    <div className="bg-purple-900 bg-opacity-50 rounded-lg p-2 text-center border border-purple-500">
                      <div className="text-lg font-bold text-purple-400">{hero.traitPoints}</div>
                      <div className="text-xs text-purple-300">Trait Points</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Battle Record & Ranking */}
              <div className="lg:col-span-1 space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                    <Trophy className="w-5 h-5 mr-2 text-yellow-400" />
                    Battle Record
                  </h3>

                  <div className="grid grid-cols-3 gap-2 mb-4">
                    <div className="bg-green-900 bg-opacity-50 rounded-lg p-3 text-center border border-green-500">
                      <div className="text-xl font-bold text-green-400">{hero.wins}</div>
                      <div className="text-xs text-green-300">Wins</div>
                    </div>
                    <div className="bg-red-900 bg-opacity-50 rounded-lg p-3 text-center border border-red-500">
                      <div className="text-xl font-bold text-red-400">{hero.losses}</div>
                      <div className="text-xs text-red-300">Losses</div>
                    </div>
                    <div className="bg-yellow-900 bg-opacity-50 rounded-lg p-3 text-center border border-yellow-500">
                      <div className="text-xl font-bold text-yellow-400">{hero.draws}</div>
                      <div className="text-xs text-yellow-300">Draws</div>
                    </div>
                  </div>

                  {/* Win Rate & Streak */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300 text-sm">Win Rate:</span>
                      <span className="text-white font-semibold">
                        {hero.wins + hero.losses > 0 ? Math.round((hero.wins / (hero.wins + hero.losses)) * 100) : 0}%
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300 text-sm">Win Streak:</span>
                      <Badge variant={hero.winStreak > 5 ? "default" : "outline"} className="bg-orange-600">
                        🔥 {hero.winStreak}
                      </Badge>
                    </div>
                    {hero.globalRank && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300 text-sm">Global Rank:</span>
                        <Badge variant="default" className="bg-purple-600">
                          #{hero.globalRank}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Combat Stats */}
            <Card className="bg-gradient-to-br from-gray-900 to-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Sword className="w-5 h-5 text-red-500" />
                  Combat Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Primary Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Heart className="w-4 h-4 text-red-500" />
                        <span className="text-sm text-gray-300">Health</span>
                      </div>
                      <span className="font-bold text-red-400">{hero.health}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Sword className="w-4 h-4 text-orange-500" />
                        <span className="text-sm text-gray-300">Attack</span>
                      </div>
                      <span className="font-bold text-orange-400">{hero.attack}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Zap className="w-4 h-4 text-yellow-500" />
                        <span className="text-sm text-gray-300">Speed</span>
                      </div>
                      <span className="font-bold text-yellow-400">{hero.speed}</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-blue-500" />
                        <span className="text-sm text-gray-300">Mana</span>
                      </div>
                      <span className="font-bold text-blue-400">{hero.mana}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Shield className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-gray-300">Defense</span>
                      </div>
                      <span className="font-bold text-green-400">{hero.defense}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-purple-500" />
                        <span className="text-sm text-gray-300">Luck</span>
                      </div>
                      <span className="font-bold text-purple-400">{hero.luck}</span>
                    </div>
                  </div>
                </div>

                {/* Combat Power Indicator */}
                <div className="mt-4 p-3 bg-black bg-opacity-50 rounded-lg border border-gray-600">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300 text-sm">Combat Power:</span>
                    <span className="text-xl font-bold text-yellow-400">
                      {hero.attack + hero.defense + hero.speed + hero.luck + Math.floor(hero.health/10) + Math.floor(hero.mana/5)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    Combined strength of all combat attributes
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Dance Styles & Skills */}
            <Card className="bg-gradient-to-br from-purple-900 to-indigo-800 border-purple-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Music className="w-5 h-5 text-purple-400" />
                  Dance Mastery
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Dance Styles */}
                <div>
                  <h4 className="font-semibold mb-3 text-purple-200">Dance Styles</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-black bg-opacity-30 rounded-lg border border-purple-500">
                      <div className="flex items-center gap-2">
                        {getDanceStyleIcon(hero.primaryStyle)}
                        <span className="font-medium text-white">{hero.primaryStyle}</span>
                        <Badge variant="default" className="bg-purple-600">Primary</Badge>
                      </div>
                      <div className="text-purple-300 font-semibold">
                        {hero.stylePoints?.[hero.primaryStyle] || 0} pts
                      </div>
                    </div>
                    {hero.secondaryStyle && (
                      <div className="flex items-center justify-between p-2 bg-black bg-opacity-30 rounded-lg border border-purple-400">
                        <div className="flex items-center gap-2">
                          {getDanceStyleIcon(hero.secondaryStyle)}
                          <span className="text-white">{hero.secondaryStyle}</span>
                          <Badge variant="outline" className="border-purple-400">Secondary</Badge>
                        </div>
                        <div className="text-purple-300 font-semibold">
                          {hero.stylePoints?.[hero.secondaryStyle] || 0} pts
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Skills */}
                <div>
                  <h4 className="font-semibold mb-3 text-purple-200">Skills</h4>
                  <div className="space-y-2">
                    {Object.entries(hero.skills || {}).slice(0, 5).map(([skill, level]) => (
                      <div key={skill} className="flex items-center justify-between">
                        <span className="text-sm text-gray-300 capitalize">{skill.replace(/_/g, ' ')}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-700 rounded-full h-2">
                            <div
                              className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${Math.min((level / 10) * 100, 100)}%` }}
                            />
                          </div>
                          <span className="text-purple-400 font-semibold text-sm w-6">{level}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Traits & Achievements */}
            <Card className="bg-gradient-to-br from-yellow-900 to-orange-800 border-yellow-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Award className="w-5 h-5 text-yellow-400" />
                  Traits & Achievements
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Traits */}
                <div>
                  <h4 className="font-semibold mb-3 text-yellow-200">Active Traits</h4>
                  <div className="flex flex-wrap gap-2">
                    {(hero.traits || []).map((trait) => (
                      <Badge
                        key={trait}
                        variant="outline"
                        className="text-xs bg-yellow-900 border-yellow-500 text-yellow-200 hover:bg-yellow-800"
                      >
                        ✨ {trait.replace(/_/g, ' ')}
                      </Badge>
                    ))}
                    {(!hero.traits || hero.traits.length === 0) && (
                      <div className="text-gray-400 text-sm italic">No traits acquired yet</div>
                    )}
                  </div>
                </div>

                {/* Titles */}
                {hero.titles && hero.titles.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-3 text-yellow-200">Earned Titles</h4>
                    <div className="space-y-2">
                      {hero.titles.map((title, index) => (
                        <div
                          key={title}
                          className={`p-2 rounded-lg border ${
                            index === 0
                              ? 'bg-yellow-900 bg-opacity-50 border-yellow-500'
                              : 'bg-black bg-opacity-30 border-yellow-600'
                          }`}
                        >
                          <div className="flex items-center gap-2">
                            <Crown className="w-4 h-4 text-yellow-400" />
                            <span className="text-yellow-200 font-medium">{title}</span>
                            {index === 0 && (
                              <Badge variant="default" className="bg-yellow-600 text-xs">Active</Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Achievement Progress */}
                <div>
                  <h4 className="font-semibold mb-3 text-yellow-200">Recent Achievements</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-black bg-opacity-30 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Trophy className="w-4 h-4 text-yellow-400" />
                        <span className="text-sm text-yellow-200">First Victory</span>
                      </div>
                      <Badge variant={hero.wins > 0 ? "default" : "outline"} className="bg-green-600">
                        {hero.wins > 0 ? "✓" : "○"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-black bg-opacity-30 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-400" />
                        <span className="text-sm text-yellow-200">Level 5 Reached</span>
                      </div>
                      <Badge variant={hero.level >= 5 ? "default" : "outline"} className="bg-purple-600">
                        {hero.level >= 5 ? "✓" : "○"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-black bg-opacity-30 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Zap className="w-4 h-4 text-yellow-400" />
                        <span className="text-sm text-yellow-200">Win Streak Master</span>
                      </div>
                      <Badge variant={hero.winStreak >= 5 ? "default" : "outline"} className="bg-orange-600">
                        {hero.winStreak >= 5 ? "✓" : `${hero.winStreak}/5`}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Equipment Preview */}
            <Card className="bg-gradient-to-br from-green-900 to-teal-800 border-green-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Package className="w-5 h-5 text-green-400" />
                  Equipment & Inventory
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Equipment Slots */}
                <div>
                  <h4 className="font-semibold mb-3 text-green-200">Equipped Items</h4>
                  <div className="grid grid-cols-2 gap-3">
                    {['SHOES', 'OUTFIT', 'ACCESSORY', 'INSTRUMENT'].map((slot) => (
                      <div key={slot} className="p-3 bg-black bg-opacity-30 rounded-lg border border-green-600 text-center">
                        <div className="text-xs text-green-300 mb-1">{slot}</div>
                        <div className="text-gray-400 text-sm">
                          {inventory?.find(item => item.type === slot) ? (
                            <div>
                              <div className="text-green-400 font-medium">
                                {inventory.find(item => item.type === slot)?.name}
                              </div>
                              <div className="text-xs text-green-300">
                                +{Object.values(inventory.find(item => item.type === slot)?.statBonuses || {}).reduce((a, b) => a + b, 0)} stats
                              </div>
                            </div>
                          ) : (
                            "Empty"
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Quick Equipment Stats */}
                <div className="p-3 bg-black bg-opacity-50 rounded-lg border border-green-600">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-green-300 text-sm">Equipment Bonus:</span>
                    <span className="text-green-400 font-bold">
                      +{inventory?.reduce((total, item) => {
                        return total + Object.values(item.statBonuses || {}).reduce((a, b) => a + b, 0);
                      }, 0) || 0}
                    </span>
                  </div>
                  <div className="text-xs text-gray-400">
                    Total stat bonuses from equipped items
                  </div>
                </div>

                {/* Quick Shop Access */}
                <Button
                  className="w-full bg-green-600 hover:bg-green-700 text-white"
                  onClick={() => setActiveTab('equipment')}
                >
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Visit Equipment Shop
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Battle Tab */}
        <TabsContent value="battle" className="space-y-6" activeTab={activeTab}>
          {!currentBattle ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Quick Battle */}
              <div className="bg-gradient-to-br from-red-900 to-red-700 rounded-lg p-6 border border-red-500">
                <h3 className="text-xl font-bold text-white mb-4">Quick Battle</h3>
                <p className="text-red-100 mb-4">
                  Challenge a random opponent for a quick dance battle
                </p>
                <button
                  onClick={() => startBattle('quick')}
                  className="w-full bg-red-600 hover:bg-red-500 text-white font-bold py-3 px-4 rounded-lg transition-colors"
                  disabled={loading}
                >
                  {loading ? 'Starting...' : 'Start Quick Battle'}
                </button>
              </div>

              {/* Ranked Battle */}
              <div className="bg-gradient-to-br from-purple-900 to-purple-700 rounded-lg p-6 border border-purple-500">
                <h3 className="text-xl font-bold text-white mb-4">Ranked Battle</h3>
                <p className="text-purple-100 mb-4">
                  Compete in ranked matches to climb the leaderboard
                </p>
                <button
                  onClick={() => startBattle('ranked')}
                  className="w-full bg-purple-600 hover:bg-purple-500 text-white font-bold py-3 px-4 rounded-lg transition-colors"
                  disabled={loading}
                >
                  {loading ? 'Starting...' : 'Start Ranked Battle'}
                </button>
              </div>

              {/* Tournament */}
              <div className="bg-gradient-to-br from-yellow-900 to-yellow-700 rounded-lg p-6 border border-yellow-500">
                <h3 className="text-xl font-bold text-white mb-4">Tournament</h3>
                <p className="text-yellow-100 mb-4">
                  Join tournaments for bigger rewards and glory
                </p>
                <button
                  onClick={() => startBattle('tournament')}
                  className="w-full bg-yellow-600 hover:bg-yellow-500 text-white font-bold py-3 px-4 rounded-lg transition-colors"
                  disabled={loading}
                >
                  {loading ? 'Starting...' : 'Join Tournament'}
                </button>
              </div>
            </div>
          ) : (
            /* Enhanced Battle Interface with Timing Mechanics */
            <div className="space-y-6">
              {/* Battle Header */}
              <div className="bg-gradient-to-r from-purple-900 via-blue-900 to-indigo-900 rounded-lg p-6 border border-purple-500">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Player Info */}
                  <div className="text-center">
                    <div className="w-20 h-20 bg-blue-600 rounded-full mx-auto mb-3 flex items-center justify-center">
                      <Users className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-white">{hero?.name || 'You'}</h3>
                    <div className="text-blue-300">Level {hero?.level || 1}</div>
                    <div className="w-full bg-gray-700 rounded-full h-3 mt-2">
                      <div 
                        className="bg-blue-500 h-3 rounded-full transition-all duration-300" 
                        style={{ width: `${((hero?.health || 100) / 100) * 100}%` }}
                      />
                    </div>
                    <div className="text-sm text-gray-300 mt-1">{hero?.health || 100}/100 HP</div>
                  </div>

                  {/* Battle Status */}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-400 mb-2">
                      {currentBattle.status === 'active' ? 'BATTLE IN PROGRESS' : 'BATTLE READY'}
                    </div>
                    <div className="text-lg text-white mb-2">Round {currentBattle.round || 1}</div>
                    
                    {/* Timing Display */}
                    <div className="bg-black bg-opacity-50 rounded-lg p-3 mb-3">
                      <div className="text-sm text-gray-300 mb-1">Timing Window</div>
                      <div className="w-full bg-gray-700 rounded-full h-2 mb-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full transition-all duration-100" 
                          style={{ width: `${timingAccuracy}%` }}
                        />
                      </div>
                      <div className="text-xs text-gray-400">
                        Perfect: ±16ms | Great: ±33ms | Good: ±116ms
                      </div>
                    </div>

                    {/* Score Display */}
                    <div className="grid grid-cols-3 gap-2 text-sm">
                      <div className="bg-green-600 bg-opacity-50 rounded p-2">
                        <div className="text-green-300">Perfect</div>
                        <div className="text-white font-bold">{battleStats.perfect || 0}</div>
                      </div>
                      <div className="bg-blue-600 bg-opacity-50 rounded p-2">
                        <div className="text-blue-300">Great</div>
                        <div className="text-white font-bold">{battleStats.great || 0}</div>
                      </div>
                      <div className="bg-yellow-600 bg-opacity-50 rounded p-2">
                        <div className="text-yellow-300">Good</div>
                        <div className="text-white font-bold">{battleStats.good || 0}</div>
                      </div>
                    </div>
                  </div>

                  {/* Opponent Info */}
                  <div className="text-center">
                    <div className="w-20 h-20 bg-red-600 rounded-full mx-auto mb-3 flex items-center justify-center">
                      <Bot className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-white">{currentBattle.opponent?.name || 'Opponent'}</h3>
                    <div className="text-red-300">Level {currentBattle.opponent?.level || 1}</div>
                    <div className="w-full bg-gray-700 rounded-full h-3 mt-2">
                      <div 
                        className="bg-red-500 h-3 rounded-full transition-all duration-300" 
                        style={{ width: `${((currentBattle.opponent?.health || 100) / 100) * 100}%` }}
                      />
                    </div>
                    <div className="text-sm text-gray-300 mt-1">{currentBattle.opponent?.health || 100}/100 HP</div>
                  </div>
                </div>
              </div>

              {/* Enhanced Combo Selection */}
              {!selectedCombo && (
                <div className="bg-black bg-opacity-50 rounded-lg p-6 border border-purple-500">
                  <h3 className="text-lg font-semibold text-purple-400 mb-4">Select Your Combo</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {combos.slice(0, 6).map((combo: Combo) => (
                      <button
                        key={combo.id}
                        className={`p-4 rounded-lg border transition-all ${
                          selectedCombo?.id === combo.id
                            ? 'border-purple-400 bg-purple-900 bg-opacity-50'
                            : 'border-gray-600 bg-gray-800 bg-opacity-50 hover:border-purple-500'
                        }`}
                        onClick={() => setSelectedCombo(combo)}
                      >
                        <div className="text-left">
                          <h4 className="font-bold text-white mb-2">{combo.name}</h4>
                          <div className="text-sm text-gray-300 mb-2">{combo.description}</div>
                          <div className="flex justify-between text-xs">
                            <span className="text-blue-400">Power: {combo.power}</span>
                            <span className="text-green-400">Difficulty: {combo.difficulty}/10</span>
                          </div>
                          <div className="flex justify-between text-xs mt-1">
                            <span className="text-yellow-400">Beats: {combo.beatCount}</span>
                            <span className="text-purple-400">Style: {combo.danceStyle}</span>
                          </div>
                          <div className="mt-2 text-xs text-gray-400">
                            Sequence: {combo.sequence?.join(' → ') || 'N/A'}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Enhanced Timing-Based Battle Interface */}
              {selectedCombo && (
                <div className="bg-black bg-opacity-50 rounded-lg p-6 border border-purple-500">
                  <h3 className="text-lg font-semibold text-yellow-400 mb-4">Execute Combo: {selectedCombo.name}</h3>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Enhanced Body Movement Interface */}
                    <div className="bg-gray-900 bg-opacity-50 rounded-lg p-4 border border-purple-500">
                      <h4 className="text-md font-semibold text-white mb-4 text-center">Body Movement Controls</h4>
                      
                      {/* Human Figure Layout */}
                      <div className="relative mx-auto" style={{ width: '200px', height: '300px' }}>
                        {/* Head */}
                        <button
                          className={`absolute top-2 left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full border-2 transition-all duration-200 ${
                            comboSequence.includes('H') 
                              ? 'bg-yellow-500 border-yellow-400 shadow-lg shadow-yellow-500/50' 
                              : 'bg-gray-700 border-gray-500 hover:bg-gray-600 hover:border-gray-400'
                          }`}
                          onClick={() => handleBodyPartPress('H')}
                          disabled={!isTimingActive}
                        >
                          <div className="text-white text-xs font-bold">HEAD</div>
                        </button>

                        {/* Torso */}
                        <button
                          className={`absolute top-16 left-1/2 transform -translate-x-1/2 w-16 h-20 rounded-lg border-2 transition-all duration-200 ${
                            comboSequence.includes('T') 
                              ? 'bg-blue-500 border-blue-400 shadow-lg shadow-blue-500/50' 
                              : 'bg-gray-700 border-gray-500 hover:bg-gray-600 hover:border-gray-400'
                          }`}
                          onClick={() => handleBodyPartPress('T')}
                          disabled={!isTimingActive}
                        >
                          <div className="text-white text-xs font-bold">TORSO</div>
                        </button>

                        {/* Left Hand */}
                        <button
                          className={`absolute top-20 left-4 w-12 h-16 rounded-lg border-2 transition-all duration-200 ${
                            comboSequence.includes('LH') 
                              ? 'bg-green-500 border-green-400 shadow-lg shadow-green-500/50' 
                              : 'bg-gray-700 border-gray-500 hover:bg-gray-600 hover:border-gray-400'
                          }`}
                          onClick={() => handleBodyPartPress('LH')}
                          disabled={!isTimingActive}
                        >
                          <div className="text-white text-xs font-bold">L.HAND</div>
                        </button>

                        {/* Right Hand */}
                        <button
                          className={`absolute top-20 right-4 w-12 h-16 rounded-lg border-2 transition-all duration-200 ${
                            comboSequence.includes('RH') 
                              ? 'bg-green-500 border-green-400 shadow-lg shadow-green-500/50' 
                              : 'bg-gray-700 border-gray-500 hover:bg-gray-600 hover:border-gray-400'
                          }`}
                          onClick={() => handleBodyPartPress('RH')}
                          disabled={!isTimingActive}
                        >
                          <div className="text-white text-xs font-bold">R.HAND</div>
                        </button>

                        {/* Hips */}
                        <button
                          className={`absolute top-36 left-1/2 transform -translate-x-1/2 w-14 h-12 rounded-lg border-2 transition-all duration-200 ${
                            comboSequence.includes('HP') 
                              ? 'bg-purple-500 border-purple-400 shadow-lg shadow-purple-500/50' 
                              : 'bg-gray-700 border-gray-500 hover:bg-gray-600 hover:border-gray-400'
                          }`}
                          onClick={() => handleBodyPartPress('HP')}
                          disabled={!isTimingActive}
                        >
                          <div className="text-white text-xs font-bold">HIPS</div>
                        </button>

                        {/* Left Leg */}
                        <button
                          className={`absolute bottom-4 left-12 w-12 h-20 rounded-lg border-2 transition-all duration-200 ${
                            comboSequence.includes('LL') 
                              ? 'bg-red-500 border-red-400 shadow-lg shadow-red-500/50' 
                              : 'bg-gray-700 border-gray-500 hover:bg-gray-600 hover:border-gray-400'
                          }`}
                          onClick={() => handleBodyPartPress('LL')}
                          disabled={!isTimingActive}
                        >
                          <div className="text-white text-xs font-bold">L.LEG</div>
                        </button>

                        {/* Right Leg */}
                        <button
                          className={`absolute bottom-4 right-12 w-12 h-20 rounded-lg border-2 transition-all duration-200 ${
                            comboSequence.includes('RL') 
                              ? 'bg-red-500 border-red-400 shadow-lg shadow-red-500/50' 
                              : 'bg-gray-700 border-gray-500 hover:bg-gray-600 hover:border-gray-400'
                          }`}
                          onClick={() => handleBodyPartPress('RL')}
                          disabled={!isTimingActive}
                        >
                          <div className="text-white text-xs font-bold">R.LEG</div>
                        </button>
                      </div>

                      {/* Timing Indicator */}
                      <div className="mt-4 text-center">
                        <div className={`text-lg font-bold mb-2 ${
                          isTimingActive ? 'text-green-400' : 'text-gray-400'
                        }`}>
                          {isTimingActive ? 'PRESS NOW!' : 'Wait for timing...'}
                        </div>
                        <div className="text-sm text-gray-300">
                          Progress: {comboSequence.length}/{selectedCombo.sequence?.length || 0}
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Combo Progress Display */}
                    <div className="bg-gray-900 bg-opacity-50 rounded-lg p-4 border border-purple-500">
                      <h4 className="text-md font-semibold text-white mb-3">Expected Sequence</h4>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {selectedCombo.sequence?.map((step: string, index: number) => {
                          const isCompleted = index < comboSequence.length;
                          const isCurrent = index === comboSequence.length;
                          const isCorrect = isCompleted && comboSequence[index] === step;
                          
                          return (
                            <div
                              key={index}
                              className={`px-3 py-2 rounded-lg border text-sm font-bold transition-all ${
                                isCompleted
                                  ? isCorrect
                                    ? 'bg-green-600 border-green-500 text-white'
                                    : 'bg-red-600 border-red-500 text-white'
                                  : isCurrent
                                    ? 'bg-yellow-600 border-yellow-500 text-white animate-pulse'
                                    : 'bg-gray-700 border-gray-600 text-gray-300'
                              }`}
                            >
                              {step}
                            </div>
                          );
                        })}
                      </div>

                      {/* Timing Accuracy Display */}
                      <div className="mb-4">
                        <h5 className="text-sm font-semibold text-white mb-2">Last Hit Accuracy</h5>
                        <div className="bg-black bg-opacity-50 rounded p-3">
                          <div className={`text-lg font-bold ${
                            lastHitAccuracy === 'PERFECT' ? 'text-green-400' :
                            lastHitAccuracy === 'GREAT' ? 'text-blue-400' :
                            lastHitAccuracy === 'GOOD' ? 'text-yellow-400' :
                            lastHitAccuracy === 'BAD' ? 'text-red-400' : 'text-gray-400'
                          }`}>
                            {lastHitAccuracy || 'N/A'}
                          </div>
                          {lastHitTiming && (
                            <div className="text-sm text-gray-300">
                              Timing: {lastHitTiming > 0 ? '+' : ''}{lastHitTiming}ms
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Combo Stats */}
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div className="bg-black bg-opacity-50 rounded p-2">
                          <div className="text-gray-300">Accuracy</div>
                          <div className="text-white font-bold">{Math.round(comboAccuracy)}%</div>
                        </div>
                        <div className="bg-black bg-opacity-50 rounded p-2">
                          <div className="text-gray-300">Speed Bonus</div>
                          <div className="text-white font-bold">x{speedMultiplier.toFixed(1)}</div>
                        </div>
                        <div className="bg-black bg-opacity-50 rounded p-2">
                          <div className="text-gray-300">Damage</div>
                          <div className="text-white font-bold">{calculatedDamage}</div>
                        </div>
                        <div className="bg-black bg-opacity-50 rounded p-2">
                          <div className="text-gray-300">Combo</div>
                          <div className="text-white font-bold">{comboMultiplier}x</div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-3 mt-4">
                        <button
                          onClick={executeCombo}
                          disabled={comboSequence.length !== selectedCombo.sequence?.length || loading}
                          className="flex-1 bg-green-600 hover:bg-green-500 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded-lg transition-colors"
                        >
                          {loading ? 'Executing...' : 'Execute Combo'}
                        </button>
                        <button
                          onClick={() => {
                            setSelectedCombo(null);
                            setComboSequence([]);
                            setLastHitAccuracy(null);
                            setLastHitTiming(null);
                          }}
                          className="bg-gray-600 hover:bg-gray-500 text-white font-bold py-2 px-4 rounded-lg transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Battle Results */}
              {currentBattle?.status === 'completed' && (
                <div className="bg-gradient-to-br from-green-900 to-blue-900 rounded-lg p-6 border border-green-500">
                  <h3 className="text-2xl font-bold text-center text-white mb-4">
                    {currentBattle.winner === hero?.id ? '🎉 VICTORY! 🎉' : '💀 DEFEAT 💀'}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div className="bg-black bg-opacity-50 rounded-lg p-4">
                      <div className="text-lg font-bold text-green-400">Total Score</div>
                      <div className="text-2xl text-white">{battleStats.totalScore || 0}</div>
                    </div>
                    <div className="bg-black bg-opacity-50 rounded-lg p-4">
                      <div className="text-lg font-bold text-blue-400">Accuracy</div>
                      <div className="text-2xl text-white">{Math.round(battleStats.averageAccuracy || 0)}%</div>
                    </div>
                    <div className="bg-black bg-opacity-50 rounded-lg p-4">
                      <div className="text-lg font-bold text-yellow-400">Rewards</div>
                      <div className="text-2xl text-white">+{battleStats.coinsEarned || 0} 🪙</div>
                    </div>
                  </div>
                  <button
                    onClick={() => setCurrentBattle(null)}
                    className="w-full mt-4 bg-blue-600 hover:bg-blue-500 text-white font-bold py-3 px-4 rounded-lg transition-colors"
                  >
                    Continue
                  </button>
                </div>
              )}
            </div>
          )}
        </TabsContent>

        {/* Equipment Tab */}
        <TabsContent value="equipment" className="space-y-6" activeTab={activeTab}>
          {/* Coin Balance Display */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center">
                    <span className="text-yellow-600 font-bold">💰</span>
                  </div>
                  <div>
                    <h3 className="font-semibold">Your Coins</h3>
                    <p className="text-sm text-gray-600">Available for equipment purchases</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-yellow-600">{hero.coins || 0}</div>
                  <div className="text-sm text-gray-500">coins</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {(equipment || []).map((item) => (
              <Card key={item.id}>
                <CardHeader>
                  <CardTitle className={`text-lg ${getRarityColor(item.rarity)}`}>
                    {item.name}
                  </CardTitle>
                  <Badge variant="outline">{item.type}</Badge>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-3">{item.description}</p>

                  {/* Stat Bonuses */}
                  {item.statBonuses && Object.keys(item.statBonuses).length > 0 && (
                    <div className="mb-3">
                      <h5 className="font-semibold text-sm mb-1">Stat Bonuses:</h5>
                      <div className="text-xs space-y-1">
                        {Object.entries(item.statBonuses || {}).map(([stat, bonus]) => (
                          <div key={stat} className="flex justify-between">
                            <span className="capitalize">{stat}:</span>
                            <span className="text-green-600">+{bonus}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Price and Purchase */}
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-1">
                      <span className={`text-lg font-bold ${(hero.coins || 0) < item.price ? 'text-red-600' : 'text-gray-900'}`}>
                        {item.price}
                      </span>
                      <span className="text-sm text-gray-600">coins</span>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => purchaseEquipment(item.id)}
                      disabled={(hero.coins || 0) < item.price || hero.level < item.requiredLevel}
                      variant={(hero.coins || 0) >= item.price && hero.level >= item.requiredLevel ? "default" : "outline"}
                    >
                      {(hero.coins || 0) < item.price ? 'Not Enough Coins' :
                       hero.level < item.requiredLevel ? 'Level Required' : 'Buy'}
                    </Button>
                  </div>

                  {/* Requirement Messages */}
                  {(hero.coins || 0) < item.price && (
                    <p className="text-xs text-red-600 mt-1">
                      💰 Need {item.price - (hero.coins || 0)} more coins
                    </p>
                  )}
                  {hero.level < item.requiredLevel && (
                    <p className="text-xs text-red-600 mt-1">
                      🔒 Requires level {item.requiredLevel}
                    </p>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Quests Tab */}
        <TabsContent value="quests" className="space-y-6" activeTab={activeTab}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {(quests || []).map((quest) => (
              <Card key={quest.id}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    {quest.title}
                    <Badge variant={quest.type === 'DAILY' ? 'default' : 'outline'}>
                      {quest.type}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-3">{quest.description}</p>

                  {/* Progress */}
                  <div className="mb-3">
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>{quest.progress} / {quest.targetValue}</span>
                    </div>
                    <Progress
                      value={(quest.progress / quest.targetValue) * 100}
                      className="h-2"
                    />
                  </div>

                  {/* Rewards */}
                  <div className="mb-3">
                    <h5 className="font-semibold text-sm mb-1">Rewards:</h5>
                    <div className="text-xs space-y-1">
                      {quest.rewards && quest.rewards.experience && (
                        <div>+{quest.rewards.experience} XP</div>
                      )}
                      {quest.rewards && quest.rewards.coins && (
                        <div>+{quest.rewards.coins} Coins</div>
                      )}
                      {quest.rewards && quest.rewards.skillPoints && (
                        <div>+{quest.rewards.skillPoints} Skill Points</div>
                      )}
                      {quest.rewards && quest.rewards.traitPoints && (
                        <div>+{quest.rewards.traitPoints} Trait Points</div>
                      )}
                    </div>
                  </div>

                  {/* Claim Button */}
                  {quest.completed && !quest.claimedAt && (
                    <Button
                      className="w-full"
                      onClick={() => claimQuestReward(quest.id)}
                    >
                      Claim Reward
                    </Button>
                  )}

                  {quest.claimedAt && (
                    <Badge variant="outline" className="w-full justify-center">
                      Completed
                    </Badge>
                  )}

                  {/* Expiry */}
                  {quest.expiresAt && (
                    <p className="text-xs text-gray-500 mt-2">
                      <Clock className="w-3 h-3 inline mr-1" />
                      Expires: {new Date(quest.expiresAt).toLocaleDateString()}
                    </p>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Leaderboard Tab */}
        <TabsContent value="leaderboard" className="space-y-6" activeTab={activeTab}>
          <div className="space-y-4">
            {(leaderboard || []).map((player, index) => (
              <Card key={player.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-semibold">{player.name}</div>
                        <div className="text-sm text-gray-600">
                          Level {player.level} • {player.wins}W - {player.losses}L
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getDanceStyleIcon(player.primaryStyle)}
                      <Badge variant="outline">{player.primaryStyle}</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Inventory Tab */}
        <TabsContent value="inventory" className="space-y-6" activeTab={activeTab}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {(inventory || []).map((item) => (
              <Card key={item.id}>
                <CardHeader>
                  <CardTitle className={`text-lg ${getRarityColor(item.rarity)}`}>
                    {item.name}
                  </CardTitle>
                  <Badge variant="outline">{item.type}</Badge>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-3">{item.description}</p>

                  {/* Stat Bonuses */}
                  {item.statBonuses && Object.keys(item.statBonuses).length > 0 && (
                    <div className="mb-3">
                      <h5 className="font-semibold text-sm mb-1">Stat Bonuses:</h5>
                      <div className="text-xs space-y-1">
                        {Object.entries(item.statBonuses || {}).map(([stat, bonus]) => (
                          <div key={stat} className="flex justify-between">
                            <span className="capitalize">{stat}:</span>
                            <span className="text-green-600">+{bonus}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <Button
                    className="w-full"
                    variant="outline"
                    size="sm"
                  >
                    Equip
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* World Map Tab */}
        <TabsContent value="world-map" className="space-y-6" activeTab={activeTab}>
          <WorldMap
            heroLevel={hero?.level || 1}
            onVenueSelect={handleVenueSelect}
            discoveredVenues={discoveredVenues}
            onVenueBattle={handleVenueBattle}
          />
        </TabsContent>

        {/* Combo Trainer Tab */}
        <TabsContent value="combo-trainer" className="space-y-6" activeTab={activeTab}>
          <ComboTrainer
            heroLevel={hero?.level || 1}
            onComboMastered={handleComboMastered}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default GamePage;