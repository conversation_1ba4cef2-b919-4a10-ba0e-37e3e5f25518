import React, { useState, useEffect } from 'react';
import { useGame } from '../../context/GameContext';
import HeroCreation from '../../components/dancer/battle/HeroCreation';
import HeroProfile from '../../components/dancer/battle/HeroProfile';
import QuestList from '../../components/dancer/battle/QuestList';
import Battle from '../../components/dancer/battle/Battle';

const DanceHero: React.FC = () => {
  const { hero, opponents, currentBattle, endBattle, startBattle, fetchOpponents } = useGame();
  const [selectedOpponent, setSelectedOpponent] = useState<string | null>(null);
  
  // Fetch opponents when component mounts and hero is available
  useEffect(() => {
    if (hero && opponents.length === 0) {
      fetchOpponents();
    }
  }, [hero, opponents.length, fetchOpponents]);
  
  const handleEndBattle = () => {
    endBattle();
  };
  
  // If no hero, show creation screen
  if (!hero) {
    return (
      <div className="container mx-auto p-4">
        <HeroCreation />
      </div>
    );
  }
  
  // If in battle, show battle screen
  if (currentBattle) {
    return (
      <div className="container mx-auto p-4">
        <div className="max-w-2xl mx-auto">
          <Battle />
          
          <div className="mt-6 flex justify-center">
            <button
              onClick={handleEndBattle}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded"
            >
              Forfeit Battle
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  // Otherwise show hero dashboard
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold text-amber-500 mb-6 text-center">Dance Battle Heroes</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column: Hero profile */}
        <div className="lg:col-span-1">
          <HeroProfile />
        </div>
        
        {/* Right column: Quests + Battle options */}
        <div className="lg:col-span-2 space-y-6">
          <QuestList />
          
          {/* Battle section */}
          <div className="bg-gray-800 rounded-lg overflow-hidden shadow-lg">
            <div className="bg-gradient-to-r from-amber-900 to-amber-800 p-4">
              <h2 className="text-xl font-bold text-white">Dance Battles</h2>
            </div>
            <div className="p-6">
              <p className="text-gray-300 mb-4">
                Challenge other dancers to a dance battle! Each battle costs 1 energy.
              </p>
              
              {hero.energy <= 0 ? (
                <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 text-center mb-4">
                  <p className="text-red-300">
                    You don't have enough energy for a battle!
                  </p>
                  <p className="text-sm text-gray-300 mt-2">
                    Energy refills over time (1 per 2 hours)
                  </p>
                </div>
              ) : (
                <div className="bg-green-900/30 border border-green-800 rounded-lg p-4 text-center mb-4">
                  <p className="text-green-300">
                    You have {hero.energy} energy remaining!
                  </p>
                </div>
              )}
              
              <h3 className="text-white font-medium mb-3">Available Opponents:</h3>
              <div className="space-y-3">
                {opponents.map((opponent) => (
                  <div 
                    key={opponent.id}
                    className={`p-4 rounded-lg cursor-pointer transition-all duration-200
                      ${selectedOpponent === opponent.id
                        ? 'bg-amber-700/60 border-l-4 border-amber-500'
                        : 'bg-gray-700 hover:bg-gray-700/80'
                      }`}
                    onClick={() => setSelectedOpponent(opponent.id)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium text-white">{opponent.name}</h4>
                        <div className="text-xs text-gray-300">
                          Level {opponent.level} • {opponent.wins}W-{opponent.losses}L
                        </div>
                      </div>
                      {hero.level < opponent.level - 2 && (
                        <span className="text-xs bg-red-900/60 text-red-300 px-2 py-1 rounded">
                          Tough!
                        </span>
                      )}
                      {hero.level > opponent.level + 2 && (
                        <span className="text-xs bg-green-900/60 text-green-300 px-2 py-1 rounded">
                          Easy
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 flex justify-center">
                <button
                  onClick={() => startBattle(selectedOpponent!)}
                  disabled={!selectedOpponent || hero.energy <= 0}
                  className="px-6 py-3 bg-amber-600 hover:bg-amber-700 text-white font-bold rounded-lg transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Start Battle!
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DanceHero; 