import {
  ArrowLeftIcon,
  ArrowPathIcon,
  ChartBarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ClockIcon,
  FireIcon,
  HandThumbUpIcon,
  ShareIcon,
  StarIcon,
} from '@heroicons/react/24/outline';
import { HandThumbUpIcon as HandThumbUpSolidIcon } from '@heroicons/react/24/solid';
import React, { useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { FaSpinner } from 'react-icons/fa';
import { Link, useParams } from 'react-router-dom';
import { useAuthContext } from '../../context/AuthContext';
import { getChartSongs, shareSong, unvoteSuggestion, voteSuggestion } from '../../utils/api';
import { logError, logInfo } from '../../utils/logger';
import { VotersList } from '../../components/ui/VotersList';

// Workaround for TS2786: Assert FaSpinner as React.ElementType
const FaSpinnerIcon = FaSpinner as React.ElementType;

interface SongItem {
  id: string;
  title: string;
  channelTitle: string;
  danceStyle: string;
  thumbnailUrl: string;
  votes: number;
  hasVoted: boolean;
  isLocked?: boolean;
  youtubeVideoId: string;
  durationSeconds?: number;
  createdAt: string;
  user: {
    id: string;
    username: string;
  };
  voters: Array<{
    id: string;
    username: string;
    profile?: {
      displayName?: string;
      avatarUrl?: string;
    };
  }>;
}

interface ChartData {
  data: SongItem[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    limit: number;
  };
  chartInfo?: {
    type: string;
    style: string;
  };
}

// Helper to format duration
const formatDuration = (seconds?: number): string => {
  if (!seconds) return '--:--';
  
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

// Format date to a readable string
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '';
  
  const date = new Date(dateStr);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays < 1) {
    return 'Today';
  } else if (diffDays < 2) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else if (diffDays < 30) {
    return `${Math.floor(diffDays / 7)} week${Math.floor(diffDays / 7) > 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined 
    });
  }
};

// Helper function to get proper chart title
const getChartTitle = (chartType: string, style: string) => {
  const formattedStyle = style === 'all' ? 'All Styles' : style;
  
  switch (chartType.toLowerCase()) {
    case 'trending':
      return `Trending · ${formattedStyle}`;
    case 'weekly':
      return `Weekly Top · ${formattedStyle}`;
    case 'monthly':
      return `Monthly Hits · ${formattedStyle}`;
    case 'top':
      return `All Time Top · ${formattedStyle}`;
    default:
      return `${chartType} · ${formattedStyle}`;
  }
};

// Helper function to get chart icon
const getChartIcon = (chartType: string) => {
  switch (chartType.toLowerCase()) {
    case 'trending':
      return <FireIcon className="h-6 w-6 text-primary-600" />;
    case 'weekly':
      return <ClockIcon className="h-6 w-6 text-blue-600" />;
    case 'monthly':
      return <ChartBarIcon className="h-6 w-6 text-purple-600" />;
    case 'top':
      return <StarIcon className="h-6 w-6 text-amber-600" />;
    default:
      return <ChartBarIcon className="h-6 w-6 text-gray-600" />;
  }
};

const getChartGradient = (style: string) => {
  switch (style.toLowerCase()) {
    case 'bachata':
      return 'from-pink-500 to-purple-600';
    case 'salsa':
      return 'from-red-500 to-orange-600';
    case 'kizomba':
      return 'from-blue-500 to-indigo-600';
    case 'zouk':
      return 'from-purple-500 to-pink-600';
    case 'cha cha':
      return 'from-green-500 to-teal-600';
    case 'all':
      return 'from-slate-500 to-slate-700'; // Gradient for "All Styles"
    default:
      return 'from-gray-500 to-slate-600';
  }
};

const ChartDetails: React.FC = () => {
  const { chartType, style } = useParams<{ chartType: string; style: string }>();
  const [songs, setSongs] = useState<SongItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [songsPerPage, setSongsPerPage] = useState(25);
  const [voteLoading, setVoteLoading] = useState<Record<string, boolean>>({});
  const [shareLoading, setShareLoading] = useState<Record<string, boolean>>({});
  const { user: currentUser } = useAuthContext();
  const [isVoting, setIsVoting] = useState(false);
  const [selectedSong, setSelectedSong] = useState<SongItem | null>(null);
  const [showVoters, setShowVoters] = useState(false);

  const fetchSongs = useCallback(async () => {
    if (!chartType || !style) {
      setError('Invalid chart parameters');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    logInfo('ChartDetails: Fetching songs', { chartType, style, page: currentPage, limit: songsPerPage });

    try {
      const result = await getChartSongs(chartType, style, currentPage, songsPerPage);

      if (result.success) {
        let songData: SongItem[] = [];
        let currentPageValue = currentPage;
        let totalPagesValue = 1;
        let totalCountValue = 0;
        let limitValue = songsPerPage;

        // Handle different response formats
        if (result.data && Array.isArray(result.data)) {
          // If result.data is an array directly
          songData = result.data;
        } else if (result.data && typeof result.data === 'object' && 'data' in result.data && Array.isArray(result.data.data)) {
          // If result.data.data is an array (nested data)
          songData = result.data.data;
        }

        // Get pagination info from the right location
        if (result.pagination) {
          currentPageValue = result.pagination.currentPage || currentPage;
          totalPagesValue = result.pagination.totalPages || 1;
          totalCountValue = result.pagination.totalCount || 0;
          limitValue = result.pagination.limit || songsPerPage;
        } else if (result.data && typeof result.data === 'object' && result.data.pagination) {
          currentPageValue = result.data.pagination.currentPage || currentPage;
          totalPagesValue = result.data.pagination.totalPages || 1;
          totalCountValue = result.data.pagination.totalCount || 0;
          limitValue = result.data.pagination.limit || songsPerPage;
        }

        // Update state with extracted data
        setSongs(songData);
        setCurrentPage(currentPageValue);
        setTotalPages(totalPagesValue);
        setTotalCount(totalCountValue);
        setSongsPerPage(limitValue);
        
        logInfo('ChartDetails: Songs fetched successfully', { 
          count: songData.length,
          currentPage: currentPageValue,
          totalPages: totalPagesValue,
          totalCount: totalCountValue 
        });
      } else {
        throw new Error(result.message || 'Failed to fetch songs');
      }
    } catch (error: any) {
      logError('ChartDetails: Error fetching songs', error);
      setError('An error occurred while fetching songs: ' + (error.message || 'Unknown error'));
      toast.error('Failed to load songs');
    } finally {
      setLoading(false);
    }
  }, [chartType, style, currentPage, songsPerPage]);

  useEffect(() => {
    fetchSongs();
  }, [fetchSongs]);

  // Handle voting
  const handleVote = async (songId: string) => {
    if (!currentUser) {
      toast.error('Please log in to vote');
      return;
    }

    // Prevent voting on your own songs
    const song = songs.find(s => s.id === songId);
    if (song?.user.id === currentUser.id) {
      toast.error("You can't vote on your own suggestions");
      return;
    }

    if (voteLoading[songId]) return;

    setVoteLoading(prev => ({ ...prev, [songId]: true }));

    try {
      const result = await voteSuggestion(songId);
      if (result.success) {
        toast.success('Vote counted!');
        // Update local state
        setSongs(prev =>
          prev.map(song =>
            song.id === songId
              ? { 
                  ...song, 
                  votes: song.votes + 1, 
                  hasVoted: true,
                  voters: [...song.voters, {
                    id: currentUser.id || '',
                    username: currentUser.username || '',
                  }]
                }
              : song
          )
        );
      } else {
        toast.error(result.message || 'Failed to vote');
      }
    } catch (error: any) {
      toast.error('Failed to vote: ' + (error.message || 'Unknown error'));
      logError('ChartDetails: Vote error', error);
    } finally {
      setVoteLoading(prev => ({ ...prev, [songId]: false }));
    }
  };

  // Handle un-voting
  const handleUnvote = async (songId: string) => {
    if (!currentUser) return;
    if (voteLoading[songId]) return;

    setVoteLoading(prev => ({ ...prev, [songId]: true }));

    try {
      const result = await unvoteSuggestion(songId);
      if (result.success) {
        toast.success('Vote removed');
        // Update local state
        setSongs(prev =>
          prev.map(song =>
            song.id === songId
              ? { 
                  ...song, 
                  votes: song.votes - 1, 
                  hasVoted: false,
                  voters: song.voters.filter(voter => voter.id !== currentUser.id)
                }
              : song
          )
        );
      } else {
        toast.error(result.message || 'Failed to remove vote');
      }
    } catch (error: any) {
      toast.error('Failed to remove vote: ' + (error.message || 'Unknown error'));
      logError('ChartDetails: Unvote error', error);
    } finally {
      setVoteLoading(prev => ({ ...prev, [songId]: false }));
    }
  };

  // Handle sharing song
  const handleShare = async (songId: string, videoId: string) => {
    if (shareLoading[songId]) return;
    
    setShareLoading(prev => ({ ...prev, [songId]: true }));
    
    try {
      const result = await shareSong(songId, videoId);
      if (result.success) {
        toast.success('Link copied to clipboard!');
        logInfo('ChartDetails: Song link copied', { songId, url: result.url });
      } else {
        toast.error('Failed to copy link');
      }
    } catch (error: any) {
      toast.error('Failed to share song');
      logError('ChartDetails: Error sharing song', error);
    } finally {
      setShareLoading(prev => ({ ...prev, [songId]: false }));
    }
  };

  // Pagination
  const paginate = (pageNumber: number) => {
    if (pageNumber === currentPage) return;
    setCurrentPage(pageNumber);
    window.scrollTo(0, 0);
  };

  // Add a share function after the toggleVote function
  const shareSongHandler = (song: SongItem) => {
    try {
      // Create the URL to share
      const songUrl = `${window.location.origin}/charts/${chartType}/${style}?songId=${song.id}`;
      
      // Use the Web Share API if available
      if (navigator.share) {
        navigator.share({
          title: `${song.title} - ${song.channelTitle}`,
          text: `Check out this ${chartType} dance song: ${song.title} by ${song.channelTitle}`,
          url: songUrl
        })
        .then(() => {
          logInfo('ChartDetails: Song shared successfully', { songId: song.id });
          toast.success('Song shared successfully!');
        })
        .catch((error) => {
          logError('ChartDetails: Error sharing song', error);
          // Fall back to clipboard if share API fails
          copyToClipboard(songUrl);
        });
      } else {
        // Fall back to clipboard
        copyToClipboard(songUrl);
      }
    } catch (error) {
      logError('ChartDetails: Error in share handler', error);
      toast.error('Failed to share song');
    }
  };

  // Helper function to copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        toast.success('Link copied to clipboard!');
        logInfo('ChartDetails: Link copied to clipboard');
      })
      .catch((error) => {
        logError('ChartDetails: Error copying to clipboard', error);
        toast.error('Failed to copy link');
      });
  };

  // Implement the toggleVote function
  const toggleVote = async (song: SongItem) => {
    if (!currentUser || !song || isVoting) return;
    
    setIsVoting(true);
    
    try {
      const songId = song.id;
      const hasVoted = song.hasVoted;
      
      logInfo('ChartDetails: Toggling vote', { songId, currentVote: hasVoted });
      
      // Optimistically update the UI
      setSongs(prevSongs => 
        prevSongs.map(s => {
          if (s.id === songId) {
            return {
              ...s,
              hasVoted: !hasVoted,
              votes: hasVoted ? Math.max(0, (s.votes || 0) - 1) : (s.votes || 0) + 1,
              voters: hasVoted 
                ? (s.voters || []).filter(voter => voter.id !== currentUser.id) 
                : [...(s.voters || []), {
                    id: currentUser.id || '',
                    username: currentUser.username || '',
                  }]
            };
          }
          return s;
        })
      );
      
      // Make the API call
      const result = hasVoted 
        ? await unvoteSuggestion(songId)
        : await voteSuggestion(songId);
      
      if (!result.success) {
        // Revert UI update on error
        setSongs(prevSongs => 
          prevSongs.map(s => {
            if (s.id === songId) {
              return {
                ...s,
                hasVoted: hasVoted,
                votes: hasVoted ? (s.votes || 0) + 1 : Math.max(0, (s.votes || 0) - 1),
                voters: hasVoted 
                  ? [...(s.voters || []), {
                      id: currentUser.id || '',
                      username: currentUser.username || '',
                    }]
                  : (s.voters || []).filter(voter => voter.id !== currentUser.id)
              };
            }
            return s;
          })
        );
        toast.error(result.message || (hasVoted ? 'Failed to remove vote' : 'Failed to vote'));
      } else {
        toast.success(hasVoted ? 'Vote removed' : 'Vote added!');
      }
    } catch (error: any) {
      logError('ChartDetails: Error toggling vote', error);
      toast.error('Failed to process vote: ' + (error.message || 'Unknown error'));
    } finally {
      setIsVoting(false);
    }
  };

  // Handle clicking on a song to open in YouTube
  const handleSongClick = (song: SongItem) => {
    window.open(`https://www.youtube.com/watch?v=${song.youtubeVideoId}`, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-5xl">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between flex-wrap gap-3">
        <div className="flex items-center">
          <Link 
            to="/charts" 
            className="mr-3 flex items-center text-primary-600 hover:text-primary-800 font-medium"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            All Charts
          </Link>
          <h1 className="text-2xl font-bold flex items-center">
            {getChartIcon(chartType || '')}
            <span className="ml-2">{getChartTitle(chartType || '', style || '')}</span>
          </h1>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => fetchSongs()}
            className="p-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-all duration-200"
            aria-label="Refresh"
            disabled={loading}
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
          
          {loading ? (
            <div className="animate-pulse bg-gray-200 h-8 w-32 rounded-md"></div>
          ) : (
            <div className="text-gray-500 font-medium">
              {totalCount} song{totalCount !== 1 ? 's' : ''}
            </div>
          )}
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center py-20">
          <FaSpinnerIcon className="animate-spin h-10 w-10 text-primary-600" />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-red-700 text-center shadow-sm">
          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mb-4">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <p className="mb-4">{error}</p>
          <button
            onClick={() => fetchSongs()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors shadow-sm flex items-center mx-auto"
          >
            <ArrowPathIcon className="h-5 w-5 mr-2" />
            Try Again
          </button>
        </div>
      ) : songs.length === 0 ? (
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-8 text-center border border-gray-200 shadow-sm">
          <p className="text-lg font-medium text-gray-600 mb-2">No songs found for this chart</p>
          <p className="text-gray-500">Check back later or try a different chart</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 mb-8">
            {songs.map((song) => (
              <div
                key={song.id}
                className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 border border-gray-200 flex flex-col"
              >
                {/* Thumbnail */}
                <div className="relative overflow-hidden" onClick={() => handleSongClick(song)}>
                  <img 
                    src={song.thumbnailUrl?.replace('/default.jpg', '/mqdefault.jpg') || '/img/placeholder-youtube.png'} 
                    alt={song.title} 
                    className="w-full aspect-video object-cover transition-transform hover:scale-105"
                  />
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 text-xs rounded-md">
                    {formatDuration(song.durationSeconds)}
                  </div>
                  <div className="absolute top-2 right-2 bg-primary-500 bg-opacity-90 text-white px-2 py-1 text-xs rounded-full">
                    {song.danceStyle}
                  </div>
                </div>
                
                {/* Content */}
                <div className="p-4 flex-1 flex flex-col">
                  <div className="flex-1">
                    <h3 className="text-base font-semibold line-clamp-2 mb-1 hover:text-primary-600 cursor-pointer" onClick={() => handleSongClick(song)}>{song.title}</h3>
                    <p className="text-gray-600 text-sm mb-1">{song.channelTitle}</p>
                    <p className="text-gray-500 text-xs mb-3">
                      <span>Added by: {song.user.username}</span> • {formatDate(song.createdAt)}
                    </p>
                  </div>
                  
                  {/* Vote Stats */}
                  <div className="flex items-center mb-3">
                    <span className="flex items-center text-primary-600 font-medium">
                      <HandThumbUpSolidIcon className="h-4 w-4 mr-1" />
                      {song.votes} vote{song.votes !== 1 ? 's' : ''}
                    </span>
                    {song.voters && song.voters.length > 0 && (
                      <button 
                        className="ml-2 text-xs text-blue-600 hover:underline"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setSelectedSong(song);
                          setShowVoters(true);
                        }}
                      >
                        View voters
                      </button>
                    )}
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center justify-between">
                    <div className="flex space-x-2">
                      {/* Vote Button */}
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          toggleVote(song);
                        }}
                        disabled={isVoting || !currentUser}
                        className={`flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 ${
                          song.hasVoted
                            ? 'bg-primary-100 text-primary-700 hover:bg-primary-200'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        } ${!currentUser ? 'opacity-50 cursor-not-allowed' : ''}`}
                        title={!currentUser ? 'Log in to vote' : (song.hasVoted ? 'Remove vote' : 'Vote')}
                      >
                        {isVoting ? (
                          <FaSpinnerIcon className="animate-spin h-4 w-4 mr-1" />
                        ) : (
                          song.hasVoted ? (
                            <HandThumbUpSolidIcon className="h-4 w-4 mr-1" />
                          ) : (
                            <HandThumbUpIcon className="h-4 w-4 mr-1" />
                          )
                        )}
                        {song.hasVoted ? 'Voted' : 'Vote'}
                      </button>
                    </div>
                    
                    <div className="flex space-x-1">
                      {/* YouTube Button */}
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          window.open(`https://www.youtube.com/watch?v=${song.youtubeVideoId}`, '_blank', 'noopener,noreferrer');
                        }}
                        className="p-1.5 rounded-full text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors"
                        title="Open in YouTube"
                      >
                        <svg className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z" />
                        </svg>
                      </button>
                      
                      {/* Share Button */}
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          shareSongHandler(song);
                        }}
                        className="p-1.5 rounded-full text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
                        title="Share this song"
                      >
                        <ShareIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => paginate(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className={`p-2 rounded-md ${currentPage === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                    }`}
                >
                  <ChevronLeftIcon className="h-5 w-5" />
                </button>

                {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                  // Logic for showing the right pages
                  let pageNum = 1;

                  if (totalPages <= 5) {
                    // If we have 5 or fewer pages, show all pages 1 through totalPages
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    // If we're near the start, show pages 1 through 5
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    // If we're near the end, show the last 5 pages
                    pageNum = totalPages - 4 + i;
                  } else {
                    // Otherwise, show current page and 2 before and after
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => paginate(pageNum)}
                      className={`w-10 h-10 flex items-center justify-center rounded-md ${currentPage === pageNum
                          ? 'bg-primary-600 text-white font-bold'
                          : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                        }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  onClick={() => paginate(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className={`p-2 rounded-md ${currentPage === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                    }`}
                >
                  <ChevronRightIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Voters Modal */}
      {showVoters && selectedSong && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-md w-full max-h-[80vh] overflow-auto">
            <div className="p-4 border-b flex items-center justify-between">
              <h3 className="text-lg font-semibold">Voters</h3>
              <button
                onClick={() => {
                  setShowVoters(false);
                  setSelectedSong(null);
                }}
                className="p-1 hover:bg-gray-100 rounded-full"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-4">
              <h4 className="font-medium mb-2 line-clamp-1">{selectedSong.title}</h4>
              <p className="text-sm text-gray-500 mb-4">{selectedSong.votes} vote{selectedSong.votes !== 1 ? 's' : ''}</p>
              
              {selectedSong.voters && selectedSong.voters.length > 0 ? (
                <ul className="divide-y">
                  {selectedSong.voters.map(voter => (
                    <li key={voter.id} className="py-2 flex items-center">
                      <div className="w-8 h-8 bg-gray-200 rounded-full overflow-hidden mr-3 flex-shrink-0">
                        {voter.profile?.avatarUrl ? (
                          <img 
                            src={voter.profile.avatarUrl} 
                            alt={voter.username} 
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-primary-100 text-primary-700">
                            {voter.username.charAt(0).toUpperCase()}
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="font-medium">
                          {voter.profile?.displayName || voter.username}
                        </p>
                        {voter.profile?.displayName && (
                          <p className="text-sm text-gray-500">@{voter.username}</p>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-center text-gray-500 py-4">No voters yet</p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChartDetails; 