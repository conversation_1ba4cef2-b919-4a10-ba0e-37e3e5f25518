import React, { useEffect, useState, useRef, ChangeEvent, FormEvent } from 'react';
import { FaSearch, FaTimes, FaSpinner, FaYoutube, FaMusic, FaPlus } from 'react-icons/fa';
import { useWebSocket } from '../../context/WebSocketContext';
import { useAuthContext } from '../../context/AuthContext';
import { logInfo, logError } from '../../utils/logger';
import { searchYouTube, submitSuggestion, submitPlaylistSuggestion, getDanceStyles } from '../../utils/api';
import { MagnifyingGlassIcon, PaperAirplaneIcon, XCircleIcon, ArrowLeftIcon, CheckCircleIcon } from '@heroicons/react/24/solid';
import toast from 'react-hot-toast';
import { extractYouTubeVideoId } from '../../utils/youtubeUtils';
import { Link, useNavigate } from 'react-router-dom';

// Define structure for YouTube search results from backend
interface YouTubeSearchResult {
  videoId: string;
  title: string;
  channelTitle?: string;
  thumbnailUrl?: string;
}

// Define props for SuggestSong - removed preselectedStyle
interface SuggestSongProps {
  // No props needed anymore
}

// Workaround for TS2786: Assert FaYoutube as React.ElementType
const FaYoutubeIcon = FaYoutube as React.ElementType;
// Workaround for TS2786: Assert FaSpinner as React.ElementType
const FaSpinnerIcon = FaSpinner as React.ElementType;
// Add workaround for FaMusic
const FaMusicIcon = FaMusic as React.ElementType;
// Add workaround for FaPlus
const FaPlusIcon = FaPlus as React.ElementType;
// Add workaround for FaSearch
const FaSearchIcon = FaSearch as React.ElementType;

const SuggestSong: React.FC<SuggestSongProps> = () => {
  const { user: authUser, isLoading: authLoading, updateUser } = useAuthContext();
  const [searchTerm, setSearchTerm] = useState('');
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [youtubePlaylistUrl, setYoutubePlaylistUrl] = useState('');
  const [searchResults, setSearchResults] = useState<YouTubeSearchResult[]>([]);
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [selectedVideo, setSelectedVideo] = useState<YouTubeSearchResult | null>(null);
  const [danceStyle, setDanceStyle] = useState<string>('');
  const [availableStyles, setAvailableStyles] = useState<string[]>([]);
  const [showStyleModal, setShowStyleModal] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [submittingPlaylist, setSubmittingPlaylist] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);

  const navigate = useNavigate();
  const { socket, isConnected } = useWebSocket();

  // Refs
  const searchInputRef = useRef<HTMLInputElement>(null);
  const urlInputRef = useRef<HTMLInputElement>(null);
  const playlistUrlInputRef = useRef<HTMLInputElement>(null);

  // Fetch available dance styles on component mount
  useEffect(() => {
    const fetchStyles = async () => {
      try {
        const result = await getDanceStyles();
        if (result.success && Array.isArray(result.data)) {
          setAvailableStyles(result.data);
          logInfo('SuggestSong: Fetched dance styles', { styles: result.data });
        } else {
          logError('SuggestSong: Failed to fetch dance styles', { message: result.message });
          // Fallback to hardcoded styles
          setAvailableStyles(['Bachata', 'Salsa', 'Kizomba', 'Zouk', 'Cha Cha']);
        }
      } catch (error) {
        logError('SuggestSong: Error fetching dance styles', error);
        // Fallback to hardcoded styles
        setAvailableStyles(['Bachata', 'Salsa', 'Kizomba', 'Zouk', 'Cha Cha']);
      }
    };
    fetchStyles();
  }, []);

  // Manual search handler (replaces debounced search)
  const handleManualSearch = async () => {
    const query = searchTerm.trim();
    if (!query || loadingSearch) return;

    setLoadingSearch(true);
    setSearchError(null);
    setSearchResults([]);
    setSelectedVideo(null);
    setYoutubeUrl('');
    setSubmitError(null);
    setSubmitSuccess(null);
    logInfo('SuggestSong: Starting manual YouTube search', { query });

    try {
      const result = await searchYouTube(query);
      if (result.success && Array.isArray(result.data)) {
        setSearchResults(result.data);
        if (result.data.length === 0) {
          setSearchError('No videos found for that search.');
        }
      } else {
        throw new Error(result.message || 'Failed to get search results');
      }
    } catch (err: any) {
      toast.error(`Search failed: ${err.message}`);
      setSearchError(`Search failed: ${err.message}`);
      logError('SuggestSong: YouTube search error', err, { query });
    } finally {
      setLoadingSearch(false);
    }
  };

  // Handle search term change (no automatic search)
  const handleSearchTermChange = (e: ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchTerm(query);
    setSelectedVideo(null);
    setYoutubeUrl('');
    // Clear results when user starts typing a new search
    if (searchResults.length > 0) {
      setSearchResults([]);
      setSearchError(null);
    }
  };

  // Handle URL change
  const handleUrlChange = (e: ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setYoutubeUrl(url);
    setSelectedVideo(null);
    setSearchTerm('');
    setSearchResults([]);
    setSearchError(null);
    setYoutubePlaylistUrl('');
  };

  const handlePlaylistUrlChange = (e: ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setYoutubePlaylistUrl(url);
    setSelectedVideo(null);
    setSearchTerm('');
    setSearchResults([]);
    setSearchError(null);
    setYoutubeUrl('');
  };

  useEffect(() => {
    if (socket && isConnected) {
      logInfo('SuggestSong: Attaching notification listener to shared socket');
      const handleNotification = (msg: any) => {
        logInfo('SuggestSong: Received general notification via shared socket', msg);
        if (msg?.type === 'suggestion_submitted' || msg?.type === 'vote_success' || msg?.type === 'credits_updated' || msg?.type === 'comment:created') {
          // credits_updated is now handled by Layout.tsx via AuthContext
        } else if (msg?.message) {
          toast(msg.message);
        } else if (msg?.type?.startsWith('suggestion_')) {
          if (msg.type === 'suggestion_approved') {
            const message = `Your suggestion "${msg.suggestion?.title?.substring(0, 30)}..." was approved!`;
            toast.success(message);
          } else if (msg.type === 'suggestion_rejected') {
            const message = `Your suggestion "${msg.suggestion?.title?.substring(0, 30)}..." was rejected.`;
            toast(message);
          }
        }
      };
      socket.on('notification', handleNotification);
      return () => {
        logInfo('SuggestSong: Detaching notification listener from shared socket');
        socket.off('notification', handleNotification);
      };
    }
  }, [socket, isConnected]);

  const handleSelectVideo = (video: YouTubeSearchResult) => {
    setSelectedVideo(video);
    setSearchResults([]);
    setSearchTerm('');
    setYoutubeUrl('');
    setSearchError(null);
  };

  const handleClearSelection = () => {
    setSelectedVideo(null);
    setSearchTerm('');
    setYoutubeUrl('');
  };

  const handleStyleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setDanceStyle(event.target.value);
  };

  const handleProceedToStyleSelection = () => {
    const hasUrl = youtubeUrl.trim().length > 0;
    const videoIdFromUrl = hasUrl ? extractYouTubeVideoId(youtubeUrl) : null;
    const hasSearchSelection = !!selectedVideo;

    if (!authUser || authLoading) {
      toast.error('User data not loaded. Please wait or refresh.');
      return;
    }

    if (!hasUrl && !hasSearchSelection) {
      toast.error('Please search and select a video, or paste a valid YouTube URL.');
      return;
    }
    if (hasUrl && !videoIdFromUrl) {
      toast.error('Invalid YouTube URL provided. Please check the link.');
      setSubmitError('Invalid YouTube URL provided.');
      return;
    }

    // Show style selection modal
    setShowStyleModal(true);
  };

  const handleSubmitSuggestion = async () => {
    const hasUrl = youtubeUrl.trim().length > 0;
    const videoIdFromUrl = hasUrl ? extractYouTubeVideoId(youtubeUrl) : null;
    const hasSearchSelection = !!selectedVideo;

    if (!danceStyle) {
      toast.error('Please select a dance style.');
      setSubmitError('Please select a dance style.');
      return;
    }

    const videoIdToSubmit = videoIdFromUrl || selectedVideo?.videoId;
    if (!videoIdToSubmit) {
      toast.error('No video identified for submission.');
      return;
    }

    setSubmitting(true);
    setSubmitError(null);
    setSubmitSuccess(null);
    logInfo('SuggestSong: Submitting suggestion', { videoId: videoIdToSubmit, danceStyle });

    try {
      const payload: { youtubeVideoId: string; danceStyle: string; title: string; } = {
        youtubeVideoId: videoIdToSubmit,
        danceStyle: danceStyle,
        title: selectedVideo?.title || ''
      };
      const result = await submitSuggestion(payload);
      if (result.success) {
        toast.success('Song suggested successfully!');
        setSubmitSuccess('Suggestion submitted! It will appear once reviewed.');
        setSelectedVideo(null);
        setYoutubeUrl('');
        setSearchTerm('');
        setSearchResults([]);
        setDanceStyle('');
        setShowStyleModal(false);
      } else {
        throw new Error(result.message || 'Failed to submit suggestion');
      }
    } catch (error: any) {
      logError('SuggestSong: Error submitting suggestion', error);
      toast.error(`Suggestion failed: ${error.message}`);
      setSubmitError(error.message || 'An unexpected error occurred.');
    } finally {
      setSubmitting(false);
    }
  };

  const handlePlaylistSubmit = async () => {
    if (!youtubePlaylistUrl.trim()) {
      toast.error('Please enter a YouTube Playlist URL.');
      return;
    }

    setSubmittingPlaylist(true);
    setSubmitError(null);
    setSubmitSuccess(null);
    logInfo('SuggestSong: Submitting playlist', { youtubePlaylistUrl });

    try {
      // For playlists, we don't require a dance style - songs can be mixed
      const result = await submitPlaylistSuggestion({ youtubePlaylistUrl, danceStyle: 'Mixed' });
      if (result.success && result.data) {
        const message = result.message || `Successfully submitted ${result.data.count} song(s) for approval. ${result.data.failedCount > 0 ? `${result.data.failedCount} song(s) were skipped.` : ''}`;
        toast.success(message);
        setSubmitSuccess(message);
        setYoutubePlaylistUrl('');
      } else {
        throw new Error(result.message || 'Failed to submit playlist.');
      }
    } catch (error: any) {
      logError('SuggestSong: Error submitting playlist', error, { youtubePlaylistUrl });
      toast.error(`Playlist submission failed: ${error.message}`);
      setSubmitError(error.message || 'An unexpected error occurred during playlist submission.');
    } finally {
      setSubmittingPlaylist(false);
    }
  };

  if (authLoading) {
    return (
      <div className="p-4 text-center">
        <FaSpinnerIcon className="animate-spin h-8 w-8 text-primary-500 mx-auto" />
        <p className="mt-2 text-gray-600">Loading user information...</p>
      </div>
    );
  }

  if (!authUser) {
    return (
      <div className="p-4 text-center">
        <p className="text-red-500">You need to be logged in to suggest songs.</p>
        <Link to="/login" className="text-primary-600 hover:underline">Login here</Link>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 bg-white rounded-lg shadow-md">
      <button
        onClick={() => navigate(-1)}
        className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-100"
        aria-label="Close suggestion form"
      >
        <XCircleIcon className="h-7 w-7" />
      </button>
      
      <div className="flex items-center mb-4">
        <FaMusicIcon className="h-6 w-6 text-primary-500 mr-3" />
        <h2 className="text-xl font-semibold text-gray-800">
          Suggest a Song
        </h2>
      </div>

      {/* Paste YouTube URL */}
      <div className="mb-4">
        <label htmlFor="youtube-url" className="block text-sm font-medium text-gray-700 mb-1">Paste YouTube Video URL</label>
        <div className="relative">
          <input
            type="url"
            id="youtube-url"
            ref={urlInputRef}
            value={youtubeUrl}
            onChange={handleUrlChange}
            placeholder="https://www.youtube.com/watch?v=..."
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            disabled={submitting}
          />
          <FaYoutubeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-red-600" />
        </div>
      </div>

      <div className="mb-4 text-center text-gray-500">
        <span className="text-sm">OR</span>
      </div>

      {/* Search YouTube */}
      <div className="mb-4">
        <label htmlFor="youtube-search" className="block text-sm font-medium text-gray-700 mb-1">Search YouTube</label>
        <div className="flex space-x-2">
          <div className="relative flex-1">
            <input
              type="text"
              id="youtube-search"
              ref={searchInputRef}
              value={searchTerm}
              onChange={handleSearchTermChange}
              onKeyPress={(e) => e.key === 'Enter' && handleManualSearch()}
              placeholder="Search for a song..."
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              disabled={submitting || loadingSearch}
            />
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          </div>
          <button
            type="button"
            onClick={handleManualSearch}
            disabled={!searchTerm.trim() || loadingSearch || submitting}
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:bg-primary-400 transition-colors flex items-center"
          >
            {loadingSearch ? (
              <FaSpinnerIcon className="animate-spin h-4 w-4" />
            ) : (
              <>
                <FaSearchIcon className="h-4 w-4 mr-1" />
                Search
              </>
            )}
          </button>
        </div>
      </div>

      {/* Search/Parse Results */}
      {loadingSearch && (
        <div className="text-center py-4">
          <FaSpinnerIcon className="animate-spin h-6 w-6 text-primary-500 mx-auto" />
          <p className="mt-1 text-sm text-gray-500">Searching...</p>
        </div>
      )}
      {searchError && <p className="text-sm text-red-600 bg-red-50 p-2 rounded-md">{searchError}</p>}

      {searchResults.length > 0 && !selectedVideo && (
        <div className="mt-4 max-h-60 overflow-y-auto border border-gray-200 rounded-md shadow-sm">
          <ul className="divide-y divide-gray-200">
            {searchResults.map(video => (
              <li
                key={video.videoId}
                onClick={() => handleSelectVideo(video)}
                className="p-3 flex items-center space-x-3 hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <img src={video.thumbnailUrl || '/img/placeholder-youtube.png'} alt={video.title} className="h-12 w-20 object-cover rounded flex-shrink-0 border border-gray-200" onError={(e) => { (e.target as HTMLImageElement).src = '/img/placeholder-youtube.png'; }} />
                <div className="flex-grow min-w-0">
                  <p className="text-sm font-medium text-gray-800 truncate" title={video.title}>{video.title}</p>
                  {video.channelTitle && <p className="text-xs text-gray-500 truncate" title={video.channelTitle}>{video.channelTitle}</p>}
                </div>
                <button
                  type="button"
                  className="ml-auto p-1.5 rounded-full text-white bg-primary-500 hover:bg-primary-600 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1"
                  aria-label="Select this video"
                >
                  <FaPlusIcon className="h-3 w-3" />
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Selected Video */}
      {(selectedVideo || extractYouTubeVideoId(youtubeUrl)) && (
        <div className="mt-6 p-4 border border-primary-200 bg-primary-50 rounded-lg shadow-inner">
          {selectedVideo && (
            <div className="flex items-start space-x-3 mb-3">
              <img src={selectedVideo.thumbnailUrl || '/img/placeholder-youtube.png'} alt={selectedVideo.title} className="h-16 w-28 object-cover rounded-md border border-gray-300 shadow-sm" onError={(e) => { (e.target as HTMLImageElement).src = '/img/placeholder-youtube.png'; }} />
              <div>
                <p className="text-sm font-semibold text-primary-700">Selected: {selectedVideo.title}</p>
                {selectedVideo.channelTitle && <p className="text-xs text-gray-600">By: {selectedVideo.channelTitle}</p>}
              </div>
              <button onClick={handleClearSelection} className="ml-auto p-1 text-gray-500 hover:text-red-600 transition-colors">
                <XCircleIcon className="h-5 w-5" />
              </button>
            </div>
          )}
          {extractYouTubeVideoId(youtubeUrl) && !selectedVideo && (
            <div className="flex items-start space-x-3 mb-3">
              <FaYoutubeIcon className="h-10 w-10 text-red-500 flex-shrink-0" />
              <div>
                <p className="text-sm font-semibold text-primary-700">Video URL provided.</p>
                <p className="text-xs text-gray-600 break-all">{youtubeUrl}</p>
              </div>
              <button onClick={handleClearSelection} className="ml-auto p-1 text-gray-500 hover:text-red-600 transition-colors">
                <XCircleIcon className="h-5 w-5" />
              </button>
            </div>
          )}

          <button
            type="button"
            onClick={handleProceedToStyleSelection}
            disabled={submitting || authLoading || !authUser}
            className="mt-4 w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:bg-primary-400 transition-colors"
          >
            <PaperAirplaneIcon className="h-5 w-5 mr-2 transform -rotate-45" /> 
            Continue to Submit
          </button>
        </div>
      )}

      {/* Style Selection Modal */}
      {showStyleModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Select Dance Style</h3>
            <div className="mb-4">
              <label htmlFor="dance-style-modal" className="block text-sm font-medium text-gray-700 mb-2">Choose the dance style for this song:</label>
              <select
                id="dance-style-modal"
                value={danceStyle}
                onChange={handleStyleChange}
                className="w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">-- Select a style --</option>
                {availableStyles.map(style => (
                  <option key={style} value={style}>{style}</option>
                ))}
              </select>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowStyleModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmitSuggestion}
                disabled={!danceStyle || submitting}
                className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:bg-primary-400"
              >
                {submitting ? (
                  <><FaSpinnerIcon className="animate-spin h-4 w-4 mr-2 inline" /> Submitting...</>
                ) : (
                  'Submit Suggestion'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {submitError && <p className="mt-3 text-sm text-red-600 bg-red-50 p-3 rounded-md border border-red-200">Error: {submitError}</p>}
      {submitSuccess &&
        <div className="mt-3 p-3 rounded-md bg-green-50 border border-green-200 text-sm text-green-700 flex items-center">
          <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2 flex-shrink-0" />
          <span>{submitSuccess}</span>
        </div>
      }

      <div className="my-6 p-4 border border-dashed border-purple-300 rounded-lg bg-purple-50">
        <label htmlFor="youtube-playlist-url" className="block text-sm font-medium text-purple-700 mb-2">
          Paste YouTube Playlist URL
        </label>
        <div className="relative">
          <input
            type="url"
            id="youtube-playlist-url"
            ref={playlistUrlInputRef}
            value={youtubePlaylistUrl}
            onChange={handlePlaylistUrlChange}
            placeholder="https://www.youtube.com/playlist?list=... or https://music.youtube.com/playlist?list=..."
            className="w-full pl-10 pr-3 py-2 border border-purple-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
            disabled={submitting}
          />
          <FaYoutubeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-red-600" />
        </div>
        <button
          type="button"
          onClick={handlePlaylistSubmit}
          disabled={submitting || submittingPlaylist || !youtubePlaylistUrl.trim()}
          className="mt-3 w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:bg-purple-400 transition-colors"
        >
          {submittingPlaylist ? (
            <><FaSpinnerIcon className="animate-spin h-5 w-5 mr-2" /> Submitting Playlist...</>
          ) : (
            <><PaperAirplaneIcon className="h-5 w-5 mr-2 transform -rotate-45" /> Submit Entire Playlist</>
          )}
        </button>
        <p className="mt-2 text-xs text-purple-600">
          All songs from this playlist will be added to the suggestion pool for admin approval. Songs can be mixed styles. Supports both regular YouTube and YouTube Music playlist formats.
        </p>
      </div>
    </div>
  );
};

export default SuggestSong;