import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { logInfo } from '../utils/logger';
import SocialLinks from '../components/ui/SocialLinks';

// Placeholder for icons - you might use react-icons or similar
const MusicIcon = () => <span>🎵</span>;
const PlaylistIcon = () => <span>📊</span>;
const VoteIcon = () => <span>👍</span>;

const DancerDashboard = () => {
  const navigate = useNavigate(); // Hook for logout or other actions

  // Placeholder function for logout
  const handleLogout = async () => {
    logInfo('Dancer initiated logout');
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
      // Clear any client-side auth state if needed (depends on auth implementation)
      navigate('/login'); // Redirect to login after logout
    } catch (error) {
      console.error('Logout failed:', error);
      // Handle logout error (e.g., show a message)
    }
  };

  return (
    // Consistent gradient background and full height
    <div className="min-h-screen w-full bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-100 p-4 sm:p-6 lg:p-8">
      {/* Main content container */}
      <div className="max-w-4xl mx-auto">

        {/* Header Section with Welcome and Logout */}
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl sm:text-4xl font-bold tracking-tight bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent">
            Welcome, Dancer!
          </h1>
          <button
            onClick={handleLogout}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
          >
            Logout
          </button>
        </div>

        {/* Grid for main actions */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Suggest a Song Card */}
          <Link
            to="/dancer/suggest"
            className="group flex flex-col justify-between p-6 bg-white rounded-xl shadow-lg hover:shadow-xl hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-300 ease-in-out min-h-[150px]"
            aria-label="Suggest a Song"
            onClick={() => logInfo('DancerDashboard: Navigate to Suggest Song')}
          >
            <div>
              <div className="flex items-center mb-3">
                <div className="p-2 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full mr-3"><MusicIcon /></div>
                <h2 className="text-xl font-semibold text-gray-800">Suggest a Song</h2>
              </div>
              <p className="text-gray-600 text-sm mb-4">Use your credits to add your favorite track to the list.</p>
            </div>
             <span className="text-sm font-medium text-primary-600 group-hover:text-primary-800 self-end">Go Suggest &rarr;</span>
          </Link>

          {/* View Playlist Card */}
          <Link
            to="/dancer/playlist"
            className="group flex flex-col justify-between p-6 bg-white rounded-xl shadow-lg hover:shadow-xl hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-300 ease-in-out min-h-[150px]"
            aria-label="View Playlist"
            onClick={() => logInfo('DancerDashboard: Navigate to View Playlist')}
          >
            <div>
              <div className="flex items-center mb-3">
                 <div className="p-2 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full mr-3"><PlaylistIcon /></div>
                <h2 className="text-xl font-semibold text-gray-800">View Playlist</h2>
              </div>
              <p className="text-gray-600 text-sm mb-4">See what's playing now and what's coming up next.</p>
            </div>
            <span className="text-sm font-medium text-primary-600 group-hover:text-primary-800 self-end">View Now &rarr;</span>
          </Link>

          {/* Vote on Suggestions Card */}
          <Link
            to="/dancer/vote"
            className="group flex flex-col justify-between p-6 bg-white rounded-xl shadow-lg hover:shadow-xl hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-300 ease-in-out min-h-[150px]"
            aria-label="Vote on Suggestions"
            onClick={() => logInfo('DancerDashboard: Navigate to Vote on Suggestions')}
          >
             <div>
              <div className="flex items-center mb-3">
                <div className="p-2 bg-gradient-to-br from-green-100 to-green-200 rounded-full mr-3"><VoteIcon /></div>
                <h2 className="text-xl font-semibold text-gray-800">Vote on Suggestions</h2>
              </div>
              <p className="text-gray-600 text-sm mb-4">Help decide which suggested songs make it to the playlist.</p>
            </div>
             <span className="text-sm font-medium text-primary-600 group-hover:text-primary-800 self-end">Cast Vote &rarr;</span>
          </Link>

           {/* Placeholder for other sections like User Info/Credits */}
           <div className="sm:col-span-2 lg:col-span-3 mt-8 p-6 bg-white/80 rounded-xl shadow-md backdrop-blur-sm">
             <h3 className="text-lg font-semibold text-gray-700 mb-3">Your Stats</h3>
             <p className="text-gray-600">Credits: [Placeholder]</p>
             <p className="text-gray-600">Suggestions Made: [Placeholder]</p>
             {/* Add more dancer-specific info here */}
           </div>

        </div>
        
        {/* Add Social Links Section */}
        <div className="mt-12">
            <SocialLinks />
        </div>
        
      </div>
    </div>
  );
};

export default DancerDashboard; 