import React, { useState, useEffect, useRef, useCallback } from 'react';
import io, { Socket } from 'socket.io-client';
import { formatDistanceToNow } from 'date-fns';
import { logInfo, logError } from '../utils/logger';
import toast from 'react-hot-toast';
import { Link } from 'react-router-dom';
import {
  PlayIcon,
  LinkIcon,
  ArrowPathIcon,
  InformationCircleIcon,
  WifiIcon,
  ListBulletIcon,
  PlayPauseIcon,
  ClockIcon,
  StopIcon,
  EyeIcon
} from '@heroicons/react/24/solid';
import { FaSpinner, FaYoutube } from 'react-icons/fa';
import { useAuthContext } from '../context/AuthContext';
import '../styles/animations.css';
import { MusicalNoteIcon as HeroMusicalNoteIcon } from '@heroicons/react/24/outline';

/**
 * ---------------------------------------------------------
 *  🎧  PUBLIC PLAYER v3 — EXTERNAL DEVICE MODE ONLY
 *  --------------------------------------------------------
 *  ▸ Core changes:
 *    1. All internal audio playback logic (streaming from server, YouTube)
 *       has been REMOVED.
 *    2. The player now assumes audio is handled by an external, offline device.
 *       It only reflects the state (current song, play/pause, progress)
 *       received from the backend.
 *    3. Progress is simulated locally using a timer when isPlaying is true,
 *       and can be synced with `currentSongProgressSeconds` from the server.
 *    4. The <audio> element is kept hidden, primarily for potential Media
 *       Session API integration for metadata display, but it no longer
 *       loads or plays any sound.
 *    5. `isBuffering` state is effectively disabled as no streaming occurs.
 *    6. "Modes" related to playback source are simplified; the player
 *       consistently behaves as a reflector for an external source.
 *
 *  💄  NO VISUAL/CSS CHANGES were done — only functional logic was touched.
 * ------------------------------------------------------------------------
 */

/* ---------- HELPER UTILS ---------- */
// buildStreamUrl removed as it's no longer used.

// Workarounds for TS2786
const FaSpinnerIcon = FaSpinner as React.ElementType;
const FaYoutubeIcon = FaYoutube as React.ElementType;

// Dance style color definitions - REVISED TO ALIGN WITH PALETTE ACCENTS
// Using palette accents (pinks, teals, yellows, purples) for dance styles
const danceStyleColors: Record<string, {
  text: string, // Text color on the tag
  bg: string,   // Background of the tag
  border?: string, // Optional border for the tag
  gradient?: string, // Optional gradient for more complex tags (not used for simple tags here)
  pulseAnimation?: string, // For currently playing style highlight
  progressBar?: string // Color for progress bars if style-specific
}> = {
  'salsa': { // Using a vibrant pink/fuchsia variant from primary actions/accents
    text: 'text-white',
    bg: 'bg-red-600',
    pulseAnimation: 'animate-pulse-red',
    progressBar: 'bg-red-500'
  },
  'bachata': { // Using a lighter, distinct pink or purple
    text: 'text-white',
    bg: 'bg-green-600',
    pulseAnimation: 'animate-pulse-green',
    progressBar: 'bg-green-500'
  },
  'kizomba': { // Using a distinct teal
    text: 'text-white',
    bg: 'bg-sky-600',
    pulseAnimation: 'animate-pulse-sky',
    progressBar: 'bg-sky-500'
  },
  'zouk': { // Using a vibrant yellow
    text: 'text-yellow-900', // Darker text for yellow usually offers better contrast
    bg: 'bg-yellow-400',    // Brighter yellow from palette
    pulseAnimation: 'animate-pulse-yellow',
    progressBar: 'bg-yellow-500'
  },
  'cha cha': {
    text: 'text-white',
    bg: 'bg-indigo-600',
    pulseAnimation: 'animate-pulse-purple', // Using purple pulse as it's visually close to indigo
    progressBar: 'bg-indigo-500'
  },
  'default': {
    text: 'text-white',
    bg: 'bg-gray-500', // Darker gray for default, with white text
    progressBar: 'bg-gray-400'
  }
};

// Get style colors based on dance style name
const getStyleColors = (danceStyle: string = '') => {
  const style = danceStyle.toLowerCase();
  return danceStyleColors[style] || danceStyleColors.default;
};

interface PlaylistItem {
  id: string;
  youtubeVideoId: string; // Still used for metadata (thumbnail, link to YouTube)
  title: string;
  thumbnailUrl: string;
  channelTitle: string;
  durationSeconds?: number;
  danceStyle?: string;
  isSuggestion?: boolean;
  suggestedBy?: string;
  playbackStartTime?: string | null;
  lastPlaybackSyncTime?: string | null;
}

interface PublicPlaylistState {
  currentSong: PlaylistItem | null;
  playlistItems: PlaylistItem[];
  isPlaying: boolean;
  currentSongStartTime?: string | null;
  serverTime?: string;
  youtubePlaylistId?: string | null;
  externalMirrorPlaylistId?: string | null;
  generatedPlaylistHistoryId?: string | null;
  lastUpdatedAt: string | null;
  activeStyleRotation?: string[];
  operationalMode: 'GENERATED' | 'EXTERNAL_MIRROR'; // Kept for potential minor UI distinctions if server sends it
  currentSongProgressSeconds?: number;
}

const PublicPlayer = () => {
  const { isAuthenticated, user } = useAuthContext();
  const [playerState, setPlayerState] = useState<PublicPlaylistState | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showConnectionError, setShowConnectionError] = useState<boolean>(false);
  const [currentProgress, setCurrentProgress] = useState<number>(0);
  const [totalDurationSeconds, setTotalDurationSeconds] = useState<number>(0);
  const [elapsedDurationSeconds, setElapsedDurationSeconds] = useState<number>(0);
  const [timeUntilEachSong, setTimeUntilEachSong] = useState<Record<string, string>>({});
  const [isBuffering, setIsBuffering] = useState(false); // Will remain false

  const socketRef = useRef<typeof Socket | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null); // Kept for MediaSession API
  // refreshIntervalRef removed as polling is not used.
  // progressIntervalRef removed as old progress simulation is not used.

  const externalModeTimerRef = useRef<NodeJS.Timeout | null>(null); // Timer for progress simulation

  const isAdmin = user?.isAdmin || false;

  const formatDuration = useCallback((totalSeconds?: number): string => {
    if (totalSeconds === undefined || totalSeconds === null || isNaN(totalSeconds) || totalSeconds < 0) {
      return '?';
    }
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = Math.floor(totalSeconds % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  }, []);

  const calculateProgressPercent = useCallback((progress?: number, duration?: number): number => {
    if (!progress || !duration || duration <= 0) {
      return 0;
    }
    return Math.min(100, Math.max(0, (progress / duration) * 100));
  }, []);

  const startExternalModeTimer = useCallback(() => {
    if (externalModeTimerRef.current) {
      clearInterval(externalModeTimerRef.current);
    }
    externalModeTimerRef.current = setInterval(() => {
      setCurrentProgress(prev => {
        // Ensure progress doesn't exceed song duration from playerState
        const songDuration = playerState?.currentSong?.durationSeconds;
        if (songDuration && prev + 1 > songDuration) {
          if (externalModeTimerRef.current) clearInterval(externalModeTimerRef.current); // Stop timer
          return songDuration;
        }
        return prev + 1;
      });
    }, 1000);
  }, [playerState?.currentSong?.durationSeconds]); // Dependency on song duration for safety stop

  const stopExternalModeTimer = useCallback(() => {
    if (externalModeTimerRef.current) {
      clearInterval(externalModeTimerRef.current);
      externalModeTimerRef.current = null;
    }
  }, []);

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw-media.js')
        .catch(err => logError('Failed to register media Service Worker', err));
    }
  }, []);

  const togglePlayback = useCallback(() => {
    // This function will be called by Media Session API or Admin UI
    // It informs the backend about the desired state change.
    if (!isAdmin || !playerState) return; // Ensure admin and playerState exist

    if (playerState.isPlaying) {
      stopPlayback(); // stopPlayback makes an API call
    } else {
      startPlayback(); // startPlayback makes an API call
    }
  }, [playerState, isAdmin]); // Added isAdmin dependency, and start/stopPlayback will be deps for this if not stable


  useEffect(() => {
    if (!playerState?.currentSong || !('mediaSession' in navigator)) return;

    const { currentSong } = playerState;
    navigator.mediaSession.metadata = new window.MediaMetadata({
      title: currentSong.title,
      artist: currentSong.channelTitle || 'Unknown',
      artwork: [
        { src: currentSong.thumbnailUrl, sizes: '512x512', type: 'image/jpeg' }
      ]
    });

    navigator.mediaSession.setActionHandler('play', () => {
      logInfo('MediaSession: Play action triggered');
      togglePlayback();
    });
    navigator.mediaSession.setActionHandler('pause', () => {
      logInfo('MediaSession: Pause action triggered');
      togglePlayback();
    });
    navigator.mediaSession.setActionHandler('nexttrack', () => { /* Not implemented for now */ });
    navigator.mediaSession.setActionHandler('previoustrack', () => { /* Not implemented for now */ });

  }, [playerState?.currentSong?.id, playerState?.currentSong?.title, playerState?.currentSong?.channelTitle, playerState?.currentSong?.thumbnailUrl, togglePlayback]);


  /* ---------- EFFECT: MANAGE PROGRESS TIMER & AUDIO ELEMENT (NO PLAYBACK) ---------- */
  useEffect(() => {
    const audio = audioRef.current;
    // This effect primarily manages the progress timer based on play state.
    // It no longer interacts with audio.src or audio.play() for actual playback.

    if (audio && audio.src) { // If audio src was ever set (e.g., by old code)
      audio.pause(); // Ensure it's paused
      audio.src = ''; // Clear src
      // audio.load(); // Not needed for an empty src and might cause issues.
    }
    setIsBuffering(false); // No buffering in this mode

    if (playerState?.isPlaying) {
      startExternalModeTimer();
    } else {
      stopExternalModeTimer();
    }

    // Cleanup timer on component unmount or when dependencies change
    return () => {
      stopExternalModeTimer();
    };
  }, [playerState?.isPlaying, playerState?.currentSong?.id, startExternalModeTimer, stopExternalModeTimer]);


  /* EFFECT: WIRE UP <audio> EVENTS - REMOVED as audio element is not used for playback */
  // The useEffect that previously handled audio.timeupdate, canplaythrough, etc. is removed.

  const handlePlayerUpdate = useCallback((data: Partial<PublicPlaylistState>) => {
    logInfo('PublicPlayer: WebSocket received player:state:update', data);

    setPlayerState(prevState => {
      if (!data) return prevState;
      const baseState = prevState || {} as PublicPlaylistState;

      const newSongId = data.currentSong?.id;
      const oldSongId = baseState.currentSong?.id;
      const isNewSong = newSongId && newSongId !== oldSongId;

      let newCurrentSongProcessed: PlaylistItem | null = null;

      if (data.currentSong) {
        if (isNewSong) {
          newCurrentSongProcessed = { ...data.currentSong };
          if (newCurrentSongProcessed && newCurrentSongProcessed.id && !newCurrentSongProcessed.danceStyle && baseState.playlistItems) {
            const playlistItemMatch = baseState.playlistItems.find(
              item => item.id === newCurrentSongProcessed!.id
            );
            if (playlistItemMatch && playlistItemMatch.danceStyle) {
              newCurrentSongProcessed.danceStyle = playlistItemMatch.danceStyle;
            }
          }
        } else {
          newCurrentSongProcessed = { ...(baseState.currentSong || null), ...data.currentSong };
        }
      } else if (baseState.currentSong) {
        newCurrentSongProcessed = baseState.currentSong;
      }

      const newState = {
        ...baseState,
        ...data,
        currentSong: newCurrentSongProcessed,
      } as PublicPlaylistState;

      let progressToSet: number | null = null;

      if (isNewSong) {
        logInfo('PublicPlayer: New song detected in update.', { newSongId, oldSongId });
        if (typeof data.currentSongProgressSeconds === 'number') {
          progressToSet = data.currentSongProgressSeconds;
          logInfo('PublicPlayer: New song, setting progress from server data.', { progress: progressToSet });
        } else {
          progressToSet = 0;
          logInfo('PublicPlayer: New song, resetting progress to 0.');
        }
      } else if (data.currentSong?.id === baseState.currentSong?.id && typeof data.currentSongProgressSeconds === 'number') {
        // Update for the same song, and progress is explicitly provided
        progressToSet = data.currentSongProgressSeconds;
        logInfo('PublicPlayer: Same song, updating progress from server data.', { progress: progressToSet });
      }

      if (progressToSet !== null) {
        setCurrentProgress(progressToSet);
      }


      // Recalculate total and elapsed durations
      // This part needs to use the latest currentProgress, which might have just been set.
      // We can pass `progressToSet` if available, or read `currentProgress` directly (it might be stale in this closure if not careful).
      // For simplicity, let's assume `currentProgress` state will be updated and subsequent calculations in other effects will use it.
      // Or, we can use `progressToSet` if it's not null, otherwise use the existing `currentProgress` from state for calculation.
      const currentProgressForCalc = progressToSet !== null ? progressToSet : (prevState ? currentProgress : 0);


      if (data.playlistItems || data.currentSong || data.currentSongProgressSeconds !== undefined) {
        let totalDuration = 0;
        let elapsedDurationCalc = 0;
        if (newState.playlistItems && newState.playlistItems.length > 0) {
          newState.playlistItems.forEach(item => {
            if (item && typeof item.durationSeconds === 'number') {
              totalDuration += item.durationSeconds;
            }
          });

          if (newState.currentSong && typeof newState.currentSong.durationSeconds === 'number') {
            const currentSongIndex = newState.playlistItems.findIndex(
              item => item?.id === newState.currentSong?.id
            );
            if (currentSongIndex > -1) {
              for (let i = 0; i < currentSongIndex; i++) {
                const playlistItem = newState.playlistItems[i];
                if (playlistItem && typeof playlistItem.durationSeconds === 'number') {
                  elapsedDurationCalc += playlistItem.durationSeconds;
                }
              }
              elapsedDurationCalc += currentProgressForCalc;
            } else if (newState.playlistItems[0]?.id === newState.currentSong.id) {
              elapsedDurationCalc += currentProgressForCalc;
            }
          }
        }
        setTotalDurationSeconds(totalDuration);
        setElapsedDurationSeconds(elapsedDurationCalc);
      }

      return newState;
    });
  }, [currentProgress]); // currentProgress is read for elapsedDurationCalc, keep if necessary or refactor calc.
  // Consider if `currentProgress` is truly needed here. If `elapsedDurationCalc` logic relies on it,
  // and `handlePlayerUpdate` is a dependency for `useEffect` setting up sockets, this could be an issue.
  // For now, assuming the user wanted minimal changes, let's keep it.
  // A safer way: Recalculate elapsedDuration in a separate `useEffect` that depends on `currentProgress` and `playerState`.
  // For now, keeping it as it was, but noting this potential optimization/refinement point.


  useEffect(() => {
    if (playerState) {
      setLoading(false);
      setError(null);
      setShowConnectionError(false);
    }
  }, [playerState]);

  const fetchPlayerState = useCallback(async () => {
    try {
      logInfo('PublicPlayer: Fetching initial player state via API');
      const response = await fetch('/api/public/player-state', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
      if (!response.ok) {
        throw new Error(`API call failed: ${response.status} ${response.statusText}`);
      }
      const data = await response.json();

      // Regardless of operationalMode, if currentSongProgressSeconds is available, use it.
      if (typeof data.currentSongProgressSeconds === 'number') {
        setCurrentProgress(data.currentSongProgressSeconds);
      } else if (data.currentSong) {
        setCurrentProgress(0); // Default to 0 if song exists but no progress specified
      }

      handlePlayerUpdate(data);
    } catch (errorInstance) {
      logError('PublicPlayer: Error fetching initial player state', errorInstance);
      const errorMessage = errorInstance instanceof Error ? errorInstance.message : 'Unknown error';
      setError(`Failed to load player state: ${errorMessage}. Displaying placeholder data while attempting to connect for updates...`);
      setLoading(false);

      const mockPlaylistState: PublicPlaylistState = {
        currentSong: {
          id: 'mock-song-1',
          youtubeVideoId: 'dQw4w9WgXcQ',
          title: 'Waiting for Connection...',
          thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg',
          channelTitle: 'Official Artist Channel',
          durationSeconds: 212,
          danceStyle: 'default',
          isSuggestion: false,
          suggestedBy: 'System',
          playbackStartTime: new Date().toISOString(),
          lastPlaybackSyncTime: new Date().toISOString(),
        },
        playlistItems: [ /* ... mock items ... */],
        isPlaying: false,
        currentSongStartTime: new Date().toISOString(),
        serverTime: new Date().toISOString(),
        youtubePlaylistId: 'PLDefaultMockPlaylist',
        externalMirrorPlaylistId: null,
        generatedPlaylistHistoryId: null,
        lastUpdatedAt: new Date().toISOString(),
        activeStyleRotation: ['default', 'salsa', 'bachata'],
        operationalMode: 'GENERATED', // Mock data can keep this, our playback logic ignores it for audio handling
        currentSongProgressSeconds: 0,
      };
      handlePlayerUpdate(mockPlaylistState);
      setCurrentProgress(mockPlaylistState.currentSongProgressSeconds || 0);
    }
  }, [handlePlayerUpdate]);

  const startPlayback = async () => {
    if (!isAdmin) return;
    logInfo('[PublicPlayer] Admin action: Attempting to start playback.');
    // Removed EXTERNAL_MIRROR specific logic that bypassed API. Always call API.
    try {
      const response = await fetch('/api/playback/start', { method: 'POST' });
      const responseData = await response.json();
      if (!response.ok) throw new Error(responseData.message || 'Failed to start playback');
      toast.success(responseData.message || 'Playlist started successfully!');
      // State will update via WebSocket, then useEffect will handle timer.
    } catch (err: any) {
      toast.error(`Failed to start playback: ${err.message}`);
      logError('Error starting playback:', err);
    }
  };

  const stopPlayback = async () => {
    if (!isAdmin) return;
    logInfo('[PublicPlayer] Admin action: Attempting to stop playback.');
    // Removed EXTERNAL_MIRROR specific logic that bypassed API. Always call API.
    try {
      const response = await fetch('/api/playback/stop', { method: 'POST' });
      const responseData = await response.json();
      if (!response.ok) throw new Error(responseData.message || 'Failed to stop playback');
      toast.success(responseData.message || 'Playlist stopped successfully!');
      // State will update via WebSocket, then useEffect will handle timer.
    } catch (err: any) {
      toast.error(`Failed to stop playback: ${err.message}`);
      logError('Error stopping playback:', err);
    }
  };

  // The existing togglePlayback was for admin UI, media session needs one too.
  // Re-aliasing the admin UI one for clarity or merging them.
  // The one used by MediaSession API is already named `togglePlayback`.
  // The one used by Admin UI buttons can also be `togglePlayback`.
  const adminTogglePlayback = useCallback(() => {
    if (!playerState) return; // No player state, nothing to toggle
    // This function is called by the admin UI play/pause button
    // It uses the already defined startPlayback and stopPlayback which make API calls
    if (playerState.isPlaying) {
      stopPlayback();
    } else {
      startPlayback();
    }
  }, [playerState, startPlayback, stopPlayback]); // Add start/stopPlayback if they are not stable themselves


  const restartFromSong = useCallback(async (songId: string) => {
    if (!isAdmin || !songId) return;
    logInfo(`PublicPlayer: Admin action: Attempting to restart playback from song ${songId}`);
    try {
      const response = await fetch('/api/playback/restart-from-song', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', },
        body: JSON.stringify({ songId })
      });
      const responseData = await response.json();
      if (!response.ok) throw new Error(responseData.message || 'Failed to restart playback');
      toast.success(responseData.message || 'Restarted playlist from selected song');
    } catch (err: any) {
      toast.error(`Failed to restart playback: ${err.message}`);
      logError('Error restarting playback from song:', err);
      if (socketRef.current) {
        socketRef.current.emit('admin:restart_playback_from_song', { songId });
      }
    }
  }, [isAdmin]);

  useEffect(() => {
    fetchPlayerState();

    logInfo('PublicPlayer: Attempting to connect to WebSocket');
    socketRef.current = io({
      path: '/socket.io',
      reconnectionAttempts: 10,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      timeout: 10000
    });

    if (socketRef.current) {
      socketRef.current.on('connect', () => {
        logInfo('PublicPlayer: WebSocket connected', { id: socketRef.current?.id });
        setError(null);
        setShowConnectionError(false);
        socketRef.current?.emit('joinRoom', 'public');
        fetchPlayerState();
      });

      socketRef.current.on('disconnect', (reason: any) => {
        logInfo('PublicPlayer: WebSocket disconnected', { reason });
        if (reason === 'io server disconnect' || reason === 'transport close') {
          setShowConnectionError(true);
        }
      });

      socketRef.current.on('connect_error', (err: any) => {
        logError('PublicPlayer: WebSocket connection error', err);
        setShowConnectionError(true);
        setLoading(false);
      });

      socketRef.current.on('player:state:update', handlePlayerUpdate);
      socketRef.current.on('player:song:changed', (data: any) => { handlePlayerUpdate(data); });
      socketRef.current.on('player:status:changed', (data: any) => { handlePlayerUpdate(data); });
      socketRef.current.on('player:playlist:changed', (data: any) => { handlePlayerUpdate(data); });

      socketRef.current.on('error', (errMsg: string) => {
        logError('PublicPlayer: Received general WebSocket error', { message: errMsg });
        setShowConnectionError(true);
      });
      socketRef.current.on('notification', (msg: { message?: string; type?: string }) => {
        if (msg.message && msg.type === 'PUBLIC_ANNOUNCEMENT') {
          toast(msg.message, { icon: '📢' });
        }
      });
    } else {
      logError('PublicPlayer: Failed to initialize WebSocket instance.');
      setShowConnectionError(true);
      setLoading(false);
    }

    return () => {
      if (socketRef.current) {
        logInfo('PublicPlayer: Cleaning up WebSocket connection', { id: socketRef.current.id });
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      // No need to pause audioRef.current as it's not playing.
      // externalModeTimerRef is cleaned up by its own useEffect.
    };
  }, [handlePlayerUpdate, fetchPlayerState]);

  /* EFFECT: PROGRESS SIMULATION (OLD) - REMOVED */
  // The useEffect that used progressIntervalRef is removed. Progress is handled by externalModeTimerRef.

  useEffect(() => {
    if (!playerState || !playerState.currentSong || !playerState.playlistItems || playerState.playlistItems.length === 0) {
      setTimeUntilEachSong({});
      return;
    }

    const { currentSong, playlistItems } = playerState;
    // currentProgress is now from our local timer/state
    const currentSongActualProgress = currentProgress;

    const newTimeUntilEachSong: Record<string, string> = {};
    let cumulativeDelaySeconds = 0;
    let foundCurrentSongInLoop = false;

    const remainingTimeForCurrentSong = Math.max(0, (currentSong.durationSeconds || 0) - currentSongActualProgress);

    for (const item of playlistItems) {
      if (!item || !item.id || !item.durationSeconds) continue;

      if (item.id === currentSong.id) {
        foundCurrentSongInLoop = true;
        cumulativeDelaySeconds = remainingTimeForCurrentSong;
        continue;
      }

      if (foundCurrentSongInLoop) {
        // Add 1 second gap between each song
        newTimeUntilEachSong[item.id] = formatDuration(cumulativeDelaySeconds);
        cumulativeDelaySeconds += item.durationSeconds + 1; // Add 1 second between songs
      }
    }
    setTimeUntilEachSong(newTimeUntilEachSong);

  }, [playerState, currentProgress, formatDuration]);

  const previousSongIdRef = useRef<string | null>(null);

  useEffect(() => {
    if (playerState?.currentSong?.id && previousSongIdRef.current !== playerState.currentSong.id) {
      previousSongIdRef.current = playerState.currentSong.id;
      setTimeout(() => {
        const currentSongElement = document.querySelector('.song-list-item-currently-playing');
        if (currentSongElement) {
          currentSongElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 1000);
    }
  }, [playerState?.currentSong?.id]);

  const openYouTubeSong = useCallback((videoId?: string) => {
    if (!videoId) return;
    window.open(`https://www.youtube.com/watch?v=${videoId}`, '_blank', 'noopener,noreferrer');
  }, []);

  const openYouTubePlaylist = useCallback((playlistId?: string | null) => {
    if (!playlistId) return;
    window.open(`https://www.youtube.com/playlist?list=${playlistId}`, '_blank', 'noopener,noreferrer');
  }, []);

  const formatLastUpdated = useCallback((timestamp?: string | null): string => {
    if (!timestamp) return 'never';
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (e) {
      logError('Error formatting date in PublicPlayer', e);
      return 'invalid date';
    }
  }, []);

  const currentSong = playerState?.currentSong || null;
  // const playlistItems = playerState?.playlistItems || []; // Already defined for map
  const progressPercent = calculateProgressPercent(currentProgress, currentSong?.durationSeconds);
  // const lastUpdated = formatLastUpdated(playerState?.lastUpdatedAt); // Already defined for UI
  const isMirrorMode = playerState?.operationalMode === 'EXTERNAL_MIRROR'; // Kept for UI consistency
  // const displayedPlaylistId = isMirrorMode ? playerState?.externalMirrorPlaylistId : playerState?.youtubePlaylistId; // Already defined for UI

  const currentDanceStyle = playerState?.currentSong?.danceStyle || 'default';
  const currentStyleColors = getStyleColors(currentDanceStyle);

  const scrollToCurrentSongInList = useCallback(() => {
    const currentSongElement = document.querySelector('.song-list-item-currently-playing');
    if (currentSongElement) {
      currentSongElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, []);

  interface SongListItemProps {
    item: PlaylistItem;
    isCurrentlyPlaying: boolean;
    isAdmin: boolean;
    isAuthenticated: boolean;
    isMirrorMode: boolean;
    onRestartFromSong: (songId: string) => void;
    onOpenYouTubeSong: (videoId: string) => void;
    formatDurationFunc: (totalSeconds?: number) => string;
    timeUntilStarts?: string;
    currentSongProgress?: number;
    calculateProgressPercentFunc: (progress?: number, duration?: number) => number;
  }

  const MemoizedSongListItem = React.memo<SongListItemProps>(({
    item,
    isCurrentlyPlaying,
    isAdmin,
    // isAuthenticated, // Not used in this component directly, remove if not needed by parent's rendering logic
    isMirrorMode, // This prop is kept to maintain UI consistency as requested
    onRestartFromSong,
    onOpenYouTubeSong,
    formatDurationFunc,
    timeUntilStarts,
    currentSongProgress,
    calculateProgressPercentFunc
  }) => {
    const itemStyleColors = getStyleColors(item.danceStyle);
    // logInfo('Rendering MemoizedSongListItem', { title: item.title, isPlaying: isCurrentlyPlaying }); // Can be noisy

    const baseLiClasses = "p-3 rounded-lg transition-all duration-300 ease-in-out flex items-start space-x-4";
    let dynamicLiClasses = "";

    if (isCurrentlyPlaying) {
      const borderColorClass = itemStyleColors.progressBar
        ? itemStyleColors.progressBar.replace('bg-', 'border-')
        : 'border-pink-500';

      dynamicLiClasses = `song-list-item-currently-playing ${itemStyleColors.pulseAnimation || ''} bg-black/40 border-2 ${borderColorClass} shadow-xl`;
    } else {
      dynamicLiClasses = "bg-black/20 hover:bg-black/30 border-2 border-transparent shadow-md";
    }

    return (
      <li className={`${baseLiClasses} ${dynamicLiClasses}`}>
        <img src={item.thumbnailUrl} alt={item.title} className="h-12 w-12 rounded object-cover flex-shrink-0" loading="lazy" />
        <div className="flex-grow min-w-0 flex flex-col space-y-1">
          <div>
            <p className="text-sm font-medium text-white break-words">
              {item.title} {isCurrentlyPlaying && <span className="ml-1 text-xs text-green-300">(Currently Playing)</span>}
            </p>
            {isCurrentlyPlaying && item.durationSeconds && currentSongProgress !== undefined && (
              <div className="w-full bg-black/25 rounded-full h-1 mt-1.5 mb-0.5">
                <div
                  className={`h-1 rounded-full ${itemStyleColors.progressBar || 'bg-pink-500'}`}
                  style={{ width: `${calculateProgressPercentFunc(currentSongProgress, item.durationSeconds)}%` }}
                ></div>
              </div>
            )}
            {isCurrentlyPlaying && item.durationSeconds && currentSongProgress !== undefined && (
              <div className="mt-2">
                <Link
                  to="/signup"
                  className="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-semibold rounded-lg shadow-lg transition-all duration-200 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-opacity-75 text-xs group"
                  title="Register to suggest songs and support us!"
                >
                  <HeroMusicalNoteIcon className="h-4 w-4 mr-1.5 transform transition-transform duration-200 ease-in-out group-hover:rotate-12" />
                  <span>You Are the one changing the World! <strong>Register, Suggest Songs, Vote for Songs, Listen to Bachata, Salsa, Kizomba Radio & Support Us!</strong></span>
                </Link>
              </div>
            )}
            {timeUntilStarts && !isCurrentlyPlaying && (
              <p className="text-sm font-bold bg-black/30 px-2 py-1 rounded-md mt-1 mb-1">
                <span className="flex items-center">
                  <ClockIcon className="h-4 w-4 mr-1 flex-shrink-0 text-yellow-300" />
                  <span className="text-yellow-300">Plays in:</span> 
                  <span className="ml-1 text-white">{timeUntilStarts} <span className="text-gray-300">minutes</span></span>
                  <span className="mx-1 text-gray-300">•</span>
                  <span className="text-amber-200">{new Date(Date.now() + (parseInt(timeUntilStarts.split(':')[0]) * 60 + parseInt(timeUntilStarts.split(':')[1])) * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} o'clock</span>
                </span>
              </p>
            )}
          </div>
          <div className="flex flex-wrap items-center gap-x-3 gap-y-1 pt-1">
            <span className={`px-2 py-0.5 text-xs font-semibold rounded-full ${itemStyleColors.bg} ${itemStyleColors.text}`}>
              {item.danceStyle || 'N/A'}
            </span>
            <span className="text-xs text-gray-400">{formatDurationFunc(item.durationSeconds)}</span>
            <div className="flex items-center gap-x-2">
              {isAdmin && !isMirrorMode && ( // This condition is kept as per "Don't touch UI"
                <button
                  onClick={() => onRestartFromSong(item.id)}
                  title="Start playlist from this song"
                  className="p-1.5 rounded-md bg-sky-500 hover:bg-sky-600 text-white transition-colors focus:outline-none focus:ring-2 focus:ring-sky-400"
                  onMouseDown={(e) => e.preventDefault()}
                >
                  <PlayIcon className="h-4 w-4" />
                </button>
              )}
              <button
                onClick={() => onOpenYouTubeSong(item.youtubeVideoId)}
                title="Open song on YouTube"
                className="p-1.5 rounded-md bg-red-500 hover:bg-red-600 text-white transition-colors focus:outline-none focus:ring-2 focus:ring-red-400"
              >
                <FaYoutubeIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </li>
    );
  });


  const syncFullPlaylistState = useCallback(() => {
    if (!socketRef.current) return;
    logInfo('PublicPlayer: Emitting admin:sync_full_playlist_state');
    socketRef.current.emit('admin:sync_full_playlist_state');
  }, []);

  if (loading && !playerState) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-pink-600 via-purple-600 to-purple-700 text-white p-4">
        <div className="absolute inset-0 overflow-hidden -z-10">
          <div className="absolute left-1/2 top-0 -translate-x-1/2 transform">
            <HeroMusicalNoteIcon className="h-20 w-20 text-white/20" />
          </div>
          <div className="absolute right-1/4 bottom-0">
            <HeroMusicalNoteIcon className="h-24 w-24 text-white/10" />
          </div>
          <div className="absolute left-1/4 top-1/2">
            <HeroMusicalNoteIcon className="h-16 w-16 text-white/10" />
          </div>
        </div>
        <FaSpinnerIcon className="animate-spin h-10 w-10 text-white mb-4" />
        <p className="text-2xl font-light">Connecting to the Party Playlist...</p>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen bg-gray-100 flex flex-col">
      <audio
        ref={audioRef}
        // preload="auto" // Not preloading anything
        hidden // Always hidden
      // No src, no controls, etc.
      />

      <div className={`relative min-h-screen overflow-hidden text-white bg-gradient-to-r from-pink-600 via-purple-600 to-purple-700 py-8 px-4 sm:px-6 lg:px-8`}>
        <div className="absolute inset-0 overflow-hidden -z-10">
          {/* Musical notes background elements */}
          <div className="absolute left-1/2 top-0 -translate-x-1/2 transform">
            <HeroMusicalNoteIcon className="h-20 w-20 text-white/20" />
          </div>
          <div className="absolute right-1/4 bottom-0">
            <HeroMusicalNoteIcon className="h-24 w-24 text-white/10" />
          </div>
          <div className="absolute left-1/4 top-1/2">
            <HeroMusicalNoteIcon className="h-16 w-16 text-white/10" />
          </div>
          <div className="absolute left-1/3 top-1/4 animate-pulse">
            <HeroMusicalNoteIcon className="h-12 w-12 text-white/5" />
          </div>
          <div className="absolute right-1/3 bottom-1/4 animate-pulse delay-500">
            <HeroMusicalNoteIcon className="h-14 w-14 text-white/8" />
          </div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <Link
              to="/"
              className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-lg shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-opacity-50"
            >
              Go to Welcome Page
            </Link>
            {isAuthenticated && (
              <Link
                to={isAdmin ? "/admin/dashboard" : "/dancer/dashboard"}
                className="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white font-semibold rounded-lg shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-opacity-50"
              >
                Go to Dashboard
              </Link>
            )}
          </div>

          {loading && ( // This loading is for initial fetch
            <div className="flex flex-col items-center justify-center py-20">
              <FaSpinnerIcon className={`animate-spin h-12 w-12 mb-4 ${currentStyleColors.text || 'text-pink-400'}`} />
              <p className="text-lg text-gray-300">Loading Party Playlist...</p>
            </div>
          )}

          {error && (
            <div className="my-6 p-6 bg-red-900/30 backdrop-blur-sm ring-1 ring-red-500/50 rounded-lg text-center">
              <InformationCircleIcon className="h-10 w-10 text-red-300 mx-auto mb-2" />
              <p className="text-xl font-semibold text-red-200 mb-2">Connection Issue</p>
              <p className="text-red-300">{error}</p>
              <p className="mt-3 text-sm text-gray-400">Attempting to reconnect for updates automatically...</p>
            </div>
          )}

          {/* Buffering indicator - will not show as isBuffering is always false */}
          {isBuffering && playerState?.isPlaying && (
            <div className="fixed top-4 right-4 bg-yellow-700 text-white p-4 rounded-lg shadow-xl flex items-center space-x-3 z-20">
              <FaSpinnerIcon className="animate-spin h-5 w-5" />
              <span>Buffering...</span>
            </div>
          )}

          {!loading && !error && playerState && (
            <>
              <section aria-labelledby="now-playing-heading" className={`p-6 rounded-xl shadow-2xl mb-8 bg-gradient-to-br ${currentStyleColors.gradient || 'from-gray-700 to-gray-800'} ${playerState.isPlaying ? currentStyleColors.pulseAnimation || '' : ''} currently-playing`}>
                <div className="flex flex-col md:flex-row gap-6 items-start">
                  <div className="md:w-1/3 relative">
                    <img
                      src={playerState.currentSong?.thumbnailUrl || 'https://via.placeholder.com/300x200?text=Music+Paused'}
                      alt={playerState.currentSong?.title || 'No song playing'}
                      className="w-full rounded-lg shadow-lg aspect-video object-cover" />
                    {playerState.currentSong && playerState.isPlaying && (
                      <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
                        <PlayPauseIcon className="h-16 w-16 text-white/70 animate-pulse" />
                      </div>
                    )}
                  </div>

                  <div className="md:w-2/3 space-y-3 flex-grow" aria-live="polite" aria-atomic="true">
                    <span className={`px-3 py-1 text-sm font-semibold rounded-full ${currentStyleColors.bg} ${currentStyleColors.text}`}>
                      {playerState.currentSong?.danceStyle || 'Unknown Style'}
                    </span>
                    <h2 id="now-playing-heading" className="text-3xl font-bold text-white">
                      {playerState.currentSong?.title || 'Playlist Paused or Ended'}
                    </h2>
                    <p className="text-gray-200 text-sm">{playerState.currentSong?.channelTitle || '-'}</p>

                    {playerState.currentSong && (
                      <div className="w-full pt-2">
                        <div className="w-full bg-black/20 rounded-full h-1.5">
                          <div
                            className={`h-1.5 rounded-full ${currentStyleColors.progressBar || 'bg-pink-500'}`}
                            style={{ width: `${progressPercent}%` }} // Uses progressPercent calculated from currentProgress
                          ></div>
                        </div>
                        <div className="flex justify-between text-xs text-gray-300 mt-1">
                          <span>{formatDuration(currentProgress)}</span>
                          <span>{formatDuration(playerState.currentSong.durationSeconds)}</span>
                        </div>
                      </div>
                    )}

                    <div className="flex flex-wrap gap-3 pt-2">
                      {playerState.currentSong?.youtubeVideoId && (
                        <button
                          onClick={() => openYouTubeSong(playerState.currentSong?.youtubeVideoId)}
                          className="flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg shadow focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 transition-colors"
                        >
                          <FaYoutubeIcon className="h-5 w-5 mr-2" /> YouTube
                        </button>
                      )}
                      <button
                        onClick={() => openYouTubePlaylist(playerState.youtubePlaylistId)}
                        className="flex items-center px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white text-sm font-medium rounded-lg shadow focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-opacity-50 transition-colors"
                      >
                        <ListBulletIcon className="h-5 w-5 mr-2" /> Playlist
                      </button>
                      {playerState.currentSong && (
                        <button
                          onClick={scrollToCurrentSongInList}
                          title="Scroll to current song in list"
                          className="flex items-center px-4 py-2 bg-sky-500 hover:bg-sky-600 text-white text-sm font-medium rounded-lg shadow focus:outline-none focus:ring-2 focus:ring-sky-400 focus:ring-opacity-50 transition-colors"
                        >
                          <EyeIcon className="h-5 w-5 mr-2" /> Find in List
                        </button>
                      )}
                      <div className="mt-2"> {/* Wrapper div for spacing and to ensure it's on a new line */}
                        <Link
                          to="/signup"
                          className="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-semibold rounded-lg shadow-lg transition-all duration-200 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-opacity-75 text-xs group"
                          title="Register to suggest songs and support us!"
                        >
                          <HeroMusicalNoteIcon className="h-4 w-4 mr-1.5 transform transition-transform duration-200 ease-in-out group-hover:rotate-12" />
                          <span>You Are the one changing the World! <strong>Register, Suggest Songs, Vote for Songs, Listen to Bachata, Salsa, Kizomba Radio & Support Us!</strong></span>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 items-start">
                {isAdmin && (
                  <section aria-labelledby="admin-controls-heading" className="md:col-span-2 p-4 bg-black/20 rounded-lg shadow-lg space-y-3">
                    <h3 id="admin-controls-heading" className="text-lg font-semibold text-white">Admin Controls</h3>
                    <div className="flex flex-wrap gap-3">
                      {playerState.isPlaying ? (
                        <button
                          onClick={adminTogglePlayback} // Changed to adminTogglePlayback
                          onMouseDown={(e) => e.preventDefault()}
                          className="flex items-center px-4 py-2 rounded-md text-sm font-medium shadow transition-colors focus:outline-none focus:ring-2 focus:ring-opacity-50 bg-yellow-500 hover:bg-yellow-600 text-yellow-900 focus:ring-yellow-400"
                        >
                          <StopIcon className="h-5 w-5 mr-2" />
                          Pause Party
                        </button>
                      ) : (
                        <button
                          onClick={adminTogglePlayback} // Changed to adminTogglePlayback
                          onMouseDown={(e) => e.preventDefault()}
                          className="flex items-center px-4 py-2 rounded-md text-sm font-medium shadow transition-colors focus:outline-none focus:ring-2 focus:ring-opacity-50 bg-green-500 hover:bg-green-600 text-white focus:ring-green-400"
                        >
                          <PlayIcon className="h-5 w-5 mr-2" />
                          Start Party
                        </button>
                      )}
                      <button
                        onClick={syncFullPlaylistState}
                        onMouseDown={(e) => e.preventDefault()}
                        className="flex items-center px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white text-sm font-medium rounded-md shadow transition-colors focus:outline-none focus:ring-2 focus:ring-teal-400 focus:ring-opacity-50"
                      >
                        <ArrowPathIcon className="h-5 w-5 mr-2" /> Refresh Player
                      </button>
                    </div>
                    {playerState.currentSong && (
                      <p className="text-xs text-gray-400">Hint: Restart from a specific song in the upcoming list.</p>
                    )}
                  </section>
                )}
              </div>

              <section aria-labelledby="upcoming-songs-heading">
                <h3 id="upcoming-songs-heading" className="text-2xl font-semibold text-white mb-4">Upcoming Songs</h3>
                {playerState.playlistItems && playerState.playlistItems.filter(item => item.id !== playerState.currentSong?.id).length > 0 ? (
                  <ul className="space-y-3" aria-live="polite" aria-atomic="false">
                    {playerState.playlistItems
                      .map((item, index) => {
                        return (
                          <MemoizedSongListItem
                            key={item.id || index}
                            item={item}
                            isCurrentlyPlaying={item.id === playerState.currentSong?.id}
                            isAdmin={isAdmin}
                            isAuthenticated={isAuthenticated}
                            isMirrorMode={playerState.operationalMode === 'EXTERNAL_MIRROR'} // Kept for UI consistency
                            onRestartFromSong={restartFromSong}
                            onOpenYouTubeSong={openYouTubeSong}
                            formatDurationFunc={formatDuration}
                            timeUntilStarts={timeUntilEachSong[item.id]}
                            currentSongProgress={(item.id === playerState.currentSong?.id) ? currentProgress : undefined}
                            calculateProgressPercentFunc={calculateProgressPercent}
                          />
                        );
                      })}
                  </ul>
                ) : (
                  <p className="text-gray-400 py-5 text-center">No upcoming songs in the queue.</p>
                )}
              </section>
            </>
          )}

          {showConnectionError && !error && (
            <div className="fixed bottom-4 right-4 bg-red-700 text-white p-4 rounded-lg shadow-xl flex items-center space-x-3">
              <WifiIcon className="h-6 w-6" />
              <span>Connection to party updates lost. Attempting to reconnect...</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PublicPlayer;