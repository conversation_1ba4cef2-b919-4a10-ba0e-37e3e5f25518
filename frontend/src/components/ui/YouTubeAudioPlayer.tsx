import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useRadio } from '../../context/RadioContext';
import { logInfo, logError, logWarn } from '../../utils/logger';

// Declare YouTube player types
declare global {
  interface Window {
    YT: {
      Player: new (
        elementId: string,
        options: {
          videoId: string;
          playerVars: {
            autoplay?: number;
            controls?: number;
            disablekb?: number;
            playsinline?: number;
            rel?: number;
            modestbranding?: number;
            fs?: number;
            suggestedQuality?: string;
            origin?: string;
            enablejsapi?: number;
            mute?: number; // Added mute property for TypeScript
          };
          events: {
            onReady?: (event: any) => void;
            onStateChange?: (event: any) => void;
            onError?: (event: any) => void;
          };
        }
      ) => any;
      PlayerState: {
        ENDED: number;
        PLAYING: number;
        PAUSED: number;
        BUFFERING: number;
        CUED: number;
        UNSTARTED: number;
      };
    };
    onYouTubeIframeAPIReady: () => void;
  }

  // No type declarations needed, we'll use type assertions instead
}

const YouTubeAudioPlayer: React.FC = () => {
  const {
    currentSong,
    isPlaying,
    skipToNext,
    incrementSongEndedCounter,
    songs,
    currentChannel,
    // playbackError: contextPlaybackError, // We'll use local playbackError state primarily
    // loadingProgress, // Not directly used in this component for player logic
  } = useRadio();

  const [playbackError, setPlaybackError] = useState<string | null>(null); // Local error state
  const [isBuffering, setIsBuffering] = useState<boolean>(false);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [volume, setVolume] = useState<number>(80);
  const [actualPlayerState, setActualPlayerState] = useState<number | null>(null);
  const [isPreloadingNext, setIsPreloadingNext] = useState<boolean>(false);
  const [preloadProgress, setPreloadProgress] = useState<number>(0);

  const playerRef = useRef<any>(null);
  const nextPlayerRef = useRef<any>(null); // Reference for the next song's player
  const playerContainerRef = useRef<HTMLDivElement>(null);
  const nextPlayerContainerRef = useRef<HTMLDivElement>(null);
  const playerReadyRef = useRef<boolean>(false);
  const nextPlayerReadyRef = useRef<boolean>(false);
  const currentVideoIdRef = useRef<string | null>(null);
  const nextVideoIdRef = useRef<string | null>(null);
  const contextIsPlayingRef = useRef<boolean>(isPlaying);
  const autoplayBlockedRef = useRef<boolean>(false);
  const endedTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Step 1 (continued): Update ref when isPlaying from context changes
  useEffect(() => {
    contextIsPlayingRef.current = isPlaying;
  }, [isPlaying]);

  // Get the next song to preload
  const getNextSong = useCallback(() => {
    if (!currentSong || !songs || songs.length <= 1) return null;

    // Find current song index
    const currentIndex = songs.findIndex(song => song.id === currentSong.id);
    if (currentIndex === -1) return null;

    // Get next song index (circular)
    const nextIndex = (currentIndex + 1) % songs.length;
    return songs[nextIndex];
  }, [currentSong, songs]);

  // Load YouTube API script
  useEffect(() => {
    if (!document.getElementById('youtube-api')) {
      const tag = document.createElement('script');
      tag.id = 'youtube-api';
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag);

      logInfo('YouTubeAudioPlayer: Loading YouTube API');
    }

    // Initialize players when API is ready
    window.onYouTubeIframeAPIReady = () => {
      initializePlayer();
      initializeNextPlayer();
    };

    return () => {
      // Cleanup main player
      if (playerRef.current) {
        try {
          playerRef.current.destroy();
          playerRef.current = null;
          playerReadyRef.current = false;
          logInfo('YouTubeAudioPlayer: Main player destroyed on unmount');
        } catch (err) {
          logError('YouTubeAudioPlayer: Error destroying main player', { error: err });
        }
      }

      // Cleanup next player
      if (nextPlayerRef.current) {
        try {
          nextPlayerRef.current.destroy();
          nextPlayerRef.current = null;
          nextPlayerReadyRef.current = false;
          logInfo('YouTubeAudioPlayer: Next player destroyed on unmount');
        } catch (err) {
          logError('YouTubeAudioPlayer: Error destroying next player', { error: err });
        }
      }

      // Clear any pending timers
      if (endedTimerRef.current) {
        clearTimeout(endedTimerRef.current);
        endedTimerRef.current = null;
      }
    };
  }, []);

  // Initialize main YouTube player
  const initializePlayer = () => {
    if (!playerContainerRef.current || playerRef.current) return;

    try {
      logInfo('YouTubeAudioPlayer: Initializing main player');
      playerRef.current = new window.YT.Player('youtube-player', {
        videoId: currentSong?.youtubeVideoId || '',
        playerVars: {
          enablejsapi: 1,
          origin: window.location.origin,
          autoplay: 1, // Enable autoplay for main player
          controls: 0,
          disablekb: 1,
          playsinline: 1,
          rel: 0,
          modestbranding: 1,
          fs: 0,
          suggestedQuality: 'medium', // Lower quality for better performance
        },
        events: {
          onReady: onPlayerReady,
          onStateChange: onPlayerStateChange,
          onError: onPlayerError,
        },
      });
    } catch (err) {
      logError('YouTubeAudioPlayer: Error initializing main player', { error: err });
      setPlaybackError('Failed to initialize player. Please refresh the page.');
    }
  };

  // Initialize next song YouTube player
  const initializeNextPlayer = () => {
    if (!nextPlayerContainerRef.current || nextPlayerRef.current) return;

    try {
      logInfo('YouTubeAudioPlayer: Initializing next player');
      nextPlayerRef.current = new window.YT.Player('youtube-next-player', {
        videoId: '', // Start with empty video
        playerVars: {
          enablejsapi: 1,
          origin: window.location.origin,
          autoplay: 0,
          controls: 0,
          disablekb: 1,
          playsinline: 1,
          rel: 0,
          modestbranding: 1,
          fs: 0,
          suggestedQuality: 'medium', // Lower quality for better performance
          mute: 1, // Mute the next player
        },
        events: {
          onReady: onNextPlayerReady,
          onStateChange: onNextPlayerStateChange,
          onError: onNextPlayerError,
        },
      });
    } catch (err) {
      logError('YouTubeAudioPlayer: Error initializing next player', { error: err });
      // Don't show error for next player, it's not critical
    }
  };

  // Handle main player ready event
  const onPlayerReady = (event: any) => {
    playerReadyRef.current = true;
    logInfo('YouTubeAudioPlayer: Main player ready');

    // Set initial volume
    try {
      event.target.setVolume(volume);
      if (isMuted) {
        event.target.mute();
      }
    } catch (err) {
      logError('YouTubeAudioPlayer: Error setting initial volume', { error: err });
    }

    // If there's a current song when player becomes ready, load it.
    // The onStateChange CUED handler will play it if isPlaying is true from context.
    if (currentSong?.youtubeVideoId) {
      logInfo('YouTubeAudioPlayer: onPlayerReady - currentSong exists. Calling loadVideo.', { videoId: currentSong.youtubeVideoId, isPlayingContext: isPlaying });
      loadVideo(currentSong.youtubeVideoId);
    }

    // Preload next song if available
    preloadNextSong();
  };

  // Handle next player ready event
  const onNextPlayerReady = (event: any) => {
    nextPlayerReadyRef.current = true;
    logInfo('YouTubeAudioPlayer: Next player ready');

    // Always mute the next player
    try {
      event.target.mute();
      event.target.setVolume(0);
    } catch (err) {
      logError('YouTubeAudioPlayer: Error muting next player', { error: err });
    }

    // Preload next song if available
    preloadNextSong();
  };

  // Handle main player state change events
  const onPlayerStateChange = (event: any) => {
    const PlayerState = window.YT.PlayerState;
    setActualPlayerState(event.data); // Store the actual player state
    logInfo(`YouTubeAudioPlayer: onPlayerStateChange - New state: ${event.data} (${getPlayerStateName(event.data)}), Current Song: ${currentSong?.youtubeVideoId}, Context isPlaying: ${contextIsPlayingRef.current}`);

    switch (event.data) {
      case PlayerState.ENDED:
        logInfo('YouTubeAudioPlayer: Video ENDED event received.');

        // Clear any existing timer
        if (endedTimerRef.current) {
          clearTimeout(endedTimerRef.current);
        }

        // Use a timer to ensure we don't miss the end event
        endedTimerRef.current = setTimeout(() => {
          // If we have a preloaded next song ready, swap players
          if (nextPlayerReadyRef.current && nextVideoIdRef.current) {
            logInfo('YouTubeAudioPlayer: Swapping to preloaded next player');
            swapPlayers();
          } else {
            // Otherwise use the normal song ended counter
            logInfo('YouTubeAudioPlayer: No preloaded player ready, calling incrementSongEndedCounter');
            incrementSongEndedCounter();
          }
        }, 300); // Shorter delay for faster transition

        // Update media session if available
        if ('mediaSession' in navigator) {
          try {
            (navigator as any).mediaSession.playbackState = 'none';
          } catch (err) {
            // Ignore errors with media session
          }
        }

        break;
      case PlayerState.PLAYING:
        setIsBuffering(false);
        // Reset autoplay blocked flag when playback starts successfully
        autoplayBlockedRef.current = false;
        logInfo('YouTubeAudioPlayer: Playback started (State: PLAYING)');

        // Start preloading next song when current song starts playing
        if (!isPreloadingNext && !nextVideoIdRef.current) {
          preloadNextSong();
        }

        // Update media session if available
        if ('mediaSession' in navigator) {
          try {
            (navigator as any).mediaSession.playbackState = 'playing';
          } catch (err) {
            // Ignore errors with media session
          }
        }

        // Set up a timer to check if we're near the end of the song
        // This helps with background playback and ensures we don't miss the ENDED event
        try {
          const duration = playerRef.current?.getDuration() || 0;
          if (duration > 0) {
            const checkTimeRemaining = () => {
              if (playerRef.current && playerReadyRef.current) {
                try {
                  const currentTime = playerRef.current.getCurrentTime() || 0;
                  const timeRemaining = duration - currentTime;

                  // If less than 3 seconds remaining, prepare for transition
                  if (timeRemaining < 3 && timeRemaining > 0) {
                    logInfo(`YouTubeAudioPlayer: Near end of song (${timeRemaining.toFixed(1)}s remaining), preparing for transition`);
                    // If next song is preloaded, prepare to swap
                    if (nextPlayerReadyRef.current && nextVideoIdRef.current) {
                      logInfo('YouTubeAudioPlayer: Next song preloaded, will swap soon');
                    }
                  }

                  // If we're at the end but didn't get ENDED event, force transition
                  if (timeRemaining <= 0.1) {
                    logInfo('YouTubeAudioPlayer: Reached end of song but no ENDED event, forcing transition');
                    if (nextPlayerReadyRef.current && nextVideoIdRef.current) {
                      swapPlayers();
                    } else {
                      incrementSongEndedCounter();
                    }
                    return; // Don't schedule another check
                  }

                  // Schedule next check based on time remaining
                  const nextCheckTime = timeRemaining < 10 ? 1000 : 5000;
                  setTimeout(checkTimeRemaining, nextCheckTime);
                } catch (err) {
                  logError('YouTubeAudioPlayer: Error in time remaining check', { error: err });
                }
              }
            };

            // Start checking time remaining
            setTimeout(checkTimeRemaining, 5000);
          }
        } catch (err) {
          logError('YouTubeAudioPlayer: Error setting up time remaining check', { error: err });
        }

        break;
      case PlayerState.BUFFERING:
        setIsBuffering(true);
        logInfo('YouTubeAudioPlayer: Buffering (State: BUFFERING)');
        break;
      case PlayerState.PAUSED:
        setIsBuffering(false);
        logInfo('YouTubeAudioPlayer: Paused (State: PAUSED)');

        // Update media session if available
        if ('mediaSession' in navigator) {
          try {
            (navigator as any).mediaSession.playbackState = 'paused';
          } catch (err) {
            // Ignore errors with media session
          }
        }

        // If we should be playing but we're paused, try to resume
        // This helps with background playback issues
        if (contextIsPlayingRef.current && document.hidden) {
          try {
            logInfo('YouTubeAudioPlayer: Detected unexpected pause in background, attempting to resume');
            setTimeout(() => {
              if (playerRef.current && playerReadyRef.current && contextIsPlayingRef.current) {
                playerRef.current.playVideo();
              }
            }, 1000);
          } catch (err) {
            logError('YouTubeAudioPlayer: Error resuming from unexpected pause', { error: err });
          }
        }
        break;
      case PlayerState.CUED:
        logInfo(`YouTubeAudioPlayer: Video cued (State: CUED). contextIsPlayingRef.current = ${contextIsPlayingRef.current}`);
        if (contextIsPlayingRef.current) {
          try {
            const currentPlayerStateBeforePlay = playerRef.current?.getPlayerState();
            logInfo(`YouTubeAudioPlayer: CUED & contextIsPlayingRef.current=true, attempting player.playVideo(). Current player state before call: ${currentPlayerStateBeforePlay} (${getPlayerStateName(currentPlayerStateBeforePlay)})`);
            playerRef.current?.playVideo();
            logInfo('YouTubeAudioPlayer: player.playVideo() called in CUED state. Waiting for subsequent state change.');
          } catch (err) {
            logError('YouTubeAudioPlayer: Error calling playVideo in CUED state', { error: err });
            // If playback fails, mark autoplay as blocked
            autoplayBlockedRef.current = true;

            // Try again after a short delay
            setTimeout(() => {
              if (playerRef.current && playerReadyRef.current && contextIsPlayingRef.current) {
                try {
                  playerRef.current.playVideo();
                  logInfo('YouTubeAudioPlayer: Retrying playVideo after delay');
                } catch (innerErr) {
                  logError('YouTubeAudioPlayer: Error in delayed playVideo retry', { error: innerErr });
                }
              }
            }, 1000);
          }
        } else {
          logInfo('YouTubeAudioPlayer: CUED but contextIsPlayingRef.current=false. Not calling playVideo.');
        }
        break;
      case PlayerState.UNSTARTED:
        // If we're in UNSTARTED state but should be playing, try to play
        if (contextIsPlayingRef.current) {
          try {
            playerRef.current?.playVideo();
            logInfo('YouTubeAudioPlayer: Attempting to play from UNSTARTED state');
          } catch (err) {
            logError('YouTubeAudioPlayer: Error playing from UNSTARTED state', { error: err });

            // Try again after a short delay
            setTimeout(() => {
              if (playerRef.current && playerReadyRef.current && contextIsPlayingRef.current) {
                try {
                  playerRef.current.playVideo();
                  logInfo('YouTubeAudioPlayer: Retrying playVideo from UNSTARTED after delay');
                } catch (innerErr) {
                  logError('YouTubeAudioPlayer: Error in delayed UNSTARTED playVideo retry', { error: innerErr });
                }
              }
            }, 1000);
          }
        }
        break;
      default:
        break;
    }
  };

  // Handle next player state change events
  const onNextPlayerStateChange = (event: any) => {
    const PlayerState = window.YT.PlayerState;
    logInfo(`YouTubeAudioPlayer: onNextPlayerStateChange - New state: ${event.data} (${getPlayerStateName(event.data)}), Next Song: ${nextVideoIdRef.current}`);

    switch (event.data) {
      case PlayerState.CUED:
        logInfo('YouTubeAudioPlayer: Next video cued successfully');
        setIsPreloadingNext(false);
        setPreloadProgress(100);
        break;
      case PlayerState.BUFFERING:
        logInfo('YouTubeAudioPlayer: Next video buffering');
        setPreloadProgress(50); // Approximate progress
        break;
      case PlayerState.PLAYING:
        // Immediately pause the next player - we just want to buffer it
        try {
          nextPlayerRef.current?.pauseVideo();
          logInfo('YouTubeAudioPlayer: Next player started playing, pausing it');
          setPreloadProgress(100);
          setIsPreloadingNext(false);
        } catch (err) {
          logError('YouTubeAudioPlayer: Error pausing next player', { error: err });
        }
        break;
      default:
        break;
    }
  };

  // Handle main player errors
  const onPlayerError = (event: any) => {
    const errorCode = event.data;
    let errorMessage = 'An unknown error occurred';

    // Map error codes to user-friendly messages
    switch (errorCode) {
      case 2:
        errorMessage = 'Invalid video ID';
        break;
      case 5:
        errorMessage = 'Video not supported in HTML5 player';
        break;
      case 100:
        errorMessage = 'Video not found or removed';
        break;
      case 101:
      case 150:
        errorMessage = 'Video cannot be played due to embedding restrictions';
        break;
    }

    logError('YouTubeAudioPlayer: Main player error', {
      errorCode,
      errorMessage,
      videoId: currentSong?.youtubeVideoId
    });

    setPlaybackError(errorMessage);

    // Skip to next song after error
    setTimeout(() => {
      setPlaybackError(null);
      skipToNext();
    }, 1000);
  };

  // Handle next player errors
  const onNextPlayerError = (event: any) => {
    const errorCode = event.data;

    logError('YouTubeAudioPlayer: Next player error', {
      errorCode,
      videoId: nextVideoIdRef.current
    });

    // Reset preloading state
    setIsPreloadingNext(false);
    setPreloadProgress(0);
    nextVideoIdRef.current = null;

    // Don't show error for next player, it's not critical
  };

  // Preload the next song
  const preloadNextSong = useCallback(() => {
    if (!nextPlayerReadyRef.current || isPreloadingNext) return;

    const nextSong = getNextSong();
    if (!nextSong || !nextSong.youtubeVideoId) {
      logInfo('YouTubeAudioPlayer: No next song available to preload');
      return;
    }

    // Don't preload if it's the same as current or already preloaded
    if (nextSong.youtubeVideoId === currentVideoIdRef.current ||
        nextSong.youtubeVideoId === nextVideoIdRef.current) {
      return;
    }

    logInfo('YouTubeAudioPlayer: Preloading next song', { videoId: nextSong.youtubeVideoId });
    setIsPreloadingNext(true);
    setPreloadProgress(10);

    try {
      // Load the video but immediately pause it (it will buffer in the background)
      nextPlayerRef.current.loadVideoById(nextSong.youtubeVideoId);
      nextVideoIdRef.current = nextSong.youtubeVideoId;

      // Ensure it's muted
      nextPlayerRef.current.mute();
      nextPlayerRef.current.setVolume(0);

      // Pause after a short delay to allow buffering to start
      setTimeout(() => {
        if (nextPlayerRef.current) {
          nextPlayerRef.current.pauseVideo();
        }
      }, 500);

    } catch (err) {
      logError('YouTubeAudioPlayer: Error preloading next song', { error: err });
      setIsPreloadingNext(false);
      setPreloadProgress(0);
    }
  }, [getNextSong, isPreloadingNext]);

  // Swap main player with next player
  const swapPlayers = useCallback(() => {
    if (!nextPlayerReadyRef.current || !nextVideoIdRef.current) {
      logInfo('YouTubeAudioPlayer: Cannot swap players, next player not ready');
      incrementSongEndedCounter(); // Fall back to normal song change
      return;
    }

    logInfo('YouTubeAudioPlayer: Swapping players', {
      currentVideoId: currentVideoIdRef.current,
      nextVideoId: nextVideoIdRef.current
    });

    try {
      // Swap the player references
      const tempPlayer = playerRef.current;
      playerRef.current = nextPlayerRef.current;
      nextPlayerRef.current = tempPlayer;

      // Update video ID references
      currentVideoIdRef.current = nextVideoIdRef.current;
      nextVideoIdRef.current = null;

      // Reset next player ready state
      nextPlayerReadyRef.current = false;

      // Apply volume settings to new main player
      playerRef.current.setVolume(volume);
      if (isMuted) {
        playerRef.current.mute();
      } else {
        playerRef.current.unMute();
      }

      // Start playing the new main player
      if (contextIsPlayingRef.current) {
        playerRef.current.playVideo();
      }

      // Signal that the song has changed
      incrementSongEndedCounter();

      // Initialize the new next player
      setTimeout(() => {
        initializeNextPlayer();
      }, 1000);

    } catch (err) {
      logError('YouTubeAudioPlayer: Error swapping players', { error: err });
      incrementSongEndedCounter(); // Fall back to normal song change
    }
  }, [incrementSongEndedCounter, volume, isMuted]);

  // Load and play a video
  const loadVideo = (videoId: string) => {
    if (!playerRef.current || !playerReadyRef.current) {
      logWarn('YouTubeAudioPlayer: Player not ready, cannot load video', { videoId });
      return;
    }

    try {
      // Only load if the video ID has changed
      if (currentVideoIdRef.current !== videoId) {
        logInfo('YouTubeAudioPlayer: Loading new video', { videoId, currentIsPlayingState: isPlaying });
        playerRef.current.loadVideoById(videoId); // This will trigger onStateChange, eventually to CUED
        currentVideoIdRef.current = videoId;

        // Reset autoplay blocked flag when loading a new video
        autoplayBlockedRef.current = false;

        // Start preloading the next song
        setTimeout(() => {
          preloadNextSong();
        }, 1000);

      } else if (isPlaying && actualPlayerState !== window.YT.PlayerState.PLAYING) {
        // If same videoId, but we want it to play and it's not.
        logInfo('YouTubeAudioPlayer: Same video, ensuring playback', { videoId });
        playerRef.current.playVideo();
      }
    } catch (err) {
      logError('YouTubeAudioPlayer: Error loading video', { error: err, videoId });
      setPlaybackError('Failed to load video. Skipping to next song.');

      setTimeout(() => {
        setPlaybackError(null);
        skipToNext();
      }, 1000);
    }
  };

  // Setup background audio mode
  useEffect(() => {
    // Handle visibility change to keep audio playing when tab is not visible
    const handleVisibilityChange = () => {
      if (document.hidden) {
        logInfo('YouTubeAudioPlayer: Page hidden, ensuring playback continues');
        // If we're supposed to be playing but autoplay might be blocked, try to play
        if (contextIsPlayingRef.current && playerRef.current && playerReadyRef.current) {
          try {
            const currentState = playerRef.current.getPlayerState();
            if (currentState !== window.YT.PlayerState.PLAYING) {
              playerRef.current.playVideo();
              logInfo('YouTubeAudioPlayer: Attempted to resume playback in background');
            }

            // Also check if we're near the end of the song to preemptively load the next one
            const currentTime = playerRef.current.getCurrentTime() || 0;
            const duration = playerRef.current.getDuration() || 0;
            if (duration > 0 && (duration - currentTime) < 10) { // If less than 10 seconds left
              logInfo('YouTubeAudioPlayer: Near end of song in background, preparing for transition');
              // Set up a timer to check again and force next song if needed
              setTimeout(() => {
                if (playerRef.current && playerReadyRef.current) {
                  const newCurrentTime = playerRef.current.getCurrentTime() || 0;
                  const newDuration = playerRef.current.getDuration() || 0;
                  // If we're still near the end after the timeout, force next song
                  if (newDuration > 0 && (newDuration - newCurrentTime) < 3) {
                    logInfo('YouTubeAudioPlayer: Forcing next song in background');
                    incrementSongEndedCounter();
                  }
                }
              }, 8000); // Check again after 8 seconds
            }
          } catch (err) {
            logError('YouTubeAudioPlayer: Error ensuring background playback', { error: err });
          }
        }
      } else {
        // Page became visible again
        logInfo('YouTubeAudioPlayer: Page visible again, checking playback state');
        if (contextIsPlayingRef.current && playerRef.current && playerReadyRef.current) {
          try {
            const currentState = playerRef.current.getPlayerState();
            if (currentState === window.YT.PlayerState.PAUSED ||
                currentState === window.YT.PlayerState.UNSTARTED) {
              logInfo('YouTubeAudioPlayer: Resuming playback after page became visible');
              playerRef.current.playVideo();
            } else if (currentState === window.YT.PlayerState.ENDED) {
              logInfo('YouTubeAudioPlayer: Song ended while page was hidden, moving to next song');
              incrementSongEndedCounter();
            }
          } catch (err) {
            logError('YouTubeAudioPlayer: Error checking playback after visibility change', { error: err });
          }
        }
      }
    };

    // Listen for page visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Setup periodic background check to ensure playback continues
    const backgroundCheckInterval = setInterval(() => {
      if (document.hidden && contextIsPlayingRef.current && playerRef.current && playerReadyRef.current) {
        try {
          const currentState = playerRef.current.getPlayerState();
          if (currentState === window.YT.PlayerState.ENDED) {
            logInfo('YouTubeAudioPlayer: Background check detected ended song, moving to next');
            incrementSongEndedCounter();
          } else if (currentState !== window.YT.PlayerState.PLAYING &&
                     currentState !== window.YT.PlayerState.BUFFERING) {
            logInfo('YouTubeAudioPlayer: Background check detected stopped playback, resuming');
            playerRef.current.playVideo();
          }
        } catch (err) {
          logError('YouTubeAudioPlayer: Error in background check interval', { error: err });
        }
      }
    }, 5000); // Check every 5 seconds

    // Setup audio context for mobile devices to keep audio playing
    let audioContext: AudioContext | null = null;
    try {
      // @ts-ignore - AudioContext might not be available in all browsers
      const AudioContextClass = window.AudioContext || window.webkitAudioContext;
      if (AudioContextClass) {
        audioContext = new AudioContextClass();
        // Create a silent oscillator to keep audio context active
        const oscillator = audioContext.createOscillator();
        oscillator.frequency.value = 0; // Silent
        oscillator.connect(audioContext.destination);
        oscillator.start();
        logInfo('YouTubeAudioPlayer: Created silent audio context for background playback');
      }
    } catch (err) {
      logError('YouTubeAudioPlayer: Error creating audio context', { error: err });
    }

    // Create a media session for better background playback control
    if ('mediaSession' in navigator) {
      try {
        // @ts-ignore - MediaMetadata might not be available in all TypeScript versions
        (navigator as any).mediaSession.metadata = new (window as any).MediaMetadata({
          title: currentSong?.title || 'Radio Player',
          artist: currentSong?.danceStyle || 'Dance Music',
          album: 'Social Dance Radio',
          artwork: [
            { src: currentSong?.thumbnailUrl || '', sizes: '96x96', type: 'image/jpeg' },
            { src: currentSong?.thumbnailUrl || '', sizes: '128x128', type: 'image/jpeg' },
            { src: currentSong?.thumbnailUrl || '', sizes: '192x192', type: 'image/jpeg' },
            { src: currentSong?.thumbnailUrl || '', sizes: '256x256', type: 'image/jpeg' },
            { src: currentSong?.thumbnailUrl || '', sizes: '384x384', type: 'image/jpeg' },
            { src: currentSong?.thumbnailUrl || '', sizes: '512x512', type: 'image/jpeg' },
          ]
        });

        // Set action handlers
        (navigator as any).mediaSession.setActionHandler('play', () => {
          if (playerRef.current && playerReadyRef.current) {
            playerRef.current.playVideo();
          }
        });

        (navigator as any).mediaSession.setActionHandler('pause', () => {
          if (playerRef.current && playerReadyRef.current) {
            playerRef.current.pauseVideo();
          }
        });

        (navigator as any).mediaSession.setActionHandler('nexttrack', () => {
          skipToNext();
        });

        logInfo('YouTubeAudioPlayer: Set up media session for background control');
      } catch (err) {
        logError('YouTubeAudioPlayer: Error setting up media session', { error: err });
      }
    }

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(backgroundCheckInterval);

      if (audioContext) {
        try {
          audioContext.close();
        } catch (err) {
          logError('YouTubeAudioPlayer: Error closing audio context', { error: err });
        }
      }
    };
  }, [currentSong, incrementSongEndedCounter, skipToNext]);

  // Handle play/pause
  useEffect(() => {
    if (!playerRef.current || !playerReadyRef.current || !currentSong) {
      return;
    }

    try {
      const currentPlayerState = playerRef.current.getPlayerState(); // Get current state from player

      if (isPlaying) {
        // If context wants to play, and player is currently PAUSED, tell it to play.
        if (currentPlayerState === window.YT.PlayerState.PAUSED) {
          logInfo('YouTubeAudioPlayer: isPlaying context true, player PAUSED. Attempting playVideo().', { videoId: currentSong.youtubeVideoId });
          playerRef.current.playVideo();
        } else if (currentPlayerState === window.YT.PlayerState.UNSTARTED && currentSong.youtubeVideoId) {
          // If player is UNSTARTED (e.g. after initial load and no autoplay) and we have a song, play it.
          // This also covers if loadVideoById was called but playVideo was not (e.g. if isPlaying was false during loadVideo)
          logInfo('YouTubeAudioPlayer: isPlaying context true, player UNSTARTED. Attempting playVideo() for current song.', { videoId: currentSong.youtubeVideoId });
          playerRef.current.playVideo(); // This might be redundant if CUED handler works, but safe.
        } else if (currentPlayerState === window.YT.PlayerState.ENDED) {
          // If the song has ended but isPlaying is still true, we need to move to the next song
          logInfo('YouTubeAudioPlayer: isPlaying context true, but player ENDED. Triggering next song.');
          incrementSongEndedCounter();
        } else if (autoplayBlockedRef.current) {
          // If autoplay was previously blocked, try again
          logInfo('YouTubeAudioPlayer: Autoplay was blocked, retrying playback');
          playerRef.current.playVideo();
          autoplayBlockedRef.current = false; // Reset the flag
        }
        // If already PLAYING or BUFFERING, do nothing, state is fine.
      } else { // isPlaying is false (context wants to pause)
        // If context wants to pause, and player is PLAYING, tell it to pause.
        if (currentPlayerState === window.YT.PlayerState.PLAYING) {
          logInfo('YouTubeAudioPlayer: isPlaying context false, player PLAYING. Attempting pauseVideo().', { videoId: currentSong.youtubeVideoId });
          playerRef.current.pauseVideo();
        }
        // If already PAUSED, CUED, ENDED, do nothing, state is fine.
      }
    } catch (err) {
      logError('YouTubeAudioPlayer: Error in isPlaying useEffect (play/pause)', { error: err, currentSongId: currentSong?.youtubeVideoId });
    }
  }, [isPlaying, currentSong?.youtubeVideoId, incrementSongEndedCounter]); // Added incrementSongEndedCounter to dependencies

  // Handle song changes
  useEffect(() => {
    if (!playerRef.current || !playerReadyRef.current) {
      return;
    }

    if (currentSong && currentSong.youtubeVideoId) {
      logInfo('YouTubeAudioPlayer: currentSong context changed', {
        videoId: currentSong.youtubeVideoId,
        title: currentSong.title,
        currentIsPlayingState: isPlaying
      });

      // Check if this is the same as the next preloaded song
      if (nextPlayerReadyRef.current && nextVideoIdRef.current === currentSong.youtubeVideoId) {
        logInfo('YouTubeAudioPlayer: Current song matches preloaded next song, swapping players');
        swapPlayers();
      } else {
        // Load the video normally. Playback will be handled by CUED state + isPlayingRef.
        loadVideo(currentSong.youtubeVideoId);
        logInfo('YouTubeAudioPlayer: Loading new video via loadVideo', { videoId: currentSong.youtubeVideoId });
      }

      // Start preloading the next song after a delay
      setTimeout(() => {
        if (!isPreloadingNext && !nextVideoIdRef.current) {
          preloadNextSong();
        }
      }, 2000);

    } else if (!currentSong) {
      logInfo('YouTubeAudioPlayer: currentSong is null, attempting to clear player.');
      // If currentSong is null, clear the player
      playerRef.current.loadVideoById('');
      playerRef.current.stopVideo();

      // Also clear the next player
      if (nextPlayerRef.current && nextPlayerReadyRef.current) {
        nextPlayerRef.current.loadVideoById('');
        nextPlayerRef.current.stopVideo();
        nextVideoIdRef.current = null;
      }
    }
  }, [currentSong?.youtubeVideoId, isPreloadingNext, swapPlayers, preloadNextSong]);

  // Handle volume changes
  const handleVolumeChange = (newVolume: number) => {
    if (!playerRef.current || !playerReadyRef.current) return;

    setVolume(newVolume);
    try {
      playerRef.current.setVolume(newVolume);

      // Unmute if previously muted and volume > 0
      if (isMuted && newVolume > 0) {
        playerRef.current.unMute();
        setIsMuted(false);
      }

      logInfo('YouTubeAudioPlayer: Volume changed', { volume: newVolume });
    } catch (err) {
      logError('YouTubeAudioPlayer: Error changing volume', { error: err });
    }
  };

  // Toggle mute
  const toggleMute = () => {
    if (!playerRef.current || !playerReadyRef.current) return;

    try {
      if (isMuted) {
        playerRef.current.unMute();
        logInfo('YouTubeAudioPlayer: Unmuted');
      } else {
        playerRef.current.mute();
        logInfo('YouTubeAudioPlayer: Muted');
      }

      setIsMuted(!isMuted);
    } catch (err) {
      logError('YouTubeAudioPlayer: Error toggling mute', { error: err });
    }
  };

  // Helper function to get player state name for logging
  const getPlayerStateName = (stateCode: number | undefined | null) => {
    if (stateCode === null || typeof stateCode === 'undefined') return 'UNKNOWN/UNSET';
    const PlayerState = window.YT?.PlayerState;
    if (!PlayerState) return 'YT.PlayerState not found';
    switch (stateCode) {
      case PlayerState.UNSTARTED: return 'UNSTARTED';
      case PlayerState.ENDED: return 'ENDED';
      case PlayerState.PLAYING: return 'PLAYING';
      case PlayerState.PAUSED: return 'PAUSED';
      case PlayerState.BUFFERING: return 'BUFFERING';
      case PlayerState.CUED: return 'CUED';
      default: return `UNKNOWN_STATE_CODE (${stateCode})`;
    }
  };

  // Effect to handle changes in isPlaying state from context
  useEffect(() => {
    const playerStateForLog = (playerReadyRef.current && playerRef.current && typeof playerRef.current.getPlayerState === 'function')
      ? playerRef.current.getPlayerState()
      : 'NOT_READY_OR_NO_METHOD';
    logInfo(`YouTubeAudioPlayer: isPlaying Effect triggered. Context isPlaying: ${isPlaying}, Player Ready: ${playerReadyRef.current}, Current Song: ${currentSong?.youtubeVideoId}, Player State: ${playerStateForLog} (${getPlayerStateName(playerStateForLog === 'NOT_READY_OR_NO_METHOD' ? undefined : playerStateForLog)})`);

    if (playerReadyRef.current && currentSong?.youtubeVideoId) {
      if (isPlaying) {
        const currentState = playerRef.current?.getPlayerState();
        if (currentState !== window.YT.PlayerState.PLAYING && currentState !== window.YT.PlayerState.BUFFERING) {
          logInfo(`YouTubeAudioPlayer: isPlaying Effect: context isPlaying=true, player not PLAYING/BUFFERING (State: ${currentState} (${getPlayerStateName(currentState)}). Attempting playVideo.`);
          try {
            playerRef.current?.playVideo();
            logInfo('YouTubeAudioPlayer: isPlaying Effect: player.playVideo() called.');

            // Update media session playback state if available
            if ('mediaSession' in navigator) {
              (navigator as any).mediaSession.playbackState = 'playing';
            }
          } catch (err) {
            logError('YouTubeAudioPlayer: isPlaying Effect: Error calling playVideo', { error: err });
          }
        } else {
          logInfo(`YouTubeAudioPlayer: isPlaying Effect: context isPlaying=true, but player already PLAYING/BUFFERING (State: ${currentState} (${getPlayerStateName(currentState)}). No action.`);
        }
      } else { // isPlaying is false
        const currentState = playerRef.current?.getPlayerState();
        // Only pause if actively playing. Might not want to pause if it's buffering or cued.
        if (currentState === window.YT.PlayerState.PLAYING || currentState === window.YT.PlayerState.BUFFERING) {
          logInfo(`YouTubeAudioPlayer: isPlaying Effect: context isPlaying=false, player PLAYING/BUFFERING (State: ${currentState} (${getPlayerStateName(currentState)}). Attempting pauseVideo.`);
          try {
            playerRef.current?.pauseVideo();
            logInfo('YouTubeAudioPlayer: isPlaying Effect: player.pauseVideo() called.');

            // Update media session playback state if available
            if ('mediaSession' in navigator) {
              (navigator as any).mediaSession.playbackState = 'paused';
            }
          } catch (err) {
            logError('YouTubeAudioPlayer: isPlaying Effect: Error calling pauseVideo', { error: err });
          }
        } else {
          logInfo(`YouTubeAudioPlayer: isPlaying Effect: context isPlaying=false, but player not PLAYING/BUFFERING (State: ${currentState} (${getPlayerStateName(currentState)}). No action.`);
        }
      }
    }
  }, [isPlaying, currentSong, playerReadyRef.current]); // Dependencies updated

  // Update media session metadata when song changes
  useEffect(() => {
    if (currentSong && 'mediaSession' in navigator) {
      try {
        // @ts-ignore - MediaMetadata might not be available in all TypeScript versions
        (navigator as any).mediaSession.metadata = new (window as any).MediaMetadata({
          title: currentSong.title || 'Radio Player',
          artist: currentSong.danceStyle || 'Dance Music',
          album: 'Social Dance Radio',
          artwork: [
            { src: currentSong.thumbnailUrl || '', sizes: '96x96', type: 'image/jpeg' },
            { src: currentSong.thumbnailUrl || '', sizes: '128x128', type: 'image/jpeg' },
            { src: currentSong.thumbnailUrl || '', sizes: '192x192', type: 'image/jpeg' },
            { src: currentSong.thumbnailUrl || '', sizes: '256x256', type: 'image/jpeg' },
            { src: currentSong.thumbnailUrl || '', sizes: '384x384', type: 'image/jpeg' },
            { src: currentSong.thumbnailUrl || '', sizes: '512x512', type: 'image/jpeg' },
          ]
        });

        // Update playback state
        (navigator as any).mediaSession.playbackState = isPlaying ? 'playing' : 'paused';

        logInfo('YouTubeAudioPlayer: Updated media session metadata for new song');
      } catch (err) {
        logError('YouTubeAudioPlayer: Error updating media session metadata', { error: err });
      }
    }
  }, [currentSong, isPlaying]);

  return (
    <div className="youtube-audio-player w-full">
      {/* Hidden main player container */}
      <div
        ref={playerContainerRef}
        style={{
          position: 'absolute',
          left: '-9999px',
          top: '-9999px',
          width: '1px',
          height: '1px',
          overflow: 'hidden',
        }}
        aria-hidden="true"
      >
        <div id="youtube-player"></div>
      </div>

      {/* Hidden next player container */}
      <div
        ref={nextPlayerContainerRef}
        style={{
          position: 'absolute',
          left: '-9999px',
          top: '-9999px',
          width: '1px',
          height: '1px',
          overflow: 'hidden',
        }}
        aria-hidden="true"
      >
        <div id="youtube-next-player"></div>
      </div>

      {/* Playback status indicators */}
      {isBuffering && (
        <div className="buffering-indicator flex items-center justify-center space-x-2 my-2">
          <div className="w-4 h-4 rounded-full bg-pink-600 animate-pulse"></div>
          <p className="text-sm">Buffering...</p>
        </div>
      )}

      {isPreloadingNext && (
        <div className="preloading-indicator flex items-center justify-center space-x-2 my-2">
          <div className="w-3 h-3 rounded-full bg-blue-500 animate-pulse"></div>
          <p className="text-xs text-blue-300">Preloading next song...</p>
        </div>
      )}

      {playbackError && (
        <div className="error-message text-red-400 text-sm mt-2 p-2 bg-black/20 rounded">
          {playbackError}
        </div>
      )}

      {/* Volume controls */}
      <div className="volume-controls flex items-center mt-5 p-3 bg-black/20 backdrop-blur-sm rounded-lg">
        <button
          onClick={toggleMute}
          className="volume-toggle mr-3 p-2 rounded-full hover:bg-black/30 transition-colors"
          aria-label={isMuted ? "Unmute" : "Mute"}
        >
          {isMuted ? (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" width="20" height="20">
              <path d="M11 5L6 9H2v6h4l5 4zM23 9l-6 6M17 9l6 6"/>
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" width="20" height="20">
              <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
              <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
              <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
            </svg>
          )}
        </button>

        <div className="w-full">
          <input
            type="range"
            min="0"
            max="100"
            value={volume}
            onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
            className="volume-slider w-full h-2 rounded-full appearance-none bg-black/30 outline-none [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-pink-600"
            aria-label="Volume"
          />
          <div className="text-xs text-center mt-1 opacity-80">Volume: {volume}%</div>
        </div>
      </div>

      {/* Background playback indicator */}
      <div className="text-xs text-center mt-3 text-gray-400">
        <span role="img" aria-label="Music note">🎵</span> Radio will continue playing in the background
      </div>
    </div>
  );
};

export default YouTubeAudioPlayer;