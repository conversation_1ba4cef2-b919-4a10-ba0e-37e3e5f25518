import React, { useState, useRef, useEffect } from 'react';
import ReactDOM from 'react-dom';
import { Tooltip } from './Tooltip';
import { UserGroupIcon, XMarkIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from "../../utils/cn"; // For classname merging

interface Voter {
  id: string;
  username: string;
  profile?: {
    displayName?: string;
    avatarUrl?: string;
  };
}

interface VotersListProps {
  voters: Voter[];
  totalVotes: number;
  className?: string;
  showButton?: boolean;
  buttonClassName?: string;
  compact?: boolean;
  hideCount?: boolean;
}

export const VotersList: React.FC<VotersListProps> = ({
  voters,
  totalVotes,
  className = '',
  showButton = true,
  buttonClassName = '',
  compact = false,
  hideCount = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const popupRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Close popup if click/touch is outside
  useEffect(() => {
    if (!isExpanded) return;
    function handleClickOutside(event: MouseEvent | TouchEvent) {
      if (popupRef.current && !popupRef.current.contains(event.target as Node) && 
          buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        setIsExpanded(false);
      }
    }
    
    // Close on ESC key
    function handleEscKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        setIsExpanded(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('touchstart', handleClickOutside);
    document.addEventListener('keydown', handleEscKey);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isExpanded]);

  // Lock scroll when modal is open
  useEffect(() => {
    if (isExpanded) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isExpanded]);

  // Helper to check if voter ID is valid
  const hasValidId = (voter: Voter): boolean => {
    return Boolean(voter.id && voter.id !== 'undefined' && voter.id.trim() !== '');
  };

  // Helper to get display name
  const getDisplayName = (voter: Voter): string => {
    return voter.profile?.displayName || voter.username || (hasValidId(voter) ? voter.id : 'Unknown User');
  };

  const renderVoterTooltip = (voter: Voter) => (
    <div className="flex items-center gap-2 p-1">
      {voter.profile?.avatarUrl && (
        <img
          src={voter.profile.avatarUrl}
          alt=""
          className="w-8 h-8 rounded-full border-2 border-primary-700/20"
        />
      )}
      <div>
        <span className="font-medium">{getDisplayName(voter)}</span>
        {voter.profile?.displayName && voter.username && (
          <span className="block text-xs opacity-75">@{voter.username}</span>
        )}
      </div>
    </div>
  );

  const renderVoterBubble = (voter: Voter, index: number) => (
    <Tooltip
      key={hasValidId(voter) ? voter.id : `voter-${index}`}
      content={renderVoterTooltip(voter)}
      position="top"
      delay={200}
      variant="primary"
    >
      {hasValidId(voter) ? (
        <Link
          to={`/user/${voter.id}`}
          className="transition-all duration-150 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600 rounded-full relative"
          onClick={(e) => e.stopPropagation()}
          aria-label={`View profile of ${getDisplayName(voter)}`}
        >
          {voter.profile?.avatarUrl ? (
            <img
              src={voter.profile.avatarUrl}
              alt={`${getDisplayName(voter)} profile`}
              className="w-8 h-8 rounded-full object-cover shadow-sm border-2 border-white"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary-500 to-accent-500 flex items-center justify-center text-white text-xs font-medium shadow-sm border-2 border-white">
              {(voter.profile?.displayName || voter.username || 'U').charAt(0).toUpperCase()}
            </div>
          )}
          {index === 0 && <span className="absolute -top-1 -right-1 w-3 h-3 bg-amber-500 rounded-full border border-white"></span>}
        </Link>
      ) : (
        <div
          className="transition-all duration-150 rounded-full relative"
          aria-label={getDisplayName(voter)}
        >
          {voter.profile?.avatarUrl ? (
            <img
              src={voter.profile.avatarUrl}
              alt={`${getDisplayName(voter)} profile`}
              className="w-8 h-8 rounded-full object-cover shadow-sm border-2 border-white"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary-500 to-accent-500 flex items-center justify-center text-white text-xs font-medium shadow-sm border-2 border-white">
              {(voter.profile?.displayName || voter.username || 'U').charAt(0).toUpperCase()}
            </div>
          )}
          {index === 0 && <span className="absolute -top-1 -right-1 w-3 h-3 bg-amber-500 rounded-full border border-white"></span>}
        </div>
      )}
    </Tooltip>
  );

  // Don't render anything if there are no voters
  if (voters.length === 0) return null;

  return (
    <div className={cn("relative", className)}>
      {showButton ? (
        <button
          ref={buttonRef}
          onClick={toggleExpanded}
          className={cn(
            "group flex items-center gap-1.5 text-gray-600 hover:text-primary-600 transition-colors duration-150",
            "focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600 rounded px-2 py-1",
            compact ? "py-0.5" : "py-1",
            buttonClassName
          )}
          title={`${voters.length} ${voters.length === 1 ? 'person' : 'people'} voted`}
          aria-expanded={isExpanded}
          aria-controls="voters-popup"
        >
          <UserGroupIcon className={cn(
            "transition-transform duration-150 ease-in-out group-hover:scale-105",
            compact ? "h-4 w-4" : "h-5 w-5"
          )} />
          {!compact && !hideCount && (
            <span className="text-sm">{voters.length}</span>
          )}
        </button>
      ) : voters.length > 0 && (
        <div className="flex -space-x-1.5">
          {voters.slice(0, 3).map(renderVoterBubble)}
          {voters.length > 3 && (
            <button
              onClick={toggleExpanded}
              className={cn(
                "rounded-full bg-gray-100 flex items-center justify-center font-medium text-gray-700 hover:bg-gray-200 transition-colors duration-150 border-2 border-white shadow-sm",
                compact ? "w-5 h-5 text-[0.65rem]" : "w-7 h-7 text-xs"
              )}
              title={`+${voters.length - 3} more voters`}
            >
              +{voters.length - 3}
            </button>
          )}
        </div>
      )}

      {/* Portal for popup */}
      {isExpanded && typeof window !== 'undefined' && ReactDOM.createPortal(
        <>
          {/* Backdrop */}
          <motion.div 
            initial={{ opacity: 0 }} 
            animate={{ opacity: 1 }} 
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/40 backdrop-blur-sm z-[9998]"
            onClick={() => setIsExpanded(false)}
          />
          
          {/* Popup */}
          <AnimatePresence>
            <motion.div
              ref={popupRef}
              id="voters-popup"
              role="dialog"
              aria-modal="true"
              aria-label="Voters list"
              initial={{ opacity: 0, scale: 0.95, y: 10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 10 }}
              transition={{ duration: 0.15, ease: "easeOut" }}
              style={{
                position: 'fixed',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                maxHeight: 'calc(100vh - 100px)',
                width: '100%',
                maxWidth: '400px',
                zIndex: 9999
              }}
              className="z-[9999] bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 p-4 overflow-hidden flex flex-col transition-colors duration-150"
            >
              <div className="flex items-center justify-between mb-4 pb-2 border-b border-gray-100 dark:border-gray-700">
                <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                  <UserGroupIcon className="h-5 w-5 mr-2 text-primary-500" />
                  {voters.length} {voters.length === 1 ? 'Vote' : 'Votes'}
                </h3>
                <button
                  onClick={toggleExpanded}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  aria-label="Close voter list"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>
              
              <div className="overflow-y-auto flex-1 -mx-4 px-4">
                <div className="space-y-1">
                  {voters.map((voter, index) => (
                    <motion.div
                      key={hasValidId(voter) ? voter.id : `voter-${index}`}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2, delay: index * 0.03 }}
                    >
                      {hasValidId(voter) ? (
                        <Link
                          to={`/user/${voter.id}`}
                          className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-150 group"
                          onClick={() => setIsExpanded(false)}
                        >
                          {voter.profile?.avatarUrl ? (
                            <img
                              src={voter.profile.avatarUrl}
                              alt=""
                              className="w-10 h-10 rounded-full border-2 border-gray-200 dark:border-gray-600 group-hover:border-primary-200 dark:group-hover:border-primary-700 transition-colors duration-150 shadow-sm"
                            />
                          ) : (
                            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white text-sm font-medium border-2 border-white dark:border-gray-700 shadow-sm group-hover:from-primary-500 group-hover:to-primary-700 transition-colors duration-150">
                              {((voter.profile?.displayName || voter.username || '?')[0] || '?').toUpperCase()}
                            </div>
                          )}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-baseline justify-between">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-150">
                                {getDisplayName(voter)}
                              </h4>
                              <ChevronRightIcon className="h-4 w-4 text-gray-400 group-hover:text-primary-500 transition-all duration-150 transform group-hover:translate-x-0.5" />
                            </div>
                            {voter.profile?.displayName && voter.username && (
                              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                @{voter.username}
                              </p>
                            )}
                          </div>
                        </Link>
                      ) : (
                        <div className="flex items-center gap-3 p-2 rounded-lg group">
                          {voter.profile?.avatarUrl ? (
                            <img
                              src={voter.profile.avatarUrl}
                              alt=""
                              className="w-10 h-10 rounded-full border-2 border-gray-200 dark:border-gray-600 transition-colors duration-150 shadow-sm"
                            />
                          ) : (
                            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white text-sm font-medium border-2 border-white dark:border-gray-700 shadow-sm transition-colors duration-150">
                              {((voter.profile?.displayName || voter.username || '?')[0] || '?').toUpperCase()}
                            </div>
                          )}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-baseline justify-between">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate transition-colors duration-150">
                                {getDisplayName(voter)}
                              </h4>
                            </div>
                            {voter.username && (
                              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                @{voter.username}
                              </p>
                            )}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </>,
        document.body
      )}
    </div>
  );
}; 