import React from 'react';
import { Link } from 'react-router-dom';
import {
  MusicalNoteIcon,
  UserGroupIcon,
  PlayCircleIcon,
  ArrowRightIcon,
  ArrowRightOnRectangleIcon,
  ArrowLeftOnRectangleIcon
} from '@heroicons/react/24/outline';
import { logInfo } from '../../utils/logger';
import ArticleCarousel from './ArticleCarousel';

interface HeroSectionProps {
  isAuthenticated: boolean;
  onLogout: () => void;
  onGoToDashboard: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ isAuthenticated, onLogout, onGoToDashboard }) => (
  <div className="relative overflow-hidden bg-gradient-to-r from-[var(--gradient-start)] via-[var(--gradient-mid)] to-[var(--gradient-end)] py-16 sm:py-24">
    {/* Logo position in top-left */}
    <Link 
      to="/"
      className="absolute top-4 left-4 sm:top-6 sm:left-8 md:top-8 md:left-10 z-20 hover:opacity-90 transition-opacity"
      onClick={() => logInfo('HeroSection: Logo clicked')}
    >
      <img 
        src="/img/logo.png" 
        alt="Social Dance Moments Logo" 
        className="h-24 sm:h-32 md:h-36 w-auto drop-shadow-lg"
      />
    </Link>
    
    <div className="absolute inset-0 overflow-hidden">
      <div className="absolute left-1/2 top-0 -translate-x-1/2 transform">
        <MusicalNoteIcon className="h-20 w-20 text-white/20" />
      </div>
      <div className="absolute right-1/4 bottom-0">
        <MusicalNoteIcon className="h-24 w-24 text-white/10" />
      </div>
      <div className="absolute left-1/4 top-1/2">
        <MusicalNoteIcon className="h-16 w-16 text-white/10" />
      </div>
    </div>
    <div className="relative z-10 mx-auto max-w-5xl text-center px-4 sm:px-6 lg:px-8">
      <MusicalNoteIcon className="h-14 w-14 text-white/80 mx-auto mb-8 animate-pulse" />
      <h1 className="text-3xl font-extrabold tracking-tight text-white sm:text-4xl md:text-5xl">
        <span className="block mb-2">Social Dance Moments</span>
        <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-pink-300 mb-4 text-xl sm:text-2xl md:text-3xl flex items-center justify-center gap-2">
          Find Top Latin Dance Clubs & Parties in Bulgaria
          <a
            href="#clubs-section-card"
            aria-label="Go to Dance Clubs section"
            className="inline-flex items-center p-1 rounded-full bg-white/20 hover:bg-white/40 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 ml-2"
            onClick={e => {
              e.preventDefault();
              logInfo('HeroSection: CTA - Go to Dance Clubs section');
              const el = document.getElementById('clubs-section-card');
              if (el) {
                el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                el.focus({ preventScroll: true });
              }
            }}
          >
            <UserGroupIcon className="h-9 w-9 text-teal-500" />
          </a>
        </span>
        <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-pink-300 mb-4 text-xl sm:text-2xl md:text-3xl flex items-center justify-center gap-2">
          Listen To Your Favorite Latin Radio!
          <a href="#radio-section" aria-label="Go to Radio section" className="inline-flex items-center p-1 rounded-full bg-white/20 hover:bg-white/40 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 ml-2" onClick={() => logInfo('HeroSection: CTA - Go to Radio section')}>
            <PlayCircleIcon className="h-9 w-9 text-pink-400" />
          </a>
        </span>
      </h1>

      <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-pink-300 mb-4 text-xl sm:text-2xl md:text-3xl flex items-center justify-center gap-2">Suggest Your Favorite Songs To Improve the Radio And Park Parties in Sofia!</span>

      <p className="mx-auto mt-6 max-w-lg text-center text-xl text-white sm:max-w-3xl">
        Discover, suggest, and vote for your favorite dance songs!
      </p>
      <div className="mt-10 flex flex-col sm:flex-row justify-center gap-5 items-center">
        {isAuthenticated ? (
          <>
            <button
              onClick={() => {
                logInfo('HeroSection: CTA - My Dashboard');
                onGoToDashboard();
              }}
              className="rounded-md bg-primary-600 px-8 py-3 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-lg transform transition-all duration-300 hover:scale-105 inline-flex items-center"
            >
              My Dashboard <ArrowRightIcon className="ml-2 h-5 w-5" />
            </button>
            <button
              onClick={() => {
                logInfo('HeroSection: CTA - Log out');
                onLogout();
              }}
              className="rounded-md bg-white px-8 py-3 text-base font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-lg transform transition-all duration-300 hover:scale-105 inline-flex items-center"
            >
              <ArrowRightOnRectangleIcon className="mr-2 h-5 w-5 text-gray-500" /> Log out
            </button>
          </>
        ) : (
          <>
            <Link
              to="/login"
              onClick={() => logInfo('HeroSection: CTA - Sign In')}
              className="rounded-md bg-white px-8 py-3 text-base font-medium text-primary-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-lg transform transition-all duration-300 hover:scale-105 inline-flex items-center"
            >
              <ArrowLeftOnRectangleIcon className="mr-2 h-5 w-5" /> Sign In
            </Link>
            <p className="text-xs text-gray-200 mt-2 sm:mt-0 sm:ml-4 text-center sm:text-left">
              You can Sign in with your Google or Facebook account from "Sign In" button above.
            </p>
            <Link
              to="/signup"
              onClick={() => logInfo('HeroSection: CTA - Sign Up')}
              className="rounded-md bg-primary-600 px-8 py-3 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-lg transform transition-all duration-300 hover:scale-105"
            >
              Sign Up
            </Link>
          </>
        )}
      </div>
      
      {/* New Article Carousel Component */}
      <ArticleCarousel className="mt-8" />
    </div>
    {/* Fade to FeaturesSection's top color */}
    <div className="absolute inset-x-0 bottom-0 h-20 bg-gradient-to-t from-purple-800 to-transparent pointer-events-none" />
  </div>
);

export default HeroSection; 