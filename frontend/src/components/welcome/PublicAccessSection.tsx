import React, { Suspense } from 'react';
import PublicAccessCard from '../ui/PublicAccessCard';
import { MusicalNoteIcon, ChartBarIcon, UserGroupIcon, CalendarIcon, MicrophoneIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { logInfo } from '../../utils/logger';

// Separate component for the cards grid to enable suspense
const PublicFeatureCards = () => (
  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
    <PublicAccessCard
      icon={<MusicalNoteIcon className="h-7 w-7 text-pink-300 mb-4" />}
      title="Public Playlist"
      description="View the current party playlist"
      link="/public-playlist"
      ariaLabel="Go to Public Playlist page"
      onClick={() => logInfo('WelcomePage: Navigated to Public Playlist')}
    />
    <PublicAccessCard
      icon={<ChartBarIcon className="h-7 w-7 text-blue-300 mb-4" />}
      title="Charts"
      description="Discover top songs and dancers"
      link="/charts"
      ariaLabel="Go to Charts page"
      onClick={() => logInfo('WelcomePage: Navigated to Charts')}
      requiresAuth={true}
    />
    <PublicAccessCard
      icon={<UserGroupIcon className="h-7 w-7 text-teal-300 mb-4" />}
      title="Dance Clubs"
      description="Find top dance clubs in Bulgaria"
      link="/clubs"
      ariaLabel="Go to Dance Clubs page"
      onClick={() => logInfo('WelcomePage: Navigated to Clubs')}
      id="clubs-section-card"
      tabIndex={-1}
    />
    <PublicAccessCard
      icon={<CalendarIcon className="h-7 w-7 text-purple-300 mb-4" />}
      title="Events"
      description="Discover upcoming dance events"
      link="/events"
      ariaLabel="Go to Events page"
      onClick={() => logInfo('WelcomePage: Navigated to Events')}
    />
    {/* <PublicAccessCard
      icon={<MicrophoneIcon className="h-7 w-7 text-yellow-300 mb-4" />}
      title="Top DJs in Bulgaria"
      description="Explore the best salsa, bachata, and kizomba DJs"
      link="/djs"
      ariaLabel="Go to Top DJs page"
      onClick={() => logInfo('WelcomePage: Navigated to Top DJs')}
    /> */}
    {/* <PublicAccessCard
      icon={<SparklesIcon className="h-7 w-7 text-red-300 mb-4" />}
      title="Artist Dancers"
      description="Meet Bulgaria's dance instructors and performers"
      link="/dancers"
      ariaLabel="Go to Artist Dancers page"
      onClick={() => logInfo('WelcomePage: Navigated to Artist Dancers')}
    />
    <PublicAccessCard
      icon={<MusicalNoteIcon className="h-7 w-7 text-green-300 mb-4" />}
      title="Artist Singers"
      description="Discover Bulgarian bachata and salsa vocalists"
      link="/singers"
      ariaLabel="Go to Artist Singers page"
      onClick={() => logInfo('WelcomePage: Navigated to Artist Singers')}
    /> */}
  </div>
);

// Loading skeleton for the cards
const LoadingSkeleton = () => (
  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
    {[...Array(7)].map((_, i) => (
      <div key={i} className="bg-white/5 backdrop-blur-sm rounded-lg p-6 shadow-lg ring-1 ring-white/10 animate-pulse">
        <div className="h-7 w-7 bg-purple-300/20 rounded-lg mb-4"></div>
        <div className="h-6 bg-white/20 rounded w-2/3 mb-2"></div>
        <div className="h-4 bg-white/10 rounded w-full"></div>
      </div>
    ))}
  </div>
);

const PublicAccessSection: React.FC = () => (
  <section className="py-16 bg-gradient-to-br from-indigo-950 via-purple-950 to-indigo-950">
    <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 className="text-3xl font-extrabold tracking-tight text-white sm:text-4xl mb-8">
        Explore Public Features
      </h2>
      <Suspense fallback={<LoadingSkeleton />}>
        <PublicFeatureCards />
      </Suspense>
    </div>
  </section>
);

export default PublicAccessSection; 