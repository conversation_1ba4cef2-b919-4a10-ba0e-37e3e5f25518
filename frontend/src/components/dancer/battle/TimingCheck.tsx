import React, { useState, useEffect, useRef, useCallback } from 'react';

interface TimingCheckProps {
  onComplete: (score: number) => void;
  duration?: number; // Duration in milliseconds (default: 2000)
  pulseSpeed?: number; // Speed of pulse in milliseconds (default: 1000)
}

const TimingCheck: React.FC<TimingCheckProps> = ({
  onComplete,
  duration = 2000,
  pulseSpeed = 1000
}) => {
  const [isActive, setIsActive] = useState<boolean>(true);
  const [targetReached, setTargetReached] = useState<boolean>(false);
  const [score, setScore] = useState<number>(0);
  const [timeRemaining, setTimeRemaining] = useState<number>(duration);
  const [pulseProgress, setPulseProgress] = useState<number>(0);
  
  const pulseAnimationRef = useRef<number | null>(null);
  const timerRef = useRef<number | null>(null);
  
  // Function to handle the pulse animation
  const animatePulse = useCallback((timestamp: number) => {
    if (!isActive) return;
    
    // Calculate progress (0 to 100)
    const progress = ((timestamp % pulseSpeed) / pulseSpeed) * 100;
    setPulseProgress(progress);
    
    // Request next frame
    pulseAnimationRef.current = requestAnimationFrame(animatePulse);
  }, [isActive, pulseSpeed]);
  
  // Initialize the pulse animation and timer
  useEffect(() => {
    // Start pulse animation
    pulseAnimationRef.current = requestAnimationFrame(animatePulse);
    
    // Start countdown timer
    const startTime = Date.now();
    timerRef.current = window.setInterval(() => {
      const elapsed = Date.now() - startTime;
      const remaining = Math.max(0, duration - elapsed);
      setTimeRemaining(remaining);
      
      if (remaining <= 0) {
        // Time's up, auto-complete with current score
        handleComplete();
      }
    }, 100);
    
    return () => {
      // Clean up
      if (pulseAnimationRef.current) {
        cancelAnimationFrame(pulseAnimationRef.current);
      }
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [animatePulse, duration]);
  
  // Tap handler
  const handleTap = () => {
    if (!isActive || targetReached) return;
    
    // Calculate score based on how close to the target zone
    // Target zone is at 50% of the pulse (range 40-60%)
    const distance = Math.abs(pulseProgress - 50);
    let tapScore = 0;
    
    if (distance <= 5) {
      // Perfect! (45-55%)
      tapScore = 100;
      setTargetReached(true);
    } else if (distance <= 10) {
      // Great (40-45% or 55-60%)
      tapScore = 80;
    } else if (distance <= 20) {
      // Good (30-40% or 60-70%)
      tapScore = 60;
    } else if (distance <= 30) {
      // OK (20-30% or 70-80%)
      tapScore = 40;
    } else {
      // Miss
      tapScore = 20;
    }
    
    setScore(tapScore);
    handleComplete();
  };
  
  // Complete the timing check
  const handleComplete = () => {
    setIsActive(false);
    
    // Clear animation and timer
    if (pulseAnimationRef.current) {
      cancelAnimationFrame(pulseAnimationRef.current);
      pulseAnimationRef.current = null;
    }
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    
    // Call the onComplete callback with the score
    onComplete(score);
  };
  
  // Calculate pulse color based on progress
  const getPulseColor = () => {
    // Green zone in the middle (40-60%)
    if (pulseProgress >= 40 && pulseProgress <= 60) {
      return 'bg-green-500';
    }
    // Yellow zones on either side (20-40% or 60-80%)
    if ((pulseProgress >= 20 && pulseProgress < 40) || 
        (pulseProgress > 60 && pulseProgress <= 80)) {
      return 'bg-yellow-500';
    }
    // Red zones on the extremes (0-20% or 80-100%)
    return 'bg-red-500';
  };
  
  // Calculate pulse size based on progress
  const getPulseSize = () => {
    // Pulse grows from 60% to 100% and back
    // Map progress 0-100 to size 60-100-60
    const size = pulseProgress <= 50
      ? 60 + (pulseProgress * 0.8) // 60% to 100%
      : 100 - ((pulseProgress - 50) * 0.8); // 100% to 60%
    
    return `${size}%`;
  };
  
  return (
    <div className="relative w-full max-w-md mx-auto h-60 flex flex-col items-center justify-center bg-gray-900 rounded-lg p-4">
      <div className="text-center mb-4">
        <h3 className="text-xl font-bold text-amber-500">Tap On Beat!</h3>
        <p className="text-sm text-gray-300">
          Tap when the circle is in the green zone
        </p>
      </div>
      
      {/* Timer bar */}
      <div className="w-full h-2 bg-gray-700 rounded-full mb-4">
        <div 
          className="h-2 bg-amber-500 rounded-full transition-all duration-100"
          style={{ width: `${(timeRemaining / duration) * 100}%` }}
        ></div>
      </div>
      
      {/* Pulse circle container */}
      <div 
        onClick={handleTap}
        className="relative w-32 h-32 flex items-center justify-center cursor-pointer"
      >
        {/* Background target zones */}
        <div className="absolute inset-0 rounded-full border-4 border-green-500/30"></div>
        <div className="absolute inset-2 rounded-full border-4 border-green-500/60"></div>
        
        {/* Pulsing circle */}
        <div 
          className={`absolute rounded-full transition-all duration-50 ${getPulseColor()}`}
          style={{ 
            width: getPulseSize(), 
            height: getPulseSize(),
            opacity: targetReached ? 0.8 : 0.6
          }}
        ></div>
        
        {/* Score display (shown after tap) */}
        {!isActive && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-2xl font-bold text-white">
              {score}
            </div>
          </div>
        )}
      </div>
      
      {/* Results display */}
      {!isActive && (
        <div className="mt-4 text-center">
          {score >= 90 ? (
            <div className="text-green-500 font-semibold text-lg">Perfect!</div>
          ) : score >= 70 ? (
            <div className="text-amber-500 font-semibold text-lg">Great!</div>
          ) : score >= 50 ? (
            <div className="text-yellow-500 font-semibold text-lg">Good</div>
          ) : score >= 30 ? (
            <div className="text-orange-500 font-semibold text-lg">OK</div>
          ) : (
            <div className="text-red-500 font-semibold text-lg">Miss</div>
          )}
        </div>
      )}
    </div>
  );
};

export default TimingCheck; 