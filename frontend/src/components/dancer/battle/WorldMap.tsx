import React, { useState, useEffect } from 'react';
import { MapP<PERSON>, Lock, Star, Trophy, <PERSON>, EyeOff, Sword, Zap } from 'lucide-react';

interface Venue {
  id: string;
  name: string;
  type: string;
  description: string;
  location: string;
  coordinates: { x: number; y: number };
  isHidden: boolean;
  unlockLevel: number;
  specialties: string[];
  difficulty: number;
  rewards: any;
  discovered?: boolean;
  completed?: boolean;
}

interface WorldMapProps {
  heroLevel: number;
  onVenueSelect: (venue: Venue) => void;
  discoveredVenues: string[];
  onVenueBattle?: (venueId: string, danceStyle: string) => void;
}

const WorldMap: React.FC<WorldMapProps> = ({ 
  heroLevel, 
  onVenueSelect, 
  discoveredVenues,
  onVenueBattle 
}) => {
  const [venues, setVenues] = useState<Venue[]>([]);
  const [selectedVenue, setSelectedVenue] = useState<Venue | null>(null);
  const [loading, setLoading] = useState(true);
  const [showHidden, setShowHidden] = useState(false);
  const [discovering, setDiscovering] = useState<string | null>(null);

  useEffect(() => {
    fetchVenues();
    fetchDiscoveredVenues();
  }, []);

  const fetchVenues = async () => {
    try {
      const response = await fetch('/api/game/venues');
      const data = await response.json();
      if (data.success) {
        setVenues(data.venues);
      }
    } catch (error) {
      console.error('Error fetching venues:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDiscoveredVenues = async () => {
    try {
      const response = await fetch('/api/game/venues/discovered', {
        credentials: 'include'
      });
      const data = await response.json();
      if (data.success) {
        // Update venues with discovery status
        setVenues(prevVenues => 
          prevVenues.map(venue => ({
            ...venue,
            discovered: data.discoveredVenues.some((d: any) => d.venueId === venue.id)
          }))
        );
      }
    } catch (error) {
      console.error('Error fetching discovered venues:', error);
    }
  };

  const discoverVenue = async (venueId: string) => {
    try {
      setDiscovering(venueId);
      const response = await fetch(`/api/game/venues/${venueId}/discover`, {
        method: 'POST',
        credentials: 'include'
      });
      const data = await response.json();
      
      if (data.success) {
        // Update venue as discovered
        setVenues(prevVenues =>
          prevVenues.map(venue =>
            venue.id === venueId ? { ...venue, discovered: true } : venue
          )
        );
        
        // Show discovery notification
        alert(`🎉 Discovered ${data.venue.name}! +${data.xpReward} XP`);
        
        if (data.levelUp) {
          alert(`🎊 Level Up! You are now level ${data.levelUp.newLevel}!`);
        }
      } else {
        alert(data.message || 'Failed to discover venue');
      }
    } catch (error) {
      console.error('Error discovering venue:', error);
      alert('Failed to discover venue');
    } finally {
      setDiscovering(null);
    }
  };

  const startVenueBattle = async (venueId: string, danceStyle: string) => {
    if (onVenueBattle) {
      onVenueBattle(venueId, danceStyle);
    }
  };

  const getVenueIcon = (type: string) => {
    const icons = {
      DANCE_ACADEMY: '🏫',
      UNDERGROUND_CLUB: '🕳️',
      COMPETITION_HALL: '🏆',
      FESTIVAL_GROUND: '🎪',
      MYSTICAL_STUDIO: '✨',
      PRACTICE_ROOM: '📚',
      ROOFTOP_TERRACE: '🏢',
      BEACH_CLUB: '🏖️',
      HISTORIC_BALLROOM: '🏛️'
    };
    return icons[type as keyof typeof icons] || '📍';
  };

  const getVenueColor = (venue: Venue) => {
    if (venue.unlockLevel > heroLevel) return 'text-gray-400';
    if (venue.isHidden && !venue.discovered) return 'text-purple-400';
    if (venue.difficulty >= 8) return 'text-red-500';
    if (venue.difficulty >= 6) return 'text-orange-500';
    if (venue.difficulty >= 4) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getDifficultyStars = (difficulty: number) => {
    return Array.from({ length: 10 }, (_, i) => (
      <Star
        key={i}
        className={`w-3 h-3 ${i < difficulty ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const isVenueAccessible = (venue: Venue) => {
    if (venue.unlockLevel > heroLevel) return false;
    if (venue.isHidden && !venue.discovered) return false;
    return true;
  };

  const handleVenueClick = (venue: Venue) => {
    if (!isVenueAccessible(venue)) {
      // Try to discover hidden venues
      if (venue.isHidden && !venue.discovered && venue.unlockLevel <= heroLevel) {
        discoverVenue(venue.id);
        return;
      }
      return;
    }
    setSelectedVenue(venue);
    onVenueSelect(venue);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">Dance World Map</h2>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowHidden(!showHidden)}
            className="flex items-center space-x-2 px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded-lg text-white text-sm transition-colors"
          >
            {showHidden ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            <span>{showHidden ? 'Hide' : 'Show'} Hidden</span>
          </button>
          <div className="text-sm text-gray-300">
            Hero Level: <span className="text-yellow-400 font-bold">{heroLevel}</span>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className="relative bg-gradient-to-br from-emerald-100 via-sky-200 to-indigo-200 rounded-lg h-96 overflow-hidden border-2 border-amber-300 shadow-lg">
        {/* Background Image - Fantasy World Map Style */}
        <div className="absolute inset-0">
          <div 
            className="w-full h-full bg-cover bg-center bg-no-repeat" 
            style={{
              backgroundImage: `url('/img/world-map-background.jpg')`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              filter: 'sepia(20%) saturate(80%) hue-rotate(10deg) brightness(0.9)',
              opacity: 0.7
            }}
          />
        </div>
        
        {/* Decorative Overlay for better contrast */}
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>

        {/* Venues */}
        {venues.map((venue) => {
          const isAccessible = isVenueAccessible(venue);
          const isHidden = venue.isHidden && !venue.discovered;
          const isDiscovering = discovering === venue.id;
          
          if (isHidden && !showHidden) return null;

          return (
            <div
              key={venue.id}
              className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-200 hover:scale-125 hover:z-20 ${
                isAccessible ? 'hover:z-10' : 'cursor-not-allowed'
              } ${isDiscovering ? 'animate-pulse' : ''}`}
              style={{
                left: `${(venue.coordinates.x / 800) * 100}%`,
                top: `${(venue.coordinates.y / 600) * 100}%`,
              }}
              onClick={() => handleVenueClick(venue)}
            >
              {/* Venue Icon with Enhanced Contrast */}
              <div className={`relative ${isAccessible ? 'animate-pulse' : 'opacity-70'}`}>
                {/* Background circle for better contrast */}
                <div className="absolute inset-0 w-12 h-12 bg-black bg-opacity-60 rounded-full transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2 blur-sm"></div>
                <div className="absolute inset-0 w-10 h-10 bg-white bg-opacity-80 rounded-full transform -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2"></div>
                
                {/* Main venue icon */}
                <div className={`relative text-4xl ${getVenueColor(venue)} drop-shadow-2xl z-10`} style={{
                  textShadow: '2px 2px 4px rgba(0,0,0,0.8), -1px -1px 2px rgba(255,255,255,0.3)'
                }}>
                  {getVenueIcon(venue.type)}
                </div>
                
                {/* Lock Icon for Inaccessible Venues */}
                {!isAccessible && venue.unlockLevel > heroLevel && (
                  <Lock className="absolute -top-1 -right-1 w-5 h-5 text-red-500 bg-white rounded-full p-0.5 border-2 border-red-500" />
                )}
                
                {/* Hidden Venue Indicator */}
                {isHidden && (
                  <div className="absolute -top-2 -right-2 w-4 h-4 bg-purple-500 rounded-full animate-ping border-2 border-white" />
                )}
                
                {/* Discovery Indicator */}
                {isDiscovering && (
                  <div className="absolute -top-2 -left-2 w-5 h-5 bg-yellow-500 rounded-full animate-spin border-2 border-white">
                    <Zap className="w-3 h-3 text-white transform translate-x-0.5 translate-y-0.5" />
                  </div>
                )}
                
                {/* Difficulty Indicator */}
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                  <div className="flex space-x-0.5 bg-black bg-opacity-60 rounded-full px-1 py-0.5">
                    {Array.from({ length: Math.min(venue.difficulty, 3) }, (_, i) => (
                      <div key={i} className="w-1.5 h-1.5 bg-red-400 rounded-full" />
                    ))}
                  </div>
                </div>
              </div>

              {/* Venue Name Tooltip */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-black bg-opacity-90 text-white text-sm rounded-lg whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity pointer-events-none border border-gray-600 shadow-lg">
                <div className="font-semibold">{venue.name}</div>
                {!isAccessible && venue.unlockLevel > heroLevel && (
                  <div className="text-red-400 text-xs">
                    🔒 Requires Level {venue.unlockLevel}
                  </div>
                )}
                {isHidden && (
                  <div className="text-purple-400 text-xs">
                    ✨ Click to discover!
                  </div>
                )}
                <div className="text-gray-300 text-xs">
                  Difficulty: {venue.difficulty}/10
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Selected Venue Details */}
      {selectedVenue && (
        <div className="mt-6 bg-black bg-opacity-50 rounded-lg p-4 border border-purple-500">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <span className="text-2xl">{getVenueIcon(selectedVenue.type)}</span>
                <div>
                  <h3 className="text-xl font-bold text-white">{selectedVenue.name}</h3>
                  <p className="text-gray-300 text-sm">{selectedVenue.location}</p>
                </div>
              </div>
              
              <p className="text-gray-300 mb-3">{selectedVenue.description}</p>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-semibold text-purple-300 mb-1">Difficulty</h4>
                  <div className="flex space-x-1">
                    {getDifficultyStars(selectedVenue.difficulty)}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-semibold text-purple-300 mb-1">Specialties</h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedVenue.specialties.map((style, index) => (
                      <span key={index} className="px-2 py-1 bg-purple-600 text-white text-xs rounded">
                        {style}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
              
              {selectedVenue.rewards && (
                <div className="mt-3">
                  <h4 className="text-sm font-semibold text-purple-300 mb-1">Rewards</h4>
                  <div className="text-sm text-gray-300">
                    {selectedVenue.rewards.bonusXp && (
                      <span className="mr-3">+{selectedVenue.rewards.bonusXp} XP</span>
                    )}
                    {selectedVenue.rewards.coins && (
                      <span className="mr-3">+{selectedVenue.rewards.coins} Coins</span>
                    )}
                    {selectedVenue.rewards.specialMoves && (
                      <span>Special Moves Available</span>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            <div className="ml-4 space-y-2">
              {isVenueAccessible(selectedVenue) && (
                <div className="space-y-2">
                  <h4 className="text-sm font-semibold text-purple-300">Battle Styles</h4>
                  {selectedVenue.specialties.map((style) => (
                    <button
                      key={style}
                      className="block w-full px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg font-semibold transition-colors"
                      onClick={() => startVenueBattle(selectedVenue.id, style)}
                    >
                      <Sword className="w-4 h-4 inline mr-2" />
                      Battle ({style})
                    </button>
                  ))}
                </div>
              )}
              
              {!isVenueAccessible(selectedVenue) && (
                <button
                  className="px-4 py-2 bg-gray-600 text-gray-400 rounded-lg font-semibold cursor-not-allowed"
                  disabled
                >
                  {selectedVenue.unlockLevel > heroLevel ? 'Locked' : 'Hidden'}
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Legend */}
      <div className="mt-4 flex flex-wrap gap-4 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-gray-300">Easy (1-3)</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <span className="text-gray-300">Medium (4-5)</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
          <span className="text-gray-300">Hard (6-7)</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <span className="text-gray-300">Expert (8-10)</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-purple-500 rounded-full animate-ping"></div>
          <span className="text-gray-300">Hidden Venue</span>
        </div>
      </div>
    </div>
  );
};

export default WorldMap; 