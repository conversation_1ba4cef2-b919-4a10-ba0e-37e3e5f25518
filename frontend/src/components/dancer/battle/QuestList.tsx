import React, { useEffect, useState } from 'react';
import { useGame } from '../../../context/GameContext';

interface QuestRewardResponse {
  success: boolean;
  rewardXp: number;
  rewardCoins: number;
  leveledUp: boolean;
  newLevel?: number;
  claimedAt: string;
}

const QuestList: React.FC = () => {
  const { quests, fetchQuests, claimQuestReward, loading, hero } = useGame();
  const [claimingQuest, setClaimingQuest] = useState<number | null>(null);
  const [rewardAnimation, setRewardAnimation] = useState<{
    questId: number;
    xp: number;
    coins: number;
    levelUp?: boolean;
    newLevel?: number;
  } | null>(null);
  
  useEffect(() => {
    if (hero) {
      fetchQuests();
    }
  }, [hero, fetchQuests]);
  
  const handleClaimReward = async (questId: number) => {
    if (loading || claimingQuest !== null) return;
    
    setClaimingQuest(questId);
    try {
      const result = await claimQuestReward(questId) as QuestRewardResponse | undefined;
      if (result?.success) {
        // Display reward animation
        setRewardAnimation({
          questId,
          xp: result.rewardXp,
          coins: result.rewardCoins,
          levelUp: result.leveledUp,
          newLevel: result.newLevel
        });
        
        // Clear animation after delay
        setTimeout(() => {
          setRewardAnimation(null);
        }, 3000);
      }
    } finally {
      setClaimingQuest(null);
    }
  };
  
  if (!hero) {
    return null;
  }
  
  if (loading && quests.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-amber-500 mb-4">Daily Quests</h2>
        <div className="flex justify-center items-center h-40">
          <div className="animate-pulse text-amber-500">Loading quests...</div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-amber-500 mb-4">Daily Quests</h2>
      
      {quests.length === 0 ? (
        <div className="text-center text-gray-400 py-6">
          No quests available right now
        </div>
      ) : (
        <div className="space-y-4">
          {quests.map((questItem) => {
            const { quest, progress, completed, claimedAt } = questItem;
            const progressPercent = Math.min(100, Math.round((progress / quest.targetValue) * 100));
            
            return (
              <div 
                key={quest.id} 
                className={`p-4 rounded-lg relative overflow-hidden ${
                  completed
                    ? claimedAt
                      ? 'bg-green-900/20 border border-green-800'
                      : 'bg-amber-900/30 border border-amber-700'
                    : 'bg-gray-700'
                }`}
              >
                {/* Quest header */}
                <div className="flex justify-between items-start">
                  <h3 className="font-semibold text-white">{quest.title}</h3>
                  <div className="text-xs rounded px-2 py-0.5 font-medium bg-amber-700/50 text-amber-300">
                    {progress}/{quest.targetValue}
                  </div>
                </div>
                
                {/* Quest description */}
                <p className="text-sm text-gray-300 mt-1 mb-2">
                  {quest.description}
                </p>
                
                {/* Progress bar */}
                <div className="w-full bg-gray-600 rounded-full h-2 mb-3">
                  <div 
                    className={`h-2 rounded-full ${
                      completed 
                        ? 'bg-green-500' 
                        : 'bg-gradient-to-r from-amber-600 to-amber-400'
                    }`}
                    style={{ width: `${progressPercent}%` }}
                  ></div>
                </div>
                
                {/* Rewards & claim button */}
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span className="text-sm text-blue-400">{quest.rewardXp} XP</span>
                    </div>
                    
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-400 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm text-yellow-400">{quest.rewardCoins} coins</span>
                    </div>
                  </div>
                  
                  {completed && !claimedAt ? (
                    <button
                      onClick={() => handleClaimReward(quest.id)}
                      disabled={claimingQuest !== null}
                      className="px-3 py-1 bg-amber-600 hover:bg-amber-700 text-white text-sm rounded-md transition duration-200 disabled:opacity-50"
                    >
                      {claimingQuest === quest.id ? 'Claiming...' : 'Claim'}
                    </button>
                  ) : claimedAt ? (
                    <span className="text-sm text-green-400">Claimed</span>
                  ) : (
                    <span className="text-sm text-gray-400">In Progress</span>
                  )}
                </div>
                
                {/* Reward animation */}
                {rewardAnimation && rewardAnimation.questId === quest.id && (
                  <div className="absolute inset-0 bg-black/70 flex items-center justify-center flex-col">
                    <div className="text-2xl font-bold text-amber-500 mb-2">Reward Claimed!</div>
                    <div className="text-xl text-blue-400">+{rewardAnimation.xp} XP</div>
                    <div className="text-xl text-yellow-400 mb-2">+{rewardAnimation.coins} coins</div>
                    
                    {rewardAnimation.levelUp && (
                      <div className="mt-4 text-2xl font-bold text-green-400 animate-pulse">
                        LEVEL UP! → {rewardAnimation.newLevel}
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default QuestList; 