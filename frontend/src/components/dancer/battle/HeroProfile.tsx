import React, { useEffect } from 'react';
import { useGame } from '../../../context/GameContext';

const TraitLabels: Record<string, string> = {
  ON2_TIMING: 'On2 Timing',
  SPIN_MASTER: 'Spin Master',
  SMOOTH_LEADS: 'Smooth Leads',
  FOOTWORK_WIZARD: 'Footwork Wizard'
};

const HeroProfile: React.FC = () => {
  const { hero, refreshHero, loading } = useGame();
  
  useEffect(() => {
    if (!hero) {
      refreshHero();
    }
  }, [hero, refreshHero]);
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-pulse text-amber-500">Loading hero data...</div>
      </div>
    );
  }
  
  if (!hero) {
    return null;
  }
  
  // Calculate XP progress percentage
  const calculateXPProgress = () => {
    // This is a simplified version - would need actual level thresholds from backend
    const currentLevelXP = 0; // XP needed for current level
    const nextLevelXP = 1000; // XP needed for next level (this is just a placeholder)
    const currentXP = hero.experience - currentLevelXP;
    const requiredXP = nextLevelXP - currentLevelXP;
    return Math.min(100, Math.round((currentXP / requiredXP) * 100));
  };
  
  return (
    <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold text-amber-500">{hero.name}</h2>
            <div className="flex items-center mt-1">
              <span className="text-white font-semibold">Level {hero.level}</span>
              <div className="mx-2 text-gray-500">•</div>
              <span className="text-gray-300">{hero.wins}-{hero.losses}-{hero.draws}</span>
            </div>
          </div>
          
          <div className="bg-amber-900/30 px-3 py-1 rounded-full">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-amber-400 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
              </svg>
              <span className="text-amber-300 font-medium">{hero.energy} / 10</span>
            </div>
          </div>
        </div>
        
        {/* XP Progress Bar */}
        <div className="mt-4">
          <div className="flex justify-between text-xs mb-1">
            <span className="text-gray-400">XP: {hero.experience}</span>
            <span className="text-amber-300">{calculateXPProgress()}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-amber-600 to-amber-400 h-2 rounded-full" 
              style={{ width: `${calculateXPProgress()}%` }}
            ></div>
          </div>
        </div>
        
        {/* Traits */}
        <div className="mt-5">
          <h3 className="text-amber-300 font-medium mb-2">Traits</h3>
          <div className="flex flex-wrap gap-2">
            {hero.traits.map((trait) => (
              <span 
                key={trait} 
                className="bg-amber-800/30 text-amber-300 px-2 py-1 rounded text-sm"
              >
                {TraitLabels[trait] || trait}
              </span>
            ))}
          </div>
        </div>
        
        {/* Stats */}
        <div className="mt-5 grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400">{hero.wins}</div>
            <div className="text-xs text-gray-400 mt-1">Wins</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-red-400">{hero.losses}</div>
            <div className="text-xs text-gray-400 mt-1">Losses</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400">{hero.draws}</div>
            <div className="text-xs text-gray-400 mt-1">Draws</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroProfile; 