import React, { useState, useEffect } from 'react';
import { useGame } from '../../../context/GameContext';
import TimingCheck from './TimingCheck';

interface MoveButtonProps {
  move: {
    id: string;
    name: string;
    description: string;
    power: number;
    accuracy: number;
    type: string;
  };
  onClick: () => void;
  disabled: boolean;
}

const MoveButton: React.FC<MoveButtonProps> = ({ move, onClick, disabled }) => {
  // Different colors based on move type
  const getMoveTypeColor = () => {
    switch (move.type) {
      case 'SHINE':
        return 'from-blue-600 to-blue-800';
      case 'TURN':
        return 'from-purple-600 to-purple-800';
      default:
        return 'from-amber-600 to-amber-800';
    }
  };
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`relative w-full py-3 px-4 rounded-lg text-left transition-all duration-200 
        bg-gradient-to-br ${getMoveTypeColor()} hover:shadow-lg hover:-translate-y-0.5
        disabled:opacity-50 disabled:hover:translate-y-0 disabled:hover:shadow-none`}
    >
      <div className="flex justify-between items-center">
        <span className="font-medium text-white">{move.name}</span>
        <div className="flex space-x-2">
          <span className="text-xs bg-black/20 rounded px-1 py-0.5">
            Power: {move.power}
          </span>
          <span className="text-xs bg-black/20 rounded px-1 py-0.5">
            Acc: {move.accuracy}%
          </span>
        </div>
      </div>
      <p className="text-xs text-gray-200 mt-1">{move.description}</p>
    </button>
  );
};

const Battle: React.FC = () => {
  const { 
    hero, 
    availableMoves, 
    fetchMoves, 
    currentBattle, 
    selectMove,
    sendEmote,
    startTiming,
    stopTiming,
    isTimingActive
  } = useGame();
  
  const [selectedMove, setSelectedMove] = useState<string | null>(null);
  const [showMoveSelection, setShowMoveSelection] = useState(true);
  const [battleState, setBattleState] = useState<{
    playerHealth: number;
    opponentHealth: number;
    currentTurn: number;
    message: string;
  }>({
    playerHealth: 100,
    opponentHealth: 100,
    currentTurn: 1,
    message: 'Choose your move!'
  });
  
  useEffect(() => {
    if (hero && availableMoves.length === 0) {
      fetchMoves();
    }
  }, [hero, availableMoves, fetchMoves]);
  
  if (!currentBattle || !hero) {
    return null;
  }
  
  const player = currentBattle.heroes.find(h => h.id === hero.id);
  const opponent = currentBattle.heroes.find(h => h.id !== hero.id);
  
  if (!player || !opponent) {
    return null;
  }
  
  const handleMoveSelect = (moveId: string) => {
    setSelectedMove(moveId);
    setShowMoveSelection(false);
    startTiming();
  };
  
  const handleTimingComplete = async (score: number) => {
    if (!selectedMove) return;
    
    // Stop timing
    stopTiming(score);
    
    // Submit move
    await selectMove(selectedMove, score);
    
    // In a real implementation, we would wait for the battle result via WebSocket
    // For now, we'll just simulate a hit
    setTimeout(() => {
      const damage = Math.floor(Math.random() * 20) + 10;
      setBattleState(prev => ({
        ...prev,
        opponentHealth: Math.max(0, prev.opponentHealth - damage),
        currentTurn: prev.currentTurn + 1,
        message: `Your attack hit for ${damage} damage!`
      }));
      
      // Reset for next turn
      setShowMoveSelection(true);
      setSelectedMove(null);
    }, 1500);
  };
  
  const handleEmote = (emoji: string) => {
    sendEmote(emoji);
  };
  
  return (
    <div className="bg-gray-900 rounded-lg overflow-hidden">
      {/* Battle header */}
      <div className="bg-gradient-to-r from-amber-900 to-amber-800 p-4">
        <div className="flex justify-between">
          <div>
            <h2 className="text-xl font-bold text-white">{player.name}</h2>
            <div className="text-xs text-amber-300">Level {player.level}</div>
          </div>
          <div className="text-right">
            <h2 className="text-xl font-bold text-white">{opponent.name}</h2>
            <div className="text-xs text-amber-300">Level {opponent.level}</div>
          </div>
        </div>
      </div>
      
      {/* Battle arena */}
      <div className="p-6">
        {/* Health bars */}
        <div className="grid grid-cols-2 gap-6 mb-8">
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-xs text-white font-medium">Your HP</span>
              <span className="text-xs text-white">
                {Math.round(battleState.playerHealth / player.maxHealth * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5">
              <div 
                className="bg-green-600 h-2.5 rounded-full" 
                style={{ width: `${battleState.playerHealth / player.maxHealth * 100}%` }}
              ></div>
            </div>
          </div>
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-xs text-white font-medium">Opponent HP</span>
              <span className="text-xs text-white">
                {Math.round(battleState.opponentHealth / opponent.maxHealth * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5">
              <div 
                className="bg-red-600 h-2.5 rounded-full" 
                style={{ width: `${battleState.opponentHealth / opponent.maxHealth * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
        
        {/* Battle message */}
        <div className="bg-gray-800 p-3 rounded-lg mb-6 text-center">
          <p className="text-white font-medium">
            {battleState.message}
          </p>
          <div className="text-xs text-gray-400 mt-1">
            Turn {battleState.currentTurn} of {currentBattle.maxTurns}
          </div>
        </div>
        
        {/* Timing check */}
        {isTimingActive && selectedMove && (
          <TimingCheck onComplete={handleTimingComplete} />
        )}
        
        {/* Move selection */}
        {showMoveSelection && (
          <>
            <h3 className="text-white font-medium mb-3">Choose your move:</h3>
            <div className="space-y-3">
              {availableMoves.slice(0, 4).map((move) => (
                <MoveButton 
                  key={move.id}
                  move={move}
                  onClick={() => handleMoveSelect(move.id)}
                  disabled={false}
                />
              ))}
            </div>
          </>
        )}
        
        {/* Emotes */}
        <div className="mt-6 flex justify-center space-x-4">
          {['👏', '👍', '🔥', '💃', '🕺', '❤️'].map((emoji) => (
            <button
              key={emoji}
              onClick={() => handleEmote(emoji)}
              className="w-10 h-10 flex items-center justify-center text-xl bg-gray-800 rounded-full hover:bg-gray-700 transition-colors"
            >
              {emoji}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Battle; 