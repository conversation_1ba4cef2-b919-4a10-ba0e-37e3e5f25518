import React, { useState, useEffect, useCallback } from 'react';
import { Play, Pause, RotateCcw, Target, Zap, Clock, Award } from 'lucide-react';

interface BodyPart {
  key: string;
  name: string;
  color: string;
  position: { x: number; y: number };
}

interface Combo {
  id: string;
  name: string;
  danceStyle: string;
  sequence: string[];
  beatCount: number;
  timingWindow: number;
  difficulty: number;
  power: number;
  description: string;
  unlockLevel: number;
  masteryLevel: number;
  usageCount: number;
}

interface ComboTrainerProps {
  heroLevel: number;
  onComboMastered: (comboId: string) => void;
  selectedCombo?: Combo | null;
}

const ComboTrainer: React.FC<ComboTrainerProps> = ({ heroLevel, onComboMastered, selectedCombo }) => {
  const [combos, setCombos] = useState<Combo[]>([]);
  const [currentCombo, setCurrentCombo] = useState<Combo | null>(selectedCombo || null);
  const [inputSequence, setInputSequence] = useState<string[]>([]);
  const [isTraining, setIsTraining] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [timing, setTiming] = useState<number[]>([]);
  const [accuracy, setAccuracy] = useState(0);
  const [comboResult, setComboResult] = useState<'success' | 'failed' | null>(null);
  const [loading, setLoading] = useState(true);

  const bodyParts: BodyPart[] = [
    { key: 'LL', name: 'Left Leg', color: 'bg-blue-500', position: { x: 30, y: 80 } },
    { key: 'RL', name: 'Right Leg', color: 'bg-blue-600', position: { x: 70, y: 80 } },
    { key: 'LH', name: 'Left Hand', color: 'bg-green-500', position: { x: 20, y: 40 } },
    { key: 'RH', name: 'Right Hand', color: 'bg-green-600', position: { x: 80, y: 40 } },
    { key: 'H', name: 'Head', color: 'bg-yellow-500', position: { x: 50, y: 15 } },
    { key: 'T', name: 'Torso', color: 'bg-red-500', position: { x: 50, y: 45 } },
    { key: 'HP', name: 'Hips', color: 'bg-purple-500', position: { x: 50, y: 65 } },
  ];

  useEffect(() => {
    fetchCombos();
  }, []);

  useEffect(() => {
    if (selectedCombo) {
      setCurrentCombo(selectedCombo);
    }
  }, [selectedCombo]);

  const fetchCombos = async () => {
    try {
      const response = await fetch('/api/game/combos');
      const data = await response.json();
      if (data.success) {
        setCombos(data.combos);
        if (!currentCombo && data.combos.length > 0) {
          setCurrentCombo(data.combos[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching combos:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBodyPartClick = useCallback((bodyPart: string) => {
    if (!isTraining || !currentCombo) return;

    const newSequence = [...inputSequence, bodyPart];
    const newTiming = [...timing, Date.now()];
    
    setInputSequence(newSequence);
    setTiming(newTiming);

    // Check if this step is correct
    const expectedStep = currentCombo.sequence[currentStep];
    const isCorrect = bodyPart === expectedStep;

    if (isCorrect) {
      setCurrentStep(currentStep + 1);
      
      // Check if combo is complete
      if (newSequence.length === currentCombo.sequence.length) {
        completeCombo(newTiming);
      }
    } else {
      // Wrong input - fail the combo
      setComboResult('failed');
      setTimeout(resetTraining, 1500);
    }
  }, [isTraining, currentCombo, inputSequence, timing, currentStep]);

  const completeCombo = (timingData: number[]) => {
    if (!currentCombo) return;

    // Calculate timing accuracy
    const idealTiming = currentCombo.beatCount * (60000 / 120); // Assuming 120 BPM
    const actualTiming = timingData[timingData.length - 1] - timingData[0];
    const timingAccuracy = Math.max(0, 100 - Math.abs(actualTiming - idealTiming) / idealTiming * 100);
    
    setAccuracy(timingAccuracy);
    setComboResult(timingAccuracy >= 70 ? 'success' : 'failed');
    
    if (timingAccuracy >= 70) {
      onComboMastered(currentCombo.id);
    }
    
    setTimeout(resetTraining, 2000);
  };

  const resetTraining = () => {
    setInputSequence([]);
    setTiming([]);
    setCurrentStep(0);
    setIsTraining(false);
    setComboResult(null);
    setAccuracy(0);
  };

  const startTraining = () => {
    resetTraining();
    setIsTraining(true);
  };

  const getBodyPartStyle = (bodyPart: BodyPart) => {
    const isActive = isTraining && currentCombo?.sequence[currentStep] === bodyPart.key;
    const isCompleted = inputSequence.includes(bodyPart.key);
    
    let className = `absolute w-12 h-12 rounded-full border-2 cursor-pointer transition-all duration-200 flex items-center justify-center text-white font-bold text-sm ${bodyPart.color}`;
    
    if (isActive) {
      className += ' animate-pulse border-yellow-400 scale-110 shadow-lg shadow-yellow-400/50';
    } else if (isCompleted) {
      className += ' border-green-400 opacity-75';
    } else {
      className += ' border-gray-400 hover:scale-105';
    }
    
    return className;
  };

  const getDanceStyleIcon = (style: string) => {
    const icons = {
      SALSA: '🔥',
      BACHATA: '💫',
      KIZOMBA: '🌊',
      ZOUK: '✨',
      CHACHA: '⚡',
      UNIVERSAL: '🌟'
    };
    return icons[style as keyof typeof icons] || '💃';
  };

  const getDifficultyColor = (difficulty: number) => {
    if (difficulty <= 3) return 'text-green-400';
    if (difficulty <= 6) return 'text-yellow-400';
    if (difficulty <= 8) return 'text-orange-400';
    return 'text-red-400';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900 rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">Combo Trainer</h2>
        <div className="flex items-center space-x-4">
          <select
            value={currentCombo?.id || ''}
            onChange={(e) => {
              const combo = combos.find(c => c.id === e.target.value);
              setCurrentCombo(combo || null);
              resetTraining();
            }}
            className="bg-gray-800 text-white px-3 py-2 rounded-lg border border-gray-600"
          >
            {combos.filter(c => c.unlockLevel <= heroLevel).map(combo => (
              <option key={combo.id} value={combo.id}>
                {getDanceStyleIcon(combo.danceStyle)} {combo.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {currentCombo && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Body Movement Interface */}
          <div className="bg-black bg-opacity-50 rounded-lg p-6 border border-purple-500">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Body Movement Interface
            </h3>
            
            {/* Human Figure */}
            <div className="relative w-full h-64 bg-gradient-to-b from-gray-800 to-gray-900 rounded-lg border border-gray-600 mb-4">
              {bodyParts.map((bodyPart) => (
                <div
                  key={bodyPart.key}
                  className={getBodyPartStyle(bodyPart)}
                  style={{
                    left: `${bodyPart.position.x}%`,
                    top: `${bodyPart.position.y}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                  onClick={() => handleBodyPartClick(bodyPart.key)}
                  title={bodyPart.name}
                >
                  {bodyPart.key}
                </div>
              ))}
              
              {/* Connection Lines */}
              <svg className="absolute inset-0 w-full h-full pointer-events-none">
                {/* Body outline */}
                <path
                  d="M 50% 15% L 50% 45% L 50% 65% L 30% 80% M 50% 45% L 70% 80% M 50% 45% L 20% 40% M 50% 45% L 80% 40%"
                  stroke="rgba(255,255,255,0.2)"
                  strokeWidth="2"
                  fill="none"
                />
              </svg>
            </div>

            {/* Control Buttons */}
            <div className="flex justify-center space-x-4">
              <button
                onClick={startTraining}
                disabled={isTraining}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>Start Training</span>
              </button>
              
              <button
                onClick={resetTraining}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
                <span>Reset</span>
              </button>
            </div>
          </div>

          {/* Combo Information */}
          <div className="space-y-4">
            {/* Combo Details */}
            <div className="bg-black bg-opacity-50 rounded-lg p-4 border border-purple-500">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <span className="text-2xl mr-2">{getDanceStyleIcon(currentCombo.danceStyle)}</span>
                  {currentCombo.name}
                </h3>
                <div className="flex items-center space-x-2">
                  <Zap className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400 font-bold">{currentCombo.power}</span>
                </div>
              </div>
              
              <p className="text-gray-300 text-sm mb-3">{currentCombo.description}</p>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Difficulty:</span>
                  <span className={`ml-2 font-semibold ${getDifficultyColor(currentCombo.difficulty)}`}>
                    {currentCombo.difficulty}/10
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Beat Count:</span>
                  <span className="ml-2 text-white">{currentCombo.beatCount}</span>
                </div>
                <div>
                  <span className="text-gray-400">Mastery:</span>
                  <span className="ml-2 text-purple-400">{currentCombo.masteryLevel}/5</span>
                </div>
                <div>
                  <span className="text-gray-400">Usage:</span>
                  <span className="ml-2 text-blue-400">{currentCombo.usageCount}</span>
                </div>
              </div>
            </div>

            {/* Sequence Display */}
            <div className="bg-black bg-opacity-50 rounded-lg p-4 border border-purple-500">
              <h4 className="text-md font-semibold text-white mb-3">Combo Sequence</h4>
              <div className="flex flex-wrap gap-2">
                {currentCombo.sequence.map((step, index) => {
                  const bodyPart = bodyParts.find(bp => bp.key === step);
                  const isCompleted = index < inputSequence.length;
                  const isCurrent = index === currentStep && isTraining;
                  
                  return (
                    <div
                      key={index}
                      className={`px-3 py-2 rounded-lg border-2 text-sm font-semibold transition-all ${
                        isCurrent
                          ? 'border-yellow-400 bg-yellow-400 bg-opacity-20 text-yellow-400 animate-pulse'
                          : isCompleted
                          ? 'border-green-400 bg-green-400 bg-opacity-20 text-green-400'
                          : 'border-gray-600 bg-gray-800 text-gray-300'
                      }`}
                    >
                      {step}
                      <div className="text-xs opacity-75">{bodyPart?.name}</div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Training Results */}
            {comboResult && (
              <div className={`bg-black bg-opacity-50 rounded-lg p-4 border-2 ${
                comboResult === 'success' ? 'border-green-500' : 'border-red-500'
              }`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {comboResult === 'success' ? (
                      <Award className="w-6 h-6 text-green-400" />
                    ) : (
                      <Clock className="w-6 h-6 text-red-400" />
                    )}
                    <span className={`font-semibold ${
                      comboResult === 'success' ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {comboResult === 'success' ? 'Combo Successful!' : 'Combo Failed'}
                    </span>
                  </div>
                  
                  {comboResult === 'success' && (
                    <div className="text-right">
                      <div className="text-yellow-400 font-bold text-lg">{accuracy.toFixed(1)}%</div>
                      <div className="text-xs text-gray-400">Timing Accuracy</div>
                    </div>
                  )}
                </div>
                
                {comboResult === 'success' && accuracy >= 90 && (
                  <div className="mt-2 text-center text-purple-400 font-semibold animate-bounce">
                    🎉 Perfect Execution! 🎉
                  </div>
                )}
              </div>
            )}

            {/* Progress Indicator */}
            {isTraining && (
              <div className="bg-black bg-opacity-50 rounded-lg p-4 border border-purple-500">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-white font-semibold">Progress</span>
                  <span className="text-gray-400">{currentStep}/{currentCombo.sequence.length}</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${(currentStep / currentCombo.sequence.length) * 100}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Legend */}
      <div className="mt-6 bg-black bg-opacity-30 rounded-lg p-4">
        <h4 className="text-white font-semibold mb-3">Body Part Controls</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
          {bodyParts.map((bodyPart) => (
            <div key={bodyPart.key} className="flex items-center space-x-2">
              <div className={`w-4 h-4 rounded-full ${bodyPart.color}`} />
              <span className="text-gray-300 text-sm">{bodyPart.key} - {bodyPart.name}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ComboTrainer; 