import React, { useState } from 'react';
import { useGame } from '../../../context/GameContext';

interface TraitOption {
  id: string;
  name: string;
  description: string;
}

const TRAITS: TraitOption[] = [
  {
    id: 'ON2_TIMING',
    name: 'On2 Timing',
    description: '+5% dodge chance. Your perfect timing on the 2 beat gives you an edge in avoiding attacks.'
  },
  {
    id: 'SPIN_MASTER',
    name: 'Spin Master',
    description: '+5% critical hit. Your spinning techniques occasionally deal extra damage.'
  },
  {
    id: 'SMOOTH_LEADS',
    name: 'Smooth Leads',
    description: '+10% accuracy. Your clear leading style makes your moves more likely to hit.'
  },
  {
    id: 'FOOTWORK_WIZARD',
    name: 'Footwork Wizard',
    description: '+5% power. Your fancy footwork adds extra impact to your moves.'
  }
];

const HeroCreation: React.FC = () => {
  const { createHero, loading, error } = useGame();
  const [name, setName] = useState('');
  const [selectedTrait, setSelectedTrait] = useState<string>('');
  const [step, setStep] = useState(1);
  
  const handleNameSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim().length >= 2) {
      setStep(2);
    }
  };
  
  const handleTraitSelect = (traitId: string) => {
    setSelectedTrait(traitId);
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim() && selectedTrait) {
      await createHero(name.trim(), selectedTrait);
    }
  };
  
  return (
    <div className="max-w-md mx-auto my-8 p-6 bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-amber-500 mb-6 text-center">Create Your Dance Hero</h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-900/50 text-red-200 rounded-md">
          {error}
        </div>
      )}
      
      {step === 1 ? (
        <form onSubmit={handleNameSubmit} className="space-y-4">
          <div>
            <label htmlFor="heroName" className="block text-amber-300 mb-2">
              Hero Name
            </label>
            <input
              type="text"
              id="heroName"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-4 py-2 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
              placeholder="Enter your dance hero name"
              required
              minLength={2}
              maxLength={30}
            />
            <p className="mt-1 text-sm text-gray-400">
              This will be your identity in Dance Battles
            </p>
          </div>
          
          <button
            type="submit"
            className="w-full py-2 px-4 bg-amber-600 hover:bg-amber-700 text-white font-bold rounded-md transition duration-200"
            disabled={name.trim().length < 2}
          >
            Continue
          </button>
        </form>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-amber-300 mb-3">
              Choose Your Trait
            </h3>
            <p className="text-sm text-gray-300 mb-4">
              Your hero's defining characteristic that gives a special advantage
            </p>
            
            <div className="space-y-3">
              {TRAITS.map((trait) => (
                <div
                  key={trait.id}
                  className={`p-3 rounded-md cursor-pointer transition-colors ${
                    selectedTrait === trait.id
                      ? 'bg-amber-700/60 border-l-4 border-amber-500'
                      : 'bg-gray-700 hover:bg-gray-700/80'
                  }`}
                  onClick={() => handleTraitSelect(trait.id)}
                >
                  <h4 className="font-medium text-amber-300">{trait.name}</h4>
                  <p className="text-sm text-gray-300">{trait.description}</p>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex gap-3">
            <button
              type="button"
              onClick={() => setStep(1)}
              className="flex-1 py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white font-bold rounded-md transition duration-200"
            >
              Back
            </button>
            <button
              type="submit"
              className="flex-1 py-2 px-4 bg-amber-600 hover:bg-amber-700 text-white font-bold rounded-md transition duration-200"
              disabled={!selectedTrait || loading}
            >
              {loading ? 'Creating...' : 'Create Hero'}
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default HeroCreation; 