import React, { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { getApprovedSuggestions, voteSuggestion, unvoteSuggestion } from '../../utils/api';
import { logInfo, logError, logWarn } from '../../utils/logger';
import toast from 'react-hot-toast';
import {
  HandThumbUpIcon,
  ArrowPathIcon,
  SparklesIcon,
  ShareIcon,
  ClockIcon,
  ArrowTopRightOnSquareIcon,
  MusicalNoteIcon,
  HeartIcon,
  LinkIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { HandThumbUpIcon as HandThumbUpSolidIcon } from '@heroicons/react/24/solid';
import { useAuthContext } from '../../context/AuthContext';
import { VotersList } from '../ui/VotersList';
import { useWebSocket } from '../../context/WebSocketContext';
import { Link } from 'react-router-dom';
import { useLocalStorage } from '../../hooks/useLocalStorage';

// Dance styles to show
const DANCE_STYLES = ['Bachata', 'Salsa', 'Kizomba', 'Zouk'];

// Define formatDuration locally to avoid external dependency
const formatDuration = (seconds: number | null | undefined): string => {
  if (!seconds) return '?:??';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// Algorithm types available for discovery
type AlgorithmType = 'standard' | 'trending' | 'personalized' | 'latest' | 'similar' | 'rotation';

interface SongItem {
  id: string;
  title: string;
  danceStyle: string;
  status: 'APPROVED';
  createdAt: string;
  updatedAt?: string;
  thumbnailUrl?: string;
  durationSeconds?: number | null;
  youtubeVideoId: string;
  votes: number;
  userId: string; // Added userId to easily filter out user's own suggestions
  user: {
    id: string;
    username: string;
    profile?: {
      displayName?: string;
      avatarUrl?: string;
    };
  };
  hasVoted: boolean;
  channelTitle?: string;
  voters?: {
    id: string;
    username: string;
    profile?: {
      displayName?: string;
      avatarUrl?: string;
    };
  }[];
}

const DanceStyleDiscoveries = () => {
  const [allSongs, setAllSongs] = useState<SongItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuthContext();
  const [voteLoading, setVoteLoading] = useState<Record<string, boolean>>({});
  const [refreshing, setRefreshing] = useState(false);
  const { socket, isConnected } = useWebSocket();
  // States to track which item has voters list open
  const [openVotersId, setOpenVotersId] = useState<string | null>(null);
  // Add selected dance style state
  const [selectedStyle, setSelectedStyle] = useState<string | null>(null);
  // Add filtered view toggle
  const [showOnlyDiscoverable, setShowOnlyDiscoverable] = useState(false);
  // Add state for algorithm type
  const [algorithmType, setAlgorithmType] = useState<AlgorithmType>('standard');
  // Track last fetch time to avoid excessive refreshes
  const [lastFetchTime, setLastFetchTime] = useState<Date>(new Date());
  const [songData, setSongData] = useState({});
  // Add cache management for song data
  const [cachedSongData, setCachedSongData] = useLocalStorage('danceStyleDiscoveries', {});
  const [cacheTimestamp, setCacheTimestamp] = useLocalStorage('danceStyleDiscoveriesTimestamp', 0);
  const [isUsingCache, setIsUsingCache] = useState(false);
  // Add a flag to prevent unnecessary fetch loops
  const [isFetching, setIsFetching] = useState(false);
  // Add a ref to track mounted state
  const isMounted = useRef(true);
  
  // Create WebSocket handler refs at component level, not inside useEffect
  const wsHandlersRef = useRef({
    suggestionVote: (data: { suggestionId: string }) => {
      // Only refresh data if we haven't refreshed recently
      if (shouldFetchNewData()) {
        logInfo('DanceStyleDiscoveries: Received vote update, refreshing songs');
        fetchApprovedSongs(algorithmType);
      }
    },
    suggestionApproved: () => {
      // Only refresh data if we haven't refreshed recently
      if (shouldFetchNewData()) {
        logInfo('DanceStyleDiscoveries: Suggestion approved, refreshing songs');
        fetchApprovedSongs(algorithmType);
      }
    }
  });
  
  // Add missing cache duration constants
  // Cache expiry time - 30 minutes
  const CACHE_EXPIRY_TIME = 30 * 60 * 1000;
  // Min time between refreshes - 5 minutes
  const MIN_FETCH_INTERVAL = 5 * 60 * 1000;
  
  // Checking if cache is valid
  const isCacheValid = useCallback(() => {
    const now = Date.now();
    return (
      Object.keys(cachedSongData).length > 0 &&
      cacheTimestamp > 0 &&
      now - cacheTimestamp < CACHE_EXPIRY_TIME
    );
  }, [cachedSongData, cacheTimestamp]);
  
  // Check if we should fetch new data
  const shouldFetchNewData = useCallback(() => {
    const now = Date.now();
    return !lastFetchTime || now - lastFetchTime.getTime() > MIN_FETCH_INTERVAL;
  }, [lastFetchTime]);

  // Group and filter songs by dance style
  const songsByStyle = useMemo(() => {
    const result: Record<string, SongItem[]> = {};
    
    // Initialize with empty arrays for each style
    DANCE_STYLES.forEach((style: string) => {
      result[style] = [];
    });
    
    // Filter songs if showOnlyDiscoverable is enabled
    const filteredSongs = showOnlyDiscoverable && user 
      ? allSongs.filter(song => !song.hasVoted && song.userId !== user.id)
      : allSongs;
    
    // Group songs by style and limit to 10 per style
    filteredSongs.forEach((song: SongItem) => {
      const style = song.danceStyle;
      if (DANCE_STYLES.includes(style) && result[style].length < 10) {
        result[style].push(song);
      }
    });
    
    return result;
  }, [allSongs, showOnlyDiscoverable, user]);

  // Enhanced fetchApprovedSongs with sophisticated algorithms
  const fetchApprovedSongs = useCallback(async (algorithmType: AlgorithmType = 'standard') => {
    // Prevent concurrent fetches
    if (isFetching) {
      logInfo('DanceStyleDiscoveries: Fetch already in progress, skipping');
      return;
    }
    
    setIsFetching(true);
    setLoading(true);
    setError(null);
    setRefreshing(true);
    setIsUsingCache(false);

    const handleFetchError = (err: any, danceStyle: string | null = null) => {
      // Log the error properly
      if (danceStyle) {
        logError(`DanceStyleDiscoveries: Failed to fetch ${danceStyle} songs with ${algorithmType} algorithm`, { 
          status: err.status || err.statusCode || 'unknown',
          algorithm: algorithmType 
        });
      } else {
        logError('DanceStyleDiscoveries: Error fetching songs', err);
      }
      
      // If we have valid cached data, use it as a fallback
      if (isCacheValid()) {
        logInfo('DanceStyleDiscoveries: Using cached data due to fetch error');
        setSongData(cachedSongData);
        setIsUsingCache(true);
        setLoading(false);
        // Show a more subtle notification for using cached data
        toast.custom((t) => (
          <div className={`${t.visible ? 'animate-enter' : 'animate-leave'} max-w-md w-full bg-white shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5`}>
            <div className="flex-1 w-0 p-4">
              <div className="flex items-start">
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">Using cached data</p>
                  <p className="mt-1 text-sm text-gray-500">We're showing you cached results while we resolve connection issues.</p>
                </div>
              </div>
            </div>
            <div className="flex border-l border-gray-200">
              <button onClick={() => toast.dismiss(t.id)} className="w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-indigo-600 hover:text-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                Dismiss
              </button>
            </div>
          </div>
        ), { duration: 4000 });
        return true; // Indicate we handled the error with cache
      }
      return false; // Cache fallback not available
    };

    try {
      logInfo('DanceStyleDiscoveries: Fetching approved songs using algorithm', { 
        algorithmType, 
        selectedStyle,
        userAuthenticated: !!user 
      });
      
      // Create an array to hold all fetched songs
      let fetchedSongs: SongItem[] = [];
      let failedStyles: string[] = [];
      
      // Map the algorithm type to the backend-supported chartType
      // Backend only supports 'standard' and 'discovery' as chartTypes
      const chartType = 'discovery'; // Always use discovery mode for the endpoint
      
      // Fetch songs for each dance style individually with a limit of 10
      const fetchPromises = DANCE_STYLES.map(style =>
        getApprovedSuggestions(style, 1, 20, chartType) // Fetch more songs to allow for better filtering
          .then(result => {
            if (result.success && result.data && Array.isArray(result.data)) {
              logInfo(`DanceStyleDiscoveries: Successfully fetched ${result.data.length} ${style} songs using ${algorithmType} algorithm`, {
                algorithm: algorithmType
              });
              return result.data;
            } else {
              const errorMsg = result.message || `Unknown error fetching ${style} songs`;
              logError(`DanceStyleDiscoveries: Failed to fetch ${style} songs with ${algorithmType} algorithm`, {
                status: result.status,
                algorithm: algorithmType
              });
              failedStyles.push(style);
              return [];
            }
          })
          .catch(err => {
            logError(`DanceStyleDiscoveries: Error fetching ${style} songs with ${algorithmType} algorithm`, {
              error: err,
              algorithm: algorithmType
            });
            failedStyles.push(style);
            return [];
          })
      );
      
      try {
        // Wait for all requests to complete
        const resultsArrays = await Promise.all(fetchPromises);
        
        // Combine all results
        const rawFetchedSongs: SongItem[] = [];
        resultsArrays.forEach(songs => {
          if (Array.isArray(songs)) {
            rawFetchedSongs.push(...songs);
          }
        });

        // Apply algorithm-specific sorting and filtering
        switch (algorithmType) {
          case 'trending':
            // Sort by recent popularity (votes within last week)
            fetchedSongs = rawFetchedSongs.sort((a, b) => {
              // Primary sort by votes
              if (b.votes !== a.votes) return b.votes - a.votes;
              // Secondary sort by recency
              return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
            });
            break;

          case 'latest':
            // Prioritize newest songs first, regardless of votes
            fetchedSongs = rawFetchedSongs.sort((a, b) => {
              return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
            });
            break;

          case 'similar':
            // Group songs by channel/artist for discovery of similar content
            if (user) {
              // Get all unique channels/artists
              const channels = new Set(rawFetchedSongs.map(song => song.channelTitle));
              const shuffledChannels = Array.from(channels).sort(() => Math.random() - 0.5);
              
              // For each channel, select one song
              const selectedSongs: SongItem[] = [];
              shuffledChannels.forEach(channel => {
                if (channel) {
                  const channelSongs = rawFetchedSongs.filter(song => song.channelTitle === channel);
                  // Prefer songs user hasn't voted for if available
                  const unvotedSongs = channelSongs.filter(song => !song.hasVoted && song.userId !== user.id);
                  
                  if (unvotedSongs.length > 0) {
                    // Pick a random unvoted song
                    selectedSongs.push(unvotedSongs[Math.floor(Math.random() * unvotedSongs.length)]);
                  } else if (channelSongs.length > 0) {
                    // If all voted, still pick one for variety
                    selectedSongs.push(channelSongs[Math.floor(Math.random() * channelSongs.length)]);
                  }
                }
              });
              
              fetchedSongs = selectedSongs;
            } else {
              // Fallback for non-authenticated users
              fetchedSongs = rawFetchedSongs.sort(() => Math.random() - 0.5);
            }
            break;

          case 'rotation':
            // Ensure style diversity by rotating through styles
            const songsByDanceStyle: Record<string, SongItem[]> = {};
            DANCE_STYLES.forEach(style => {
              songsByDanceStyle[style] = rawFetchedSongs.filter(song => song.danceStyle === style);
            });
            
            // Create an interleaved list from all styles
            fetchedSongs = [];
            let maxLength = Math.max(...DANCE_STYLES.map(style => songsByDanceStyle[style].length));
            
            for (let i = 0; i < maxLength; i++) {
              // Shuffle the order of styles for each "row" to ensure variety
              const shuffledStyles = [...DANCE_STYLES].sort(() => Math.random() - 0.5);
              
              shuffledStyles.forEach(style => {
                if (songsByDanceStyle[style][i]) {
                  fetchedSongs.push(songsByDanceStyle[style][i]);
                }
              });
            }
            break;

          case 'personalized':
            // Prioritize songs with characteristics similar to user's voted songs
            if (user) {
              // First, filter out songs the user has already voted for
              const nonVotedSongs = rawFetchedSongs.filter(song => !song.hasVoted && song.userId !== user.id);
              
              // Find what styles and channels the user tends to vote for
              const userPreferredStyles = new Set<string>();
              const userPreferredChannels = new Set<string>();
              
              rawFetchedSongs.forEach(song => {
                if (song.hasVoted) {
                  userPreferredStyles.add(song.danceStyle);
                  if (song.channelTitle) userPreferredChannels.add(song.channelTitle);
                }
              });
              
              // Score each song based on match to user preferences
              const scoredSongs = nonVotedSongs.map(song => {
                let score = 0;
                
                // Higher score if style matches user preferences
                if (userPreferredStyles.has(song.danceStyle)) score += 2;
                
                // Higher score if channel matches user preferences
                if (song.channelTitle && userPreferredChannels.has(song.channelTitle)) score += 3;
                
                // Boost newer songs slightly
                const daysSinceCreation = (new Date().getTime() - new Date(song.createdAt).getTime()) / (1000 * 60 * 60 * 24);
                if (daysSinceCreation < 30) score += 1;
                
                // Add small random factor to avoid identical recommendations
                score += Math.random() * 0.5;
                
                return { song, score };
              });
              
              // Sort by score and extract just the songs
              fetchedSongs = scoredSongs
                .sort((a, b) => b.score - a.score)
                .map(item => item.song);
            } else {
              // Fallback for non-authenticated users - mix of popular and new
              fetchedSongs = rawFetchedSongs
                .sort((a, b) => {
                  // 70% by votes, 30% by recency
                  const voteScore = (b.votes * 0.7) - (a.votes * 0.7);
                  const recencyScore = (new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()) * 0.3;
                  return voteScore + recencyScore;
                });
            }
            break;

          case 'standard':
          default:
            // Standard sorting with slight randomization for variety
            fetchedSongs = rawFetchedSongs
              .sort((a, b) => {
                // Primary sort by votes
                if (b.votes !== a.votes) return b.votes - a.votes;
                // Secondary sort by creation date
                return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
              })
              // Add a small amount of randomization to standard results
              .map(value => ({ value, sort: Math.random() }))
              .sort((a, b) => a.sort - b.sort)
              .map(({ value }) => value);
            break;
        }

        // Limit songs per style for clean display
        const limitedSongs: SongItem[] = [];
        const songsPerStyle: Record<string, number> = {};
        
        // Initialize counters
        DANCE_STYLES.forEach(style => {
          songsPerStyle[style] = 0;
        });
        
        // Add songs while respecting limits per style
        fetchedSongs.forEach(song => {
          const style = song.danceStyle;
          if (DANCE_STYLES.includes(style) && songsPerStyle[style] < 10) {
            limitedSongs.push(song);
            songsPerStyle[style]++;
          }
        });

        setAllSongs(limitedSongs);
        setAlgorithmType(algorithmType);
        setLastFetchTime(new Date());
        
        if (failedStyles.length > 0) {
          if (failedStyles.length === DANCE_STYLES.length) {
            logError('DanceStyleDiscoveries: Failed to fetch any dance style songs');
            setError('Could not fetch song discoveries. Please try again later.');
          } else {
            logWarn(`DanceStyleDiscoveries: Failed to fetch songs for styles: ${failedStyles.join(', ')}`);
            // We have some data, so don't set error, just log
          }
        }

        // On successful fetch, update the cache
        if (Object.keys(limitedSongs).length > 0) {
          setCachedSongData(limitedSongs);
          setCacheTimestamp(Date.now());
        }
      } catch (innerErr: any) {
        logError('DanceStyleDiscoveries: Error processing fetch results', innerErr);
        setError('Error processing song data. Please try refreshing.');
      }
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to load song discoveries';
      logError('DanceStyleDiscoveries: Unexpected error in fetch function', err);
      setError(errorMessage);
    } finally {
      if (isMounted.current) {
        setIsFetching(false);
        setRefreshing(false);
        setLoading(false);
      }
    }
  }, [algorithmType, selectedStyle, user, cachedSongData, isCacheValid]);

  // Prevent memory leaks
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Fetch data on initial load and when algorithm type changes
  useEffect(() => {
    if (isCacheValid()) {
      // Use cached data first for instant display
      setSongData(cachedSongData);
      // Add type assertion to fix the error
      setAllSongs((Object.values(cachedSongData).flat() as unknown) as SongItem[]);
      setIsUsingCache(true);
      setLoading(false);
      
      // Then check if we need to refresh in the background
      if (shouldFetchNewData()) {
        logInfo('DanceStyleDiscoveries: Background refresh after cache usage');
        fetchApprovedSongs(algorithmType);
      }
    } else {
      // No valid cache, fetch fresh data
      fetchApprovedSongs(algorithmType);
    }
  }, [algorithmType]); // Only depend on algorithm type, not on other state that might change frequently

  // Helper function for style-specific gradient and color classes
  const getStyleData = (style: string): {
    gradientBg: string;
    textColor: string;
    iconColor: string;
  } => {
    switch (style) {
      case 'Bachata':
        return {
          gradientBg: 'bg-gradient-to-r from-red-50 to-red-100',
          textColor: 'text-red-800',
          iconColor: 'text-red-600',
        };
      case 'Salsa':
        return {
          gradientBg: 'bg-gradient-to-r from-orange-50 to-orange-100',
          textColor: 'text-orange-800',
          iconColor: 'text-orange-600',
        };
      case 'Kizomba':
        return {
          gradientBg: 'bg-gradient-to-r from-purple-50 to-purple-100',
          textColor: 'text-purple-800',
          iconColor: 'text-purple-600',
        };
      case 'Zouk':
        return {
          gradientBg: 'bg-gradient-to-r from-blue-50 to-blue-100',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600',
        };
      default:
        return {
          gradientBg: 'bg-gradient-to-r from-gray-50 to-gray-100',
          textColor: 'text-gray-800',
          iconColor: 'text-gray-600',
        };
    }
  };

  // Handler for toggling votes
  const handleVoteToggle = useCallback(async (songId: string, hasVoted: boolean) => {
    if (!user) {
      toast.error('Please log in to like songs');
      logInfo('DanceStyleDiscoveries: Vote attempt without being logged in');
      return;
    }
    
    setVoteLoading(prev => ({ ...prev, [songId]: true }));
    logInfo(`DanceStyleDiscoveries: ${hasVoted ? 'Removing' : 'Adding'} vote for song`, { songId });
    
    try {
      const action = hasVoted ? unvoteSuggestion : voteSuggestion;
      const response = await action(songId);
      
      if (response.success) {
        logInfo(`DanceStyleDiscoveries: Successfully ${hasVoted ? 'removed' : 'added'} vote`, { songId });
        
        // Update songs with new vote status and update voters list
        setAllSongs(prev => prev.map(song => {
          if (song.id === songId) {
            // Create a copy of current voters list
            let updatedVoters = [...(song.voters || [])];
            
            if (hasVoted) {
              // Remove current user from voters
              updatedVoters = updatedVoters.filter(voter => voter.id !== user.id);
            } else {
              // Add current user to voters
              const currentUser = {
                id: user.id,
                username: user.username,
                profile: undefined
              };
              updatedVoters.push(currentUser);
            }
            
            return {
              ...song,
              votes: hasVoted ? song.votes - 1 : song.votes + 1,
              hasVoted: !hasVoted,
              voters: updatedVoters
            };
          }
          return song;
        }));
      } else {
        const errorMsg = response.message || 'Something went wrong';
        toast.error(errorMsg);
        logError(`DanceStyleDiscoveries: Failed to ${hasVoted ? 'remove' : 'add'} vote`, { 
          songId, 
          message: response.message 
        });
      }
    } catch (err) {
      toast.error('Failed to process your vote');
      logError(`DanceStyleDiscoveries: Error ${hasVoted ? 'removing' : 'adding'} vote`, { songId, error: err });
    } finally {
      setVoteLoading(prev => ({ ...prev, [songId]: false }));
    }
  }, [user]);

  const handleShareSong = useCallback((song: SongItem) => {
    const songUrl = `https://www.youtube.com/watch?v=${song.youtubeVideoId}`;
    navigator.clipboard.writeText(songUrl)
      .then(() => {
        toast.success('Link copied to clipboard!');
        logInfo('DanceStyleDiscoveries: Song link copied', { songId: song.id, url: songUrl });
      })
      .catch(err => {
        toast.error('Failed to copy link.');
        logError('DanceStyleDiscoveries: Error copying song link', err);
      });
  }, []);

  const openInYouTube = useCallback((videoId: string) => {
    const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`;
    window.open(youtubeUrl, '_blank');
    logInfo('DanceStyleDiscoveries: Opening song in YouTube', { videoId, url: youtubeUrl });
  }, []);

  // Processing song data for graphs
  const processChartData = (songs: SongItem[]) => {
    // ... existing code ...
  };

  // Add useEffect for WebSocket listeners with stable handler references
  useEffect(() => {
    if (socket && isMounted.current) {
      // Only setup listeners if socket exists and component is mounted
      logInfo('DanceStyleDiscoveries: Setting up WebSocket listeners');
      
      // Use the handlers from the ref created outside useEffect
      socket.on('suggestion_vote', wsHandlersRef.current.suggestionVote);
      socket.on('suggestion_approved', wsHandlersRef.current.suggestionApproved);
      
      return () => {
        if (socket) {
          logInfo('DanceStyleDiscoveries: Cleaning up WebSocket listeners');
          socket.off('suggestion_vote', wsHandlersRef.current.suggestionVote);
          socket.off('suggestion_approved', wsHandlersRef.current.suggestionApproved);
        }
      };
    }
  }, [socket]); // Only depend on socket connection

  if (loading) {
    return (
      <div className="my-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-6 text-center">
        <div className="animate-pulse flex space-x-1 justify-center mb-3">
          <div className="h-3 w-3 bg-primary-400 rounded-full"></div>
          <div className="h-3 w-3 bg-primary-500 rounded-full"></div>
          <div className="h-3 w-3 bg-primary-600 rounded-full"></div>
        </div>
        <p className="text-gray-600">Discovering dance songs for you...</p>
      </div>
    );
  }

  if (!error) {
    return (
      <div className="my-6 bg-white p-4 rounded-lg shadow-md">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-primary-700 flex items-center">
            <SparklesIcon className="h-5 w-5 mr-2 text-primary-500" />
            Dance Style Discoveries
          </h3>
          
          <div className="flex space-x-2">
            <div className="relative group">
              <button
                onClick={() => {
                  setRefreshing(true);
                  fetchApprovedSongs(algorithmType);
                }}
                className="p-1 text-gray-400 hover:text-primary-500 transition-colors"
                disabled={refreshing}
                aria-label="Refresh dance style discoveries"
              >
                <ArrowPathIcon className={`h-5 w-5 ${refreshing ? 'animate-spin text-primary-500' : ''}`} />
              </button>
              <div className="absolute hidden group-hover:block right-0 top-full mt-1 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap z-10">
                Refresh discoveries
              </div>
            </div>
          </div>
        </div>
        
        {/* Algorithm selection buttons - mobile friendly grid layout */}
        <div className="mt-3">
          <div className="grid grid-cols-3 gap-2 mb-2">
            <button
              onClick={() => {
                setRefreshing(true);
                fetchApprovedSongs('standard');
              }}
              className={`p-2 text-xs rounded-md font-medium ${algorithmType === 'standard' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
            >
              Standard
            </button>
            <button
              onClick={() => {
                setRefreshing(true);
                fetchApprovedSongs('trending');
              }}
              className={`p-2 text-xs rounded-md font-medium ${algorithmType === 'trending' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
            >
              Trending
            </button>
            <button
              onClick={() => {
                setRefreshing(true);
                fetchApprovedSongs('personalized');
              }}
              className={`p-2 text-xs rounded-md font-medium ${algorithmType === 'personalized' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
            >
              For You
            </button>
          </div>
          <div className="grid grid-cols-3 gap-2">
            <button
              onClick={() => {
                setRefreshing(true);
                fetchApprovedSongs('latest');
              }}
              className={`p-2 text-xs rounded-md font-medium ${algorithmType === 'latest' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
            >
              Latest
            </button>
            <button
              onClick={() => {
                setRefreshing(true);
                fetchApprovedSongs('similar');
              }}
              className={`p-2 text-xs rounded-md font-medium ${algorithmType === 'similar' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
            >
              Similar
            </button>
            <button
              onClick={() => {
                setRefreshing(true);
                fetchApprovedSongs('rotation');
              }}
              className={`p-2 text-xs rounded-md font-medium ${algorithmType === 'rotation' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
            >
              Rotation
            </button>
          </div>
        </div>
        
        {/* Add style selector buttons - mobile friendly grid layout */}
        <div className="mt-4 mb-3">
          <div className="grid grid-cols-3 gap-2 mb-2">
            <button
              onClick={() => setSelectedStyle(null)}
              className={`p-2 rounded-md text-xs font-medium transition-colors ${
                !selectedStyle 
                  ? 'bg-primary-600 text-white shadow-sm' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              All Styles
            </button>
            
            {DANCE_STYLES.slice(0, 2).map(style => (
              <button
                key={style}
                onClick={() => setSelectedStyle(style)}
                className={`p-2 rounded-md text-xs font-medium transition-colors ${
                  selectedStyle === style 
                    ? 'bg-primary-600 text-white shadow-sm' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {style}
              </button>
            ))}
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            {DANCE_STYLES.slice(2).map(style => (
              <button
                key={style}
                onClick={() => setSelectedStyle(style)}
                className={`p-2 rounded-md text-xs font-medium transition-colors ${
                  selectedStyle === style 
                    ? 'bg-primary-600 text-white shadow-sm' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {style}
              </button>
            ))}
          </div>
        </div>
        
        {/* Add discover mode toggle - improved for mobile */}
        <div className="mt-3 mb-3">
          <button
            onClick={() => setShowOnlyDiscoverable(!showOnlyDiscoverable)}
            className={`flex items-center justify-center w-full text-sm px-4 py-2.5 rounded-md transition-colors ${
              showOnlyDiscoverable 
                ? 'bg-green-100 text-green-700 border border-green-200' 
                : 'bg-gray-100 text-gray-600 border border-gray-200 hover:bg-gray-200'
            }`}
            title="Show only songs you haven't voted on and didn't suggest"
          >
            <EyeIcon className="h-4 w-4 mr-2" />
            {showOnlyDiscoverable ? "Showing new discoveries only" : "Show new discoveries only"}
          </button>
        </div>
        
        {/* Algorithm description */}
        <div className="mt-2 mb-4">
          <p className="text-xs text-gray-500 italic">
            {algorithmType === 'standard' && "Standard algorithm: Showing songs sorted by popularity and recency with light randomization"}
            {algorithmType === 'trending' && "Trending algorithm: Highlighting songs gaining popularity recently"}
            {algorithmType === 'personalized' && "For You: Personalized recommendations based on your voting history and preferences"}
            {algorithmType === 'latest' && "Latest: Most recently added songs first, regardless of popularity"}
            {algorithmType === 'similar' && "Similar: Discover songs from artists related to what you already enjoy"}
            {algorithmType === 'rotation' && "Rotation: Evenly balanced selection across all dance styles for maximum variety"}
          </p>
        </div>
        
        {allSongs.length === 0 ? (
          <div className="mt-4 p-3 bg-gray-50 rounded-md text-center">
            <p className="text-gray-600">No approved songs available yet.</p>
            <p className="text-gray-500 text-sm">Check back later for dance style discoveries!</p>
          </div>
        ) : (
          <div className="mt-4 space-y-4">
            {DANCE_STYLES.map((style: string) => {
              // Skip if a style is selected and this isn't it
              if (selectedStyle && style !== selectedStyle) return null;
              
              const styleData = getStyleData(style);
              const styleSpecificSongs = songsByStyle[style] || [];
              
              if (styleSpecificSongs.length === 0) return null;
              
              return (
                <div key={style} className={`p-3 rounded-md ${styleData.gradientBg}`}>
                  <h4 className={`text-md font-medium ${styleData.textColor} mb-2 flex items-center`}>
                    <MusicalNoteIcon className={`h-4 w-4 mr-1 ${styleData.iconColor}`} />
                    {style} 
                    <span className="ml-1 text-xs bg-white bg-opacity-30 rounded-full px-2 py-0.5">
                      {styleSpecificSongs.length}
                    </span>
                  </h4>
                  
                  <div className="grid grid-cols-1 gap-2">
                    {styleSpecificSongs.map(song => (
                      <div 
                        key={song.id} 
                        className={`p-2 rounded bg-white bg-opacity-90 shadow-sm flex items-start`}
                      >
                        <div 
                          className="w-12 h-12 rounded overflow-hidden mr-3 flex-shrink-0 cursor-pointer"
                          onClick={() => openInYouTube(song.youtubeVideoId)}
                        >
                          {song.thumbnailUrl ? (
                            <img 
                              src={song.thumbnailUrl} 
                              alt={song.title} 
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                // On error, replace with placeholder
                                (e.target as HTMLImageElement).src = 'https://placehold.co/120/gray/white?text=No+Image';
                              }}
                            />
                          ) : (
                            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                              <MusicalNoteIcon className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>
                        
                        <div className="min-w-0 flex-1">
                          <p className="text-sm font-medium text-gray-800 truncate" title={song.title}>
                            {song.title}
                          </p>
                          
                          <p className="text-xs text-gray-500 truncate">
                            <Link 
                              to={`/user/${song.user.id}`}
                              className="hover:text-primary-600 hover:underline"
                            >
                              {song.user.profile?.displayName || song.user.username}
                            </Link>
                          </p>
                          
                          <div className="flex items-center mt-1">
                            <span className="mr-2 flex items-center text-xs text-gray-400">
                              <ClockIcon className="h-3 w-3 mr-1" />
                              {formatDuration(song.durationSeconds)}
                            </span>
                            
                            <div className="flex items-center">
                              {/* Vote Button */}
                              <button 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleVoteToggle(song.id, song.hasVoted);
                                }}
                                disabled={voteLoading[song.id]}
                                className={`flex items-center space-x-1 text-xs px-2 py-1 rounded-full transition-colors ${
                                  song.hasVoted
                                    ? 'bg-primary-100 text-primary-700 hover:bg-primary-200'
                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                } ${voteLoading[song.id] ? 'opacity-50 cursor-not-allowed' : ''}`}
                                title={song.hasVoted ? 'Remove vote' : 'Vote for this song'}
                                aria-label={song.hasVoted ? 'Remove vote' : 'Vote for this song'}
                                data-dance-style={style}
                              >
                                {song.hasVoted ? (
                                  <HandThumbUpSolidIcon className="h-4 w-4" />
                                ) : (
                                  <HandThumbUpIcon className="h-4 w-4" />
                                )}
                                <span className="ml-1">{song.votes || 0}</span>
                              </button>
                              
                              {/* Voters List Button */}
                              {song.voters && song.voters.length > 0 && (
                                <VotersList 
                                  voters={song.voters}
                                  totalVotes={song.votes}
                                  compact={true}
                                  className="ml-2"
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  }

  // If there's an error and we're not using cache, show error display
  if (error && !isUsingCache) {
    return (
      <div className="my-6 bg-white p-4 rounded-lg shadow-md">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-primary-700 flex items-center">
            <SparklesIcon className="h-5 w-5 mr-2 text-primary-500" />
            Dance Style Discoveries
          </h3>
          {isUsingCache && (
            <span className="text-xs text-gray-500 bg-gray-100 rounded-full px-2 py-1">
              Cached data
            </span>
          )}
        </div>
        <div className="mt-4 flex flex-col items-center text-center">
          <div className="w-full p-4 bg-red-50 rounded-lg mb-3 border border-red-100">
            <p className="text-red-600 mb-2 font-medium">{error}</p>
            <p className="text-gray-600 text-sm mb-3">We couldn't load all dance style songs. Please try again.</p>
          </div>
          <button
            onClick={() => fetchApprovedSongs('standard')}
            className="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-md flex items-center justify-center transition-colors"
          >
            <ArrowPathIcon className="h-4 w-4 mr-1" />
            Refresh Songs
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="my-6 bg-white p-4 rounded-lg shadow-md">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-primary-700 flex items-center">
          <SparklesIcon className="h-5 w-5 mr-2 text-primary-500" />
          Dance Style Discoveries
        </h3>
      </div>
      <div className="mt-4 flex flex-col items-center text-center">
        <div className="w-full p-4 bg-red-50 rounded-lg mb-3 border border-red-100">
          <p className="text-red-600 mb-2 font-medium">{error}</p>
          <p className="text-gray-600 text-sm mb-3">We couldn't load all dance style songs. Please try again.</p>
        </div>
        <button
          onClick={() => fetchApprovedSongs('standard')}
          className="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-md flex items-center justify-center transition-colors"
        >
          <ArrowPathIcon className="h-4 w-4 mr-1" />
          Refresh Songs
        </button>
      </div>
    </div>
  );
};

export default DanceStyleDiscoveries; 