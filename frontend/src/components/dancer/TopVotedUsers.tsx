import React, { useState, useEffect, FC, useCallback, useRef } from 'react';
import { getTopVotedUsers } from '../../utils/api';
import { logInfo, logError } from '../../utils/logger';
import toast from 'react-hot-toast';
import { TrophyIcon, UserCircleIcon, SparklesIcon, FireIcon, ChartBarIcon } from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';
import { useLocalStorage } from '../../hooks/useLocalStorage';

// Add retry counter state
const MAX_RETRY_COUNT = 3;

export interface TopVotedUserEntry {
  id: string;
  username: string;
  displayName?: string;
  avatarUrl?: string;
  voteCount: number;
  rank: number;
  totalVotes?: number;
  suggestionCount?: number;
  approvedSuggestions?: number;
  averageVotesPerSuggestion?: string;
}

interface TopVotedUsersProps {
  limit?: number;
  showTimeframeSelector?: boolean;
  className?: string;
}

// Define timeframe type for consistency
type TimeframeType = 'week' | 'month' | 'alltime';

const TopVotedUsers: FC<TopVotedUsersProps> = ({ 
  limit = 5, 
  showTimeframeSelector = true,
  className = ''
}) => {
  const [userData, setUserData] = useState<TopVotedUserEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState<TimeframeType>('week');
  
  // Add caching with proper typing
  const [cachedUsers, setCachedUsers] = useLocalStorage<Record<string, TopVotedUserEntry[]>>('topVotedUsersCache', {});
  const [cacheTimestamps, setCacheTimestamps] = useLocalStorage<Record<string, number>>('topVotedUsersCacheTimestamps', {});
  const [isUsingCache, setIsUsingCache] = useState(false);
  
  // Add refs for tracking component lifecycle
  const isMounted = useRef(true);
  const isFetching = useRef(false);
  const lastFetchTimes = useRef<Record<string, number>>({});
  
  // Cache expiry time - 30 minutes (increased from 15)
  const CACHE_EXPIRY_TIME = 30 * 60 * 1000;
  // Min time between refreshes - 5 minutes (increased from 2)
  const MIN_FETCH_INTERVAL = 5 * 60 * 1000;
  
  // Check if cache is valid
  const isCacheValid = useCallback((tf: TimeframeType) => {
    const cacheKey = `${tf}_${limit}`;
    const now = Date.now();
    return (
      cachedUsers[cacheKey] && 
      cachedUsers[cacheKey].length > 0 && 
      cacheTimestamps[cacheKey] && 
      now - cacheTimestamps[cacheKey] < CACHE_EXPIRY_TIME
    );
  }, [cachedUsers, cacheTimestamps, limit]);
  
  // Check if we should fetch new data
  const shouldFetchNewData = useCallback((tf: TimeframeType) => {
    const cacheKey = `${tf}_${limit}`;
    const now = Date.now();
    return !lastFetchTimes.current[cacheKey] || now - lastFetchTimes.current[cacheKey] > MIN_FETCH_INTERVAL;
  }, [limit]);
  
  // Fetch top voted users data
  const fetchTopVotedUsers = useCallback(async (selectedTimeframe: TimeframeType) => {
    const cacheKey = `${selectedTimeframe}_${limit}`;
    
    // Prevent concurrent fetches
    if (isFetching.current) {
      logInfo('TopVotedUsers: Fetch already in progress, skipping', { timeframe: selectedTimeframe });
      return;
    }
    
    isFetching.current = true;
    setLoading(true);
    setError(null);
    
    // Get the current retry count for this timeframe or initialize it
    const retryKey = `retry_${selectedTimeframe}`;
    const currentRetryCount = (lastFetchTimes.current[retryKey] || 0);
    
    // Check if max retries reached
    if (currentRetryCount >= MAX_RETRY_COUNT) {
      logInfo('TopVotedUsers: Max retry count reached, not attempting further fetches', 
        { timeframe: selectedTimeframe, retryCount: currentRetryCount }
      );
      isFetching.current = false;
      setLoading(false);
      
      // Use cache if available
      if (isCacheValid(selectedTimeframe)) {
        setUserData(cachedUsers[cacheKey]);
        setIsUsingCache(true);
      } else {
        setError('Unable to load the latest data after multiple attempts.');
      }
      
      return;
    }
    
    try {
      logInfo('TopVotedUsers: Fetching data', { timeframe: selectedTimeframe, limit, retryCount: currentRetryCount });
      
      const response = await getTopVotedUsers(limit, selectedTimeframe);
      
      if (isMounted.current) {
        if (response.success && Array.isArray(response.data)) {
          // Process the data to include ranks
          const processedData = response.data.map((user, index) => ({
            id: user.userId,
            username: user.username,
            displayName: user.displayName || user.username,
            avatarUrl: user.avatarUrl,
            voteCount: user.totalVotes || 0,
            rank: index + 1,
            totalVotes: user.totalVotes,
            suggestionCount: user.suggestionCount,
            approvedSuggestions: user.approvedSuggestions,
            averageVotesPerSuggestion: user.averageVotesPerSuggestion
          }));
          
          setUserData(processedData);
          
          // Update cache
          setCachedUsers(prev => ({ ...prev, [cacheKey]: processedData }));
          setCacheTimestamps(prev => ({ ...prev, [cacheKey]: Date.now() }));
          
          // Update last fetch time
          lastFetchTimes.current[cacheKey] = Date.now();
          // Reset retry counter on success
          lastFetchTimes.current[retryKey] = 0;
          
          logInfo('TopVotedUsers: Data fetched successfully', { 
            timeframe: selectedTimeframe, 
            count: processedData.length 
          });
        } else {
          // Increment retry counter on failure
          lastFetchTimes.current[retryKey] = currentRetryCount + 1;
          throw new Error(response.message || 'Failed to fetch top voted users');
        }
      }
    } catch (error) {
      // Increment retry counter on error
      lastFetchTimes.current[retryKey] = currentRetryCount + 1;
      
      logError('TopVotedUsers: Error fetching data', { 
        error, 
        timeframe: selectedTimeframe,
        retryCount: lastFetchTimes.current[retryKey]
      });
      
      if (isMounted.current) {
        setError('Could not load top voted users');
        
        // Try to use cache as fallback
        if (isCacheValid(selectedTimeframe)) {
          setUserData(cachedUsers[cacheKey]);
          setIsUsingCache(true);
          logInfo('TopVotedUsers: Using cached data after error', { timeframe: selectedTimeframe });
        }
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
        isFetching.current = false;
      }
    }
  }, [limit, cachedUsers, setCachedUsers, setCacheTimestamps, isCacheValid]);
  
  // Handle initial load and timeframe changes
  useEffect(() => {
    // Define a function to load the data
    const loadData = async () => {
      if (isMounted.current && !isFetching.current) {
        const cacheKey = `${timeframe}_${limit}`;
        
        // Try to use cache first for instant display
        if (isCacheValid(timeframe)) {
          setUserData(cachedUsers[cacheKey]);
          setIsUsingCache(true);
          setLoading(false);
        } else {
          setLoading(true);
        }
        
        // Check if we should attempt to fetch fresh data
        const shouldFetch = !isCacheValid(timeframe) || shouldFetchNewData(timeframe);
        
        // Check retry count
        const retryKey = `retry_${timeframe}`;
        const currentRetryCount = lastFetchTimes.current[retryKey] || 0;
        
        // Only fetch if we need fresh data AND we haven't exceeded retry limit
        if (shouldFetch && currentRetryCount < MAX_RETRY_COUNT) {
          try {
            await fetchTopVotedUsers(timeframe);
          } catch (err) {
            logError('TopVotedUsers: Failed to load data', { error: err });
          }
        }
      }
    };
    
    // Call the function
    loadData();
    
    // Important: Completely simplified dependency array to prevent loops
    // Only re-run when timeframe changes (user selection) or limit changes
  }, [timeframe, limit]);
  
  // Add a separate effect to handle component cleanup
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);
  
  const handleTimeframeChange = (tf: TimeframeType) => {
    setTimeframe(tf);
  };
  
  const getRankColor = (rank: number): string => {
    if (rank === 1) return 'text-yellow-500';
    if (rank === 2) return 'text-gray-400';
    if (rank === 3) return 'text-amber-600';
    return 'text-gray-600';
  };
  
  // Show loading state
  if (loading && !isUsingCache) {
    return (
      <div className={`bg-white rounded-lg shadow-sm p-4 ${className}`}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-800 flex items-center">
            <TrophyIcon className="h-5 w-5 text-yellow-500 mr-2" />
            Top Voted Users
          </h3>
          
          {showTimeframeSelector && (
            <div className="flex space-x-2 text-xs">
              <button 
                onClick={() => handleTimeframeChange('week')}
                className={`px-2 py-1 rounded ${timeframe === 'week' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'}`}
              >
                Week
              </button>
              <button 
                onClick={() => handleTimeframeChange('month')}
                className={`px-2 py-1 rounded ${timeframe === 'month' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'}`}
              >
                Month
              </button>
              <button 
                onClick={() => handleTimeframeChange('alltime')}
                className={`px-2 py-1 rounded ${timeframe === 'alltime' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'}`}
              >
                All Time
              </button>
            </div>
          )}
        </div>
        
        <div className="space-y-2 animate-pulse">
          {Array(limit).fill(0).map((_, i) => (
            <div key={i} className="flex items-center p-2 bg-gray-50 rounded">
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="ml-3 flex-1">
                <div className="h-4 bg-gray-200 rounded w-24"></div>
                <div className="h-3 bg-gray-200 rounded w-16 mt-1"></div>
              </div>
              <div className="h-6 w-6 bg-gray-200 rounded-full"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }
  
  // Show error state with cached data fallback
  if (error && !isUsingCache) {
    return (
      <div className={`bg-white rounded-lg shadow-sm p-4 ${className}`}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-800 flex items-center">
            <TrophyIcon className="h-5 w-5 text-yellow-500 mr-2" />
            Top Voted Users
          </h3>
        </div>
        
        <div className="text-center py-4">
          <p className="text-red-500 mb-3">{error}</p>
          <button
            onClick={() => fetchTopVotedUsers(timeframe)}
            className="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-md transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`bg-white rounded-lg shadow-sm p-4 ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center">
          <TrophyIcon className="h-5 w-5 text-yellow-500 mr-2" />
          Top Voted Users
        </h3>
        
        {showTimeframeSelector && (
          <div className="flex space-x-2 text-xs">
            <button 
              onClick={() => handleTimeframeChange('week')}
              className={`px-2 py-1 rounded ${timeframe === 'week' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'}`}
            >
              Week
            </button>
            <button 
              onClick={() => handleTimeframeChange('month')}
              className={`px-2 py-1 rounded ${timeframe === 'month' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'}`}
            >
              Month
            </button>
            <button 
              onClick={() => handleTimeframeChange('alltime')}
              className={`px-2 py-1 rounded ${timeframe === 'alltime' ? 'bg-primary-500 text-white' : 'bg-gray-100 text-gray-600'}`}
            >
              All Time
            </button>
          </div>
        )}
      </div>
      
      {isUsingCache && (
        <div className="mb-2 text-xs text-gray-500 flex items-center justify-end">
          <span className="italic">Using cached data</span>
          <button
            onClick={() => fetchTopVotedUsers(timeframe)}
            className="ml-2 text-primary-500 hover:text-primary-700"
            title="Refresh data"
          >
            <SparklesIcon className="h-4 w-4" />
          </button>
        </div>
      )}
      
      {userData.length === 0 ? (
        <div className="text-center py-4 text-gray-500">
          No users found.
        </div>
      ) : (
        <div className="space-y-2">
          {userData.map((user) => (
            user.id ? (
              <Link 
                key={user.id} 
                to={`/user/${user.id}`}
                className="flex items-center p-2 bg-gray-50 hover:bg-gray-100 rounded transition-colors"
              >
                <div className={`w-7 h-7 flex-shrink-0 flex items-center justify-center ${getRankColor(user.rank)} font-bold rounded-full border`}>
                  {user.rank}
                </div>
                
                <div className="ml-3 flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-800 hover:text-primary-600">
                    {user.displayName || user.username}
                  </div>
                  <div className="text-xs text-gray-500 truncate">
                    @{user.username}
                  </div>
                  <div className="flex gap-2 mt-1 text-xs text-gray-600">
                    {user.totalVotes !== undefined && (
                      <span className="flex items-center">
                        <FireIcon className="h-3 w-3 mr-1" />
                        {user.totalVotes} votes
                      </span>
                    )}
                    {user.suggestionCount !== undefined && (
                      <span className="flex items-center">
                        <ChartBarIcon className="h-3 w-3 mr-1" />
                        {user.suggestionCount} songs
                      </span>
                    )}
                    {user.averageVotesPerSuggestion && (
                      <span className="flex items-center">
                        <SparklesIcon className="h-3 w-3 mr-1" />
                        {user.averageVotesPerSuggestion} avg
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center text-primary-600">
                  <FireIcon className="h-4 w-4 mr-1" />
                  <span className="text-sm font-medium">{user.voteCount}</span>
                </div>
              </Link>
            ) : (
              <div 
                key={`user-${user.rank}`} 
                className="flex items-center p-2 bg-gray-50 rounded transition-colors"
              >
                <div className={`w-7 h-7 flex-shrink-0 flex items-center justify-center ${getRankColor(user.rank)} font-bold rounded-full border`}>
                  {user.rank}
                </div>
                
                <div className="ml-3 flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-800">
                    {user.displayName || user.username}
                  </div>
                  <div className="text-xs text-gray-500 truncate">
                    @{user.username}
                  </div>
                  <div className="flex gap-2 mt-1 text-xs text-gray-600">
                    {user.totalVotes !== undefined && (
                      <span className="flex items-center">
                        <FireIcon className="h-3 w-3 mr-1" />
                        {user.totalVotes} votes
                      </span>
                    )}
                    {user.suggestionCount !== undefined && (
                      <span className="flex items-center">
                        <ChartBarIcon className="h-3 w-3 mr-1" />
                        {user.suggestionCount} songs
                      </span>
                    )}
                    {user.averageVotesPerSuggestion && (
                      <span className="flex items-center">
                        <SparklesIcon className="h-3 w-3 mr-1" />
                        {user.averageVotesPerSuggestion} avg
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center text-primary-600">
                  <FireIcon className="h-4 w-4 mr-1" />
                  <span className="text-sm font-medium">{user.voteCount}</span>
                </div>
              </div>
            )
          ))}
        </div>
      )}
    </div>
  );
};

export default TopVotedUsers; 