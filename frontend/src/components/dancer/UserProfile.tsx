import React, { useState, useEffect, FC, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { getMyFavoriteDancers, removeFavoriteDancer } from '../../utils/api';
import { logInfo, logError } from '../../utils/logger';
import { 
  ChevronRightIcon, 
  UserMinusIcon,
  UsersIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';
import { cn } from "../../utils/cn";

// Import Tooltip only if it's used somewhere in the component
// import { Tooltip } from '../ui/Tooltip';

export interface DisplayProfileData {
  displayName: string | null;
  avatarUrl: string | null;
  // Add other profile fields if needed
}

interface FavoriteDancerDisplayItem {
  id: string;
  username: string;
  displayName: string | null;
  avatarUrl: string | null;
}

interface UserProfileProps {
  userInfo: any; 
  displayProfile: DisplayProfileData | null;
  onAvatarUpdate: () => void;
  isOwnProfile?: boolean; 
  targetUserId?: string;
  className?: string;
}

const UserProfile: FC<UserProfileProps> = ({ 
  userInfo, 
  displayProfile, 
  onAvatarUpdate, 
  isOwnProfile = true, 
  targetUserId,
  className
}) => {
  const [favoriteDancers, setFavoriteDancers] = useState<FavoriteDancerDisplayItem[]>([]);
  const [loadingFavorites, setLoadingFavorites] = useState(false);

  const fetchFavoriteDancersList = useCallback(async () => {
    if (!isOwnProfile || !userInfo) return; // Only for current user's profile
    setLoadingFavorites(true);
    try {
      const result = await getMyFavoriteDancers();
      if (result.success && Array.isArray(result.data)) {
        setFavoriteDancers(result.data);
        logInfo('UserProfile: Fetched favorite dancers list', { userId: userInfo.id, count: result.data.length });
      } else {
        logError('UserProfile: Failed to fetch favorite dancers', { userId: userInfo.id, message: result.message });
        setFavoriteDancers([]);
      }
    } catch (err) {
      logError('UserProfile: Error fetching favorite dancers', { userId: userInfo.id, error: err });
      setFavoriteDancers([]);
    } finally {
      setLoadingFavorites(false);
    }
  }, [isOwnProfile, userInfo]);

  useEffect(() => {
    if (isOwnProfile) {
      fetchFavoriteDancersList();
    }
  }, [isOwnProfile, fetchFavoriteDancersList]);

  const handleUnfavoriteDancer = async (dancerIdToUnfavorite: string) => {
    if (!isOwnProfile) return;
    logInfo('UserProfile: Attempting to unfavorite dancer', { currentUserId: userInfo.id, dancerIdToUnfavorite });
    const result = await removeFavoriteDancer(dancerIdToUnfavorite);
    if (result.success) {
      toast.success('Dancer removed from favorites.');
      fetchFavoriteDancersList(); // Refresh the list
    } else {
      toast.error(result.message || 'Failed to remove favorite.');
      logError('UserProfile: Failed to unfavorite dancer', { message: result.message });
    }
  };

  // Avatar, Name, Edit/Favorite Button section
  const renderUserProfileHeader = () => {
    return (
      <div className="text-center mb-3">
        {displayProfile?.avatarUrl && <img src={displayProfile.avatarUrl} alt="avatar" className="w-20 h-20 rounded-full mx-auto mb-2 border-2 border-gray-300 shadow-sm" />}
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">{displayProfile?.displayName || userInfo?.username}</h3>
        {/* Existing favorite button logic for non-own profiles would go here */} 
      </div>
    );
  };

  return (
    <div className={cn(
      "flex flex-col p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 space-y-4 transition-colors duration-150 ease-in-out",
      className
    )}>
      {renderUserProfileHeader()}

      {/* Favorite Dancers Section - Only show for own profile */}
      {isOwnProfile && (
        <div className="w-full">
          <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 text-center border-b pb-2 transition-colors duration-150 ease-in-out">
            <UsersIcon className="h-4 w-4 inline-block mr-1" />
            Favorite Dancers
          </h4>
          {loadingFavorites ? (
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center py-2 transition-colors duration-150 ease-in-out">
              Loading favorites...
            </p>
          ) : favoriteDancers.length > 0 ? (
            <div className="space-y-2">
              {favoriteDancers.map((dancer) => (
                <div 
                  key={dancer.id}
                  className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700/30 rounded-lg text-sm transition-colors duration-150 ease-in-out"
                >
                  <Link 
                    to={`/dancer/profile/${dancer.id}`}
                    className="flex items-center flex-1 min-w-0"
                  >
                    {dancer.avatarUrl ? (
                      <img 
                        src={dancer.avatarUrl} 
                        alt="avatar" 
                        className="w-6 h-6 rounded-full mr-2 border border-gray-200 dark:border-gray-600" 
                      />
                    ) : (
                      <div className="w-6 h-6 rounded-full mr-2 bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400 text-xs">
                        ?
                      </div>
                    )}
                    <span className="truncate text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-150 ease-in-out">
                      {dancer.displayName || dancer.username}
                    </span>
                  </Link>
                  <button
                    onClick={() => handleUnfavoriteDancer(dancer.id)}
                    className="ml-2 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 transition-colors duration-150 ease-in-out"
                    title="Remove from favorites"
                  >
                    <UserMinusIcon className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center py-2 transition-colors duration-150 ease-in-out">
              No favorite dancers yet.
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default UserProfile; 