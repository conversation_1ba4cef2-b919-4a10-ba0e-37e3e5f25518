import React, { useState, useEffect, FC, useRef, useCallback } from 'react';
import { getLeaderboard } from '../../utils/api';
import { logInfo, logError } from '../../utils/logger';
import toast from 'react-hot-toast';
import { TrophyIcon, UserCircleIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';
import { useLocalStorage } from '../../hooks/useLocalStorage';

interface LeaderboardEntry {
  userId: string;
  username: string;
  displayName: string | null;
  avatarUrl: string | null;
  score: number; // Represents approved suggestions count
}

const Leaderboard: FC = () => {
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUsingCache, setIsUsingCache] = useState(false);
  const [cachedLeaderboard, setCachedLeaderboard] = useLocalStorage<LeaderboardEntry[]>('cachedLeaderboard', []);
  const [cacheTimestamp, setCacheTimestamp] = useLocalStorage<number>('leaderboardCacheTimestamp', 0);
  
  // References to track component state
  const isMounted = useRef(true);
  const isFetching = useRef(false);
  const lastFetchTime = useRef<number>(0);
  
  // Cache validity - 30 minutes
  const CACHE_EXPIRY_TIME = 30 * 60 * 1000;
  // Minimum time between fetches - 5 minutes
  const MIN_FETCH_INTERVAL = 5 * 60 * 1000;
  
  // Check if cache is valid
  const isCacheValid = useCallback(() => {
    const now = Date.now();
    return (
      cachedLeaderboard.length > 0 && 
      cacheTimestamp > 0 && 
      now - cacheTimestamp < CACHE_EXPIRY_TIME
    );
  }, [cachedLeaderboard, cacheTimestamp]);
  
  // Check if we should fetch new data
  const shouldFetchNewData = useCallback(() => {
    const now = Date.now();
    return !lastFetchTime.current || now - lastFetchTime.current > MIN_FETCH_INTERVAL;
  }, []);

  const fetchLeaderboard = useCallback(async () => {
    // Prevent concurrent fetches
    if (isFetching.current) {
      logInfo('Leaderboard: Fetch already in progress, skipping');
      return;
    }
    
    isFetching.current = true;
    setLoading(true);
    setError(null);
    setIsUsingCache(false);
    logInfo('Leaderboard: Fetching data...');
    
    try {
      const result = await getLeaderboard(10); // Fetch top 10
      if (result.success && Array.isArray(result.data)) {
        if (isMounted.current) {
          setLeaderboardData(result.data);
          setCachedLeaderboard(result.data);
          setCacheTimestamp(Date.now());
          lastFetchTime.current = Date.now();
          logInfo('Leaderboard: Data fetched successfully', { count: result.data.length });
        }
      } else {
        logError('Leaderboard: Failed to fetch data', { message: result.message });
        
        if (isCacheValid() && isMounted.current) {
          setLeaderboardData(cachedLeaderboard);
          setIsUsingCache(true);
          logInfo('Leaderboard: Using cached data due to API error');
        } else if (isMounted.current) {
          setError(result.message || 'Could not load the leaderboard.');
          toast.error('Failed to load leaderboard.');
          setLeaderboardData([]);
        }
      }
    } catch (error: any) {
      logError('Leaderboard: Exception while fetching data', { error: error.message });
      
      if (isCacheValid() && isMounted.current) {
        setLeaderboardData(cachedLeaderboard);
        setIsUsingCache(true);
        logInfo('Leaderboard: Using cached data due to exception');
      } else if (isMounted.current) {
        setError('Could not connect to the server. Please try again later.');
        toast.error('Failed to load leaderboard.');
        setLeaderboardData([]);
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
        isFetching.current = false;
      }
    }
  }, [cachedLeaderboard, setCachedLeaderboard, setCacheTimestamp, isCacheValid]);

  // Set up cleanup for component unmount
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Fetch leaderboard data with caching
  useEffect(() => {
    const fetchData = async () => {
      if (isFetching.current) return;
      
      // Check for valid cached data first
      if (isCacheValid()) {
        logInfo('Leaderboard: Using cached data on initial load');
        setLeaderboardData(cachedLeaderboard);
        setIsUsingCache(true);
        setLoading(false);
        
        // Only fetch fresh data in the background if needed
        if (shouldFetchNewData()) {
          logInfo('Leaderboard: Background refresh after using cache');
          fetchLeaderboard();
        }
      } else {
        // No valid cache, we need to fetch
        fetchLeaderboard();
      }
    };
    
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once on mount

  const getRankColor = (rank: number): string => {
    switch (rank) {
      case 0: return 'text-yellow-500 bg-yellow-100'; // Gold
      case 1: return 'text-gray-500 bg-gray-100'; // Silver
      case 2: return 'text-orange-600 bg-orange-100'; // Bronze
      default: return 'text-gray-500 bg-gray-50';
    }
  };

  return (
    <div className="mt-6">
      <div className="flex items-center gap-2 mb-4 bg-gradient-to-r from-yellow-50 to-orange-50 p-3 rounded-lg shadow-sm">
         <TrophyIcon className="h-7 w-7 text-yellow-500" />
         <h3 className="text-lg font-bold text-gray-800 tracking-tight">Top Suggesters</h3>
         <SparklesIcon className="h-5 w-5 text-amber-400 ml-auto animate-pulse" />
      </div>

      {loading && !isUsingCache && <div className="text-center py-6 text-gray-500">
        <div className="animate-pulse flex justify-center">
          <div className="h-4 w-4 bg-gray-300 rounded-full mr-1"></div>
          <div className="h-4 w-4 bg-gray-400 rounded-full mr-1"></div>
          <div className="h-4 w-4 bg-gray-300 rounded-full"></div>
        </div>
        <p className="mt-2">Loading leaderboard...</p>
      </div>}
      
      {error && <div className="text-center py-4 text-red-600 bg-red-50 p-4 rounded-md border border-red-100">Error: {error}</div>}
      
      {!loading && !error && leaderboardData.length === 0 && (
        <div className="text-center py-6 text-gray-600 bg-gray-50 p-4 rounded-lg border border-gray-100 shadow-inner">
          <p>Leaderboard is empty.</p>
          <p className="text-sm mt-1 text-gray-500">Be the first to get a suggestion approved!</p>
        </div>
      )}
      
      {!error && leaderboardData.length > 0 && (
        <>
          <ol className="space-y-3">
            {leaderboardData.slice(0, 5).map((entry, index) => (
              <li key={entry.userId} 
                  className={`bg-gradient-to-r ${index === 0 ? 'from-yellow-50 to-amber-50 border-yellow-200' : 
                               index === 1 ? 'from-gray-50 to-slate-50 border-gray-200' : 
                               index === 2 ? 'from-orange-50 to-amber-50 border-orange-200' : 
                               'from-white to-gray-50 border-gray-200'} 
                             p-4 rounded-lg border shadow-sm hover:shadow-md transition-all duration-200 flex items-center space-x-3`}>
                <span className={`flex-shrink-0 w-9 h-9 rounded-full flex items-center justify-center text-sm font-bold ${getRankColor(index)} shadow-inner`}>
                  {index + 1}
                </span>
                <div className="relative">
                  <img
                    src={entry.avatarUrl || '/img/default-avatar.png'}
                    alt={entry.displayName || entry.username}
                    className={`flex-shrink-0 h-12 w-12 rounded-full object-cover border-2 ${index === 0 ? 'border-yellow-300' : 
                               index === 1 ? 'border-gray-300' : 
                               index === 2 ? 'border-orange-300' : 'border-gray-200'} shadow-sm`}
                    onError={(e) => { (e.target as HTMLImageElement).src = '/img/default-avatar.png'; }}
                  />
                  {index < 3 && (
                    <span className="absolute -top-1 -right-1 flex h-4 w-4">
                      <span className={`animate-ping absolute inline-flex h-full w-full rounded-full ${
                        index === 0 ? 'bg-yellow-400' : index === 1 ? 'bg-gray-400' : 'bg-orange-400'
                      } opacity-75`}></span>
                      <span className={`relative inline-flex rounded-full h-4 w-4 ${
                        index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-500' : 'bg-orange-500'
                      }`}></span>
                    </span>
                  )}
                </div>
                <div className="flex-grow min-w-0 mr-2">
                  {entry.userId ? (
                    <Link 
                      to={`/user/${entry.userId}`} 
                      className="text-sm font-medium text-gray-800 hover:text-primary-600 hover:underline overflow-visible block break-words"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {entry.displayName || entry.username}
                      {index === 0 && <span className="text-yellow-500 text-xs ml-1">👑</span>}
                    </Link>
                  ) : (
                    <span className="text-sm font-medium text-gray-800 overflow-visible block break-words">
                      {entry.displayName || entry.username}
                      {index === 0 && <span className="text-yellow-500 text-xs ml-1">👑</span>}
                    </span>
                  )}
                  <p className="text-xs text-gray-500 mt-1">{entry.score} approved suggestion{entry.score !== 1 ? 's' : ''}</p>
                </div>
                <div className={`flex-shrink-0 rounded-full w-8 h-8 ml-auto flex items-center justify-center ${
                  index === 0 ? 'bg-yellow-100' : index === 1 ? 'bg-gray-100' : index === 2 ? 'bg-orange-100' : 'bg-gray-50'
                }`}>
                  <span className="text-xs font-bold">{entry.score}</span>
                </div>
              </li>
            ))}
          </ol>
          {leaderboardData.length > 0 && (
            <div className="mt-4 text-center">
              <Link
                to="/leaderboard/all"
                className="text-sm font-medium text-primary-600 hover:text-primary-700 hover:underline"
              >
                See All Top Suggesters
              </Link>
            </div>
          )}
        </>
      )}
      
      {isUsingCache && (
        <div className="text-center mt-2">
          <button 
            onClick={() => fetchLeaderboard()}
            className="text-xs text-primary-600 hover:text-primary-800 flex items-center justify-center mx-auto"
            disabled={isFetching.current}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
        </div>
      )}
    </div>
  );
};

export default Leaderboard; 