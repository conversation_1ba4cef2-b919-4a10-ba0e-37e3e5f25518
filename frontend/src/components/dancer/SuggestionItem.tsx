import React from 'react';
import { HandThumbUpIcon } from '@heroicons/react/24/outline';
import { HandThumbUpIcon as HandThumbUpSolidIcon } from '@heroicons/react/24/solid';
import { FaSpinner } from 'react-icons/fa';
import { logInfo } from '../../utils/logger';
import { Link } from 'react-router-dom';
import { VotersList } from '../ui/VotersList';

// Interface matching the one used in VoteSuggestions
interface Suggestion {
  id: string;
  user?: { id: string; username: string };
  youtubeVideoId: string;
  title: string;
  channelTitle?: string;
  thumbnailUrl?: string;
  danceStyle: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  votes: number;
  isLocked?: boolean;
  createdAt: string;
  updatedAt?: string;
  hasVoted: boolean;
  durationSeconds?: number | null;
  voters: Array<{
    id: string;
    username: string;
    profile?: {
      displayName?: string;
      avatarUrl?: string;
    };
  }>;
}

interface SuggestionItemProps {
  suggestion: Suggestion;
  onVote: (suggestionId: string) => Promise<void>;
  onUnvote: (suggestionId: string) => Promise<void>;
  currentUserId: string | null;
  voteLoading: Record<string, boolean>;
  voters?: Array<{
    id: string;
    username: string;
    profile?: {
      displayName?: string;
      avatarUrl?: string;
    };
  }>;
}

// Workaround for TS2786: Assert FaSpinner as React.ElementType
const FaSpinnerIcon = FaSpinner as React.ElementType;

// Helper to format relative time
const timeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const secondsPast = (now.getTime() - date.getTime()) / 1000;

  if (secondsPast < 60) return `${Math.floor(secondsPast)}s ago`;
  if (secondsPast < 3600) return `${Math.floor(secondsPast / 60)}m ago`;
  if (secondsPast <= 86400) return `${Math.floor(secondsPast / 3600)}h ago`;
  const days = Math.floor(secondsPast / 86400);
  if (days < 7) return `${days}d ago`;

  // For older dates, show the actual date
  const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric' };
  return date.toLocaleDateString(undefined, options);
};

// Helper to format duration
const formatDuration = (seconds: number | null | undefined): string => {
  if (seconds === null || seconds === undefined || seconds <= 0) return '--:--';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const SuggestionItem: React.FC<SuggestionItemProps> = ({
  suggestion,
  onVote,
  onUnvote,
  currentUserId,
  voteLoading,
  voters
}) => {
  const {
    id,
    title,
    youtubeVideoId,
    thumbnailUrl,
    danceStyle,
    votes,
    hasVoted,
    createdAt,
    user,
    durationSeconds
  } = suggestion;

  const isVoteLoading = voteLoading[id] || false;
  const canInteract = currentUserId && currentUserId !== user?.id;

  const handleVoteClick = () => {
    if (!canInteract || isVoteLoading) return;
    logInfo('SuggestionItem: Vote button clicked', { suggestionId: id, currentVoteStatus: hasVoted });
    if (hasVoted) {
      onUnvote(id);
    } else {
      onVote(id);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow">
      <div className="flex p-3">
        {/* Thumbnail */}
        <a
          href={`https://www.youtube.com/watch?v=${youtubeVideoId}`}
          target="_blank"
          rel="noopener noreferrer"
          className="flex-shrink-0"
        >
          <img
            src={thumbnailUrl || '/img/placeholder-youtube.png'}
            alt={title}
            className="w-20 h-20 object-cover rounded-md"
            onError={(e) => { (e.target as HTMLImageElement).src = '/img/placeholder-youtube.png'; }}
          />
        </a>

        {/* Content */}
        <div className="ml-3 flex-grow">
          <div className="flex flex-col items-stretch sm:flex-row sm:justify-between sm:items-start">
            <div>
              <a
                href={`https://www.youtube.com/watch?v=${youtubeVideoId}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm font-medium text-blue-600 hover:underline break-words"
              >
                {title}
              </a>
              <div className="flex flex-wrap items-center mt-1 text-xs text-gray-500">
                <span className="bg-purple-100 text-purple-800 rounded px-2 py-0.5 mr-2 mb-1 sm:mb-0">
                  {danceStyle}
                </span>
                <span className="mr-1 mb-1 sm:mb-0">{formatDuration(durationSeconds)}</span>
                <span className="mx-1 mb-1 sm:mb-0">•</span>
                <span className="mr-1 mb-1 sm:mb-0">{timeAgo(createdAt)}</span>
                <span className="mx-1 mb-1 sm:mb-0">•</span>
                <span className="mb-1 sm:mb-0">By {user?.id ? (
                  <Link
                    to={`/user/${user.id}`}
                    className="text-primary-500 hover:text-primary-700 hover:underline"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {user.username || 'Unknown'}
                  </Link>
                ) : (
                  user?.username || 'Unknown'
                )}</span>
              </div>
            </div>

            {/* Vote Button */}
            <div className="flex items-center gap-2 mt-2 sm:mt-0 sm:ml-2 self-end sm:self-auto">
              <button
                onClick={handleVoteClick}
                disabled={!canInteract || isVoteLoading}
                className={`flex items-center justify-center rounded-full p-2 transition-colors duration-150 ${hasVoted
                    ? 'text-primary-600 bg-primary-50 hover:bg-primary-100'
                    : canInteract
                      ? 'text-gray-600 bg-gray-100 hover:bg-gray-200'
                      : 'text-gray-400 bg-gray-50 cursor-not-allowed'
                  }`}
                title={
                  hasVoted
                    ? 'Remove your vote'
                    : !currentUserId
                      ? 'Log in to vote'
                      : currentUserId === user?.id
                        ? "You can't vote on your own suggestion"
                        : 'Vote for this song'
                }
              >
                {isVoteLoading ? (
                  <FaSpinnerIcon className="animate-spin h-5 w-5" />
                ) : hasVoted ? (
                  <HandThumbUpSolidIcon className="h-5 w-5" />
                ) : (
                  <HandThumbUpIcon className="h-5 w-5" />
                )}
                <span className="ml-1 text-sm font-medium">{votes}</span>
              </button>

              {/* Voters List Button */}
              {suggestion.voters && suggestion.voters.length > 0 && (
                <VotersList
                  voters={suggestion.voters}
                  totalVotes={suggestion.votes}
                  compact={true}
                  className="ml-2"
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuggestionItem; 