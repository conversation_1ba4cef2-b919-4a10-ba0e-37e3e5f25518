import React, { useState, useEffect, FC, useCallback } from 'react';
import { getSuggestionHistory, getDanceStyles } from '../../utils/api';
import { logInfo, logError } from '../../utils/logger';
import toast from 'react-hot-toast';
import { ClockIcon, TagIcon, CheckCircleIcon, XCircleIcon, QuestionMarkCircleIcon, ArrowPathIcon, LinkIcon, AdjustmentsHorizontalIcon, ChevronLeftIcon, ChevronRightIcon, FunnelIcon } from '@heroicons/react/24/outline';
import { useWebSocket } from '../../context/WebSocketContext';
import { VotersList } from '../ui/VotersList';
import { useAuthContext } from '../../context/AuthContext';

interface SuggestionHistoryItem {
  id: string;
  title: string;
  danceStyle: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED'; // Add PLAYED later if needed
  createdAt: string;
  thumbnailUrl?: string;
  durationSeconds?: number | null;
  youtubeVideoId: string;
  votes: number;
  voters: Array<{
    id: string;
    username: string;
    profile?: {
      displayName?: string;
      avatarUrl?: string;
    };
  }>;
  user: { id: string; username: string };
}

// Define a type for pagination
interface Pagination {
  page: number;
  limit: number;
  totalItems: number;
  totalPages: number;
}

interface SuggestionHistoryProps {
  className?: string;
  userId?: string; // Optional userId to view history for a specific user
  limit?: number; // Optional limit for number of items per page
}

// Define a proper formatDate function
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  }).format(date);
};

// Format duration helper
const formatDuration = (seconds: number | null | undefined): string => {
  if (!seconds) return '?:??';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const SuggestionHistory: FC<SuggestionHistoryProps> = ({ className = '', userId, limit = 10 }) => {
  const [suggestions, setSuggestions] = useState<SuggestionHistoryItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const { socket, isConnected } = useWebSocket();
  const { user } = useAuthContext();
  
  // Pagination state
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit,
    totalItems: 0,
    totalPages: 0
  });
  
  // Add state for dance styles filter
  const [danceStyles, setDanceStyles] = useState<string[]>([]);
  const [selectedStyle, setSelectedStyle] = useState<string | null>(null);
  const [filterOpen, setFilterOpen] = useState<boolean>(false);

  // Load dance styles on component mount
  useEffect(() => {
    const loadDanceStyles = async () => {
      try {
        const res = await getDanceStyles();
        if (res.success && Array.isArray(res.data)) {
          setDanceStyles(res.data);
          logInfo('SuggestionHistory: Dance styles loaded successfully', { count: res.data.length });
        }
      } catch (err) {
        logError('SuggestionHistory: Error loading dance styles', err);
      }
    };
    
    loadDanceStyles();
  }, []);

  const fetchSuggestionHistory = useCallback(async (page = 1, styleFilter: string | null = null) => {
    // Check if the user is logged in
    if (!user) {
      setLoading(false);
      setError('Please log in to view your suggestion history');
      return;
    }

    setLoading(true);
    setError(null);
    setRefreshing(true);
    
    try {
      logInfo('SuggestionHistory: Fetching history', { page, styleFilter });
      const response = await getSuggestionHistory(page, pagination.limit, styleFilter || undefined);
      
      if (response.success && response.data) {
        // Use the appropriate response structure
        const responseData = Array.isArray(response.data) ? response.data : response.data;
        
        // Map the response pagination to our component's pagination interface
        const paginationData: Pagination = {
          page: response.pagination?.currentPage || page,
          limit: response.pagination?.limit || pagination.limit,
          totalItems: response.pagination?.totalCount || responseData.length,
          totalPages: response.pagination?.totalPages || 1
        };
        
        setSuggestions(responseData);
        setPagination(paginationData);
        logInfo('SuggestionHistory: History fetched successfully', { 
          count: responseData.length, 
          page: paginationData.page, 
          totalPages: paginationData.totalPages 
        });
      } else {
        setError('Failed to load suggestion history');
        logError('SuggestionHistory: Failed to fetch history', { message: response.message });
      }
    } catch (err: any) {
      setError(`Error: ${err.message || 'Unknown error'}`);
      logError('SuggestionHistory: Error fetching history', err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [pagination.limit, user]);

  useEffect(() => {
    // Only fetch if the user is logged in
    if (user) {
      fetchSuggestionHistory(pagination.page, selectedStyle);
    } else {
      setLoading(false);
    }
    // Simplified dependency array - only fetch when these critical values change
    // Don't include the fetchSuggestionHistory function, as it creates a dependency cycle
  }, [pagination.page, selectedStyle, user]);

  // Handle WebSocket events to refresh suggestion history when needed
  useEffect(() => {
    if (socket && isConnected) {
      const handleSuggestionUpdated = () => {
        logInfo('SuggestionHistory: Suggestion updated, refreshing history');
        fetchSuggestionHistory(pagination.page, selectedStyle);
      };

      socket.on('suggestion:updated', handleSuggestionUpdated);

      return () => {
        socket.off('suggestion:updated', handleSuggestionUpdated);
      };
    }
  }, [socket, isConnected, fetchSuggestionHistory, pagination.page, selectedStyle]);

  const handleRefresh = () => {
    if (!refreshing) {
      fetchSuggestionHistory(pagination.page, selectedStyle);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      setPagination(prev => ({ ...prev, page: newPage }));
    }
  };

  const handleDanceStyleChange = (style: string | null) => {
    setSelectedStyle(style);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page when changing filter
  };

  // Renders status badge with appropriate icon and color
  const renderStatusBadge = (status: string) => {
    let badgeClass = '';
    let Icon: any = QuestionMarkCircleIcon;
    let statusText = status.charAt(0) + status.slice(1).toLowerCase();

    switch (status) {
      case 'PENDING':
        badgeClass = 'bg-yellow-100 text-yellow-800 border-yellow-200';
        Icon = ClockIcon;
        break;
      case 'APPROVED':
        badgeClass = 'bg-green-100 text-green-800 border-green-200';
        Icon = CheckCircleIcon;
        break;
      case 'REJECTED':
        badgeClass = 'bg-red-100 text-red-800 border-red-200';
        Icon = XCircleIcon;
        break;
      default:
        badgeClass = 'bg-gray-100 text-gray-800 border-gray-200';
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${badgeClass}`}>
        <Icon className="w-3.5 h-3.5 mr-1" aria-hidden="true" />
        {statusText}
      </span>
    );
  };

  // Override the render function to show login message if not logged in
  if (!user) {
    return (
      <div className={`my-6 p-5 bg-white rounded-xl shadow ${className}`}>
        <div className="text-center p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Suggestion History</h3>
          <p className="text-gray-600 mb-4">Please log in to view your suggestion history.</p>
          <a href="/login" className="inline-flex items-center px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors">
            Log in
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white shadow-sm rounded-lg p-4 ${className}`}>
      {/* Header with filter and refresh button */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-3">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center">
          <ClockIcon className="h-5 w-5 mr-2 text-primary-500" />
          Your Suggestion History
        </h3>
        
        <div className="flex items-center space-x-2">
          {/* Dance style filter dropdown */}
          <div className="relative">
            <button 
              onClick={() => setFilterOpen(!filterOpen)}
              className="flex items-center px-3 py-1.5 text-sm bg-white text-gray-700 border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <FunnelIcon className="h-4 w-4 mr-1" />
              {selectedStyle || 'All Styles'}
              <ChevronLeftIcon className={`h-4 w-4 ml-1 transition-transform duration-200 ${filterOpen ? 'rotate-90' : '-rotate-90'}`} />
            </button>
            
            {filterOpen && (
              <div className="absolute right-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <ul className="py-1 max-h-60 overflow-auto">
                  <li>
                    <button
                      onClick={() => {
                        handleDanceStyleChange(null);
                        setFilterOpen(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${!selectedStyle ? 'bg-gray-100 font-medium' : ''}`}
                    >
                      All Styles
                    </button>
                  </li>
                  {danceStyles.map(style => (
                    <li key={style}>
                      <button
                        onClick={() => {
                          handleDanceStyleChange(style);
                          setFilterOpen(false);
                        }}
                        className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${selectedStyle === style ? 'bg-gray-100 font-medium' : ''}`}
                      >
                        {style}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          
          {/* Refresh button */}
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className={`p-1.5 rounded-full text-gray-500 hover:bg-gray-100 focus:outline-none ${refreshing ? 'animate-spin' : ''}`}
            aria-label="Refresh history"
          >
            <ArrowPathIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">
          {error}
        </div>
      )}

      {/* Loading state */}
      {loading && !refreshing && (
        <div className="py-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-200 border-t-primary-500"></div>
          <p className="mt-2 text-gray-500">Loading suggestion history...</p>
        </div>
      )}

      {/* Empty state */}
      {!loading && suggestions.length === 0 && (
        <div className="py-8 text-center border border-dashed border-gray-300 rounded-lg">
          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3">
            <ClockIcon className="h-6 w-6 text-gray-400" />
          </div>
          <p className="text-gray-500">
            {selectedStyle 
              ? `No ${selectedStyle} suggestions found in your history.` 
              : 'You haven\'t made any suggestions yet.'}
          </p>
        </div>
      )}

      {/* Suggestions list */}
      {!loading && suggestions.length > 0 && (
        <>
          <ul className="divide-y divide-gray-200">
            {suggestions.map((suggestion) => (
              <li key={suggestion.id} className="py-4">
                <div className="flex flex-col sm:flex-row sm:items-start sm:space-x-4">
                  {/* Thumbnail */}
                  <a
                    href={`https://www.youtube.com/watch?v=${suggestion.youtubeVideoId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-shrink-0 group relative mb-2 sm:mb-0"
                  >
                    <img
                      src={suggestion.thumbnailUrl || '/img/placeholder-youtube.png'}
                      alt={suggestion.title}
                      className="w-full sm:w-20 h-20 object-cover rounded-md border border-gray-200 shadow-sm group-hover:shadow-md transition-shadow"
                      onError={(e) => { (e.target as HTMLImageElement).src = '/img/placeholder-youtube.png'; }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all rounded-md">
                      <LinkIcon className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                    </div>
                  </a>
                  
                  {/* Content */}
                  <div className="flex-grow min-w-0">
                    <a
                      href={`https://www.youtube.com/watch?v=${suggestion.youtubeVideoId}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-lg font-semibold text-primary-600 hover:text-primary-700 hover:underline leading-tight break-words mb-1 block"
                    >
                      {suggestion.title}
                    </a>
                    
                    <div className="flex flex-wrap mt-2 gap-2 items-center text-sm text-gray-600">
                      {/* Status badge */}
                      {renderStatusBadge(suggestion.status)}
                      
                      {/* Dance style badge */}
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 border border-indigo-200">
                        <TagIcon className="w-3.5 h-3.5 mr-1" aria-hidden="true" />
                        {suggestion.danceStyle}
                      </span>
                      
                      {/* Date */}
                      <span className="text-gray-500">
                        {formatDate(suggestion.createdAt)}
                      </span>
                      
                      {/* Duration if available */}
                      {suggestion.durationSeconds && (
                        <span className="text-gray-500 flex items-center">
                          <ClockIcon className="w-3.5 h-3.5 mr-1" />
                          {formatDuration(suggestion.durationSeconds)}
                        </span>
                      )}
                    </div>
                    
                    {/* Votes */}
                    <div className="mt-2 flex items-center flex-wrap gap-2">
                      <VotersList 
                        voters={suggestion.voters || []} 
                        totalVotes={suggestion.votes} 
                        hideCount={false} 
                      />
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
          
          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-center mt-6 space-x-2">
              <button 
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
                className="p-2 rounded border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeftIcon className="h-5 w-5" />
              </button>
              
              <div className="text-sm">
                Page <span className="font-medium">{pagination.page}</span> of <span className="font-medium">{pagination.totalPages}</span>
              </div>
              
              <button 
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages}
                className="p-2 rounded border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRightIcon className="h-5 w-5" />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default SuggestionHistory; 