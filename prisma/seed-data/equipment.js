const { PrismaClient } = require('@prisma/client');
const logger = require('../../backend/utils/logger');

const prisma = new PrismaClient();

// Comprehensive dance-themed equipment with 100 items
const equipmentItems = [
  // SHOES (20 items)
  { name: "Salsa Fire Shoes", description: "Ignite the dance floor with every step", type: "SHOES", rarity: "COMMON", danceStyle: "SALSA", statBonuses: { speed: 5, attack: 3 }, skillBonuses: { TIMING: 5 }, specialEffects: ["fire_trail"] },
  { name: "Bachata Soul Slippers", description: "Feel the rhythm in your soul", type: "SHOES", rarity: "RARE", danceStyle: "BACHATA", statBonuses: { charisma: 8, luck: 5 }, skillBonuses: { MUSICALITY: 10 }, specialEffects: ["soul_connection"] },
  { name: "Lightning Cha-Cha Sneakers", description: "Strike like lightning on the dance floor", type: "SHOES", rarity: "EPIC", danceStyle: "CHACHA", statBonuses: { speed: 15, attack: 8 }, skillBonuses: { FOOTWORK: 15 }, specialEffects: ["lightning_strike"] },
  { name: "Kizomba Flow Boots", description: "Move like water, flow like wind", type: "SHOES", rarity: "LEGENDARY", danceStyle: "KIZOMBA", statBonuses: { defense: 12, health: 10 }, skillBonuses: { FOLLOWING: 20 }, specialEffects: ["water_flow"] },
  { name: "Zouk Magic Heels", description: "Defy gravity with mystical power", type: "SHOES", rarity: "MYTHIC", danceStyle: "ZOUK", statBonuses: { mana: 20, luck: 15 }, skillBonuses: { SPINS: 25 }, specialEffects: ["gravity_defiance"] },
  { name: "Merengue March Boots", description: "March to victory with Dominican spirit", type: "SHOES", rarity: "COMMON", danceStyle: "MERENGUE", statBonuses: { stamina: 6, speed: 4 }, skillBonuses: { STAMINA: 8 }, specialEffects: ["march_rhythm"] },
  { name: "Reggaeton Power Sneakers", description: "Urban beats meet dance floor heat", type: "SHOES", rarity: "RARE", danceStyle: "REGGAETON", statBonuses: { attack: 10, charisma: 6 }, skillBonuses: { CREATIVITY: 12 }, specialEffects: ["urban_power"] },
  { name: "Cumbia Cloud Shoes", description: "Float on clouds of Colombian rhythm", type: "SHOES", rarity: "EPIC", danceStyle: "CUMBIA", statBonuses: { speed: 12, mana: 8 }, skillBonuses: { MUSICALITY: 15 }, specialEffects: ["cloud_float"] },
  { name: "Tango Passion Heels", description: "Burn with Argentine passion", type: "SHOES", rarity: "LEGENDARY", danceStyle: null, statBonuses: { attack: 15, charisma: 12 }, skillBonuses: { EXPRESSION: 20 }, specialEffects: ["passionate_fire"] },
  { name: "Samba Carnival Shoes", description: "Bring the carnival to every step", type: "SHOES", rarity: "RARE", danceStyle: null, statBonuses: { speed: 8, luck: 10 }, skillBonuses: { CREATIVITY: 15 }, specialEffects: ["carnival_energy"] },
  { name: "Flamenco Fire Boots", description: "Spanish fire in every stomp", type: "SHOES", rarity: "EPIC", danceStyle: null, statBonuses: { attack: 12, defense: 8 }, skillBonuses: { EXPRESSION: 18 }, specialEffects: ["flamenco_fire"] },
  { name: "Forró Folk Sandals", description: "Traditional Brazilian folk spirit", type: "SHOES", rarity: "COMMON", danceStyle: null, statBonuses: { stamina: 8, health: 5 }, skillBonuses: { STAMINA: 10 }, specialEffects: ["folk_spirit"] },
  { name: "Lambada Heat Shoes", description: "Feel the heat of forbidden dance", type: "SHOES", rarity: "RARE", danceStyle: null, statBonuses: { charisma: 12, mana: 6 }, skillBonuses: { EXPRESSION: 14 }, specialEffects: ["forbidden_heat"] },
  { name: "Mambo King Loafers", description: "Rule the mambo kingdom", type: "SHOES", rarity: "LEGENDARY", danceStyle: null, statBonuses: { attack: 18, charisma: 15 }, skillBonuses: { LEADING: 22 }, specialEffects: ["royal_command"] },
  { name: "Rumba Romance Slippers", description: "Dance the language of love", type: "SHOES", rarity: "EPIC", danceStyle: null, statBonuses: { mana: 15, charisma: 10 }, skillBonuses: { EXPRESSION: 16 }, specialEffects: ["romantic_aura"] },
  { name: "Bolero Ballad Boots", description: "Slow and passionate like a ballad", type: "SHOES", rarity: "RARE", danceStyle: null, statBonuses: { defense: 10, mana: 8 }, skillBonuses: { MUSICALITY: 12 }, specialEffects: ["ballad_grace"] },
  { name: "Hip-Hop Fusion Sneakers", description: "Where street meets Latin heat", type: "SHOES", rarity: "COMMON", danceStyle: null, statBonuses: { speed: 6, attack: 4 }, skillBonuses: { CREATIVITY: 8 }, specialEffects: ["fusion_power"] },
  { name: "Contemporary Flow Shoes", description: "Modern expression meets classic technique", type: "SHOES", rarity: "EPIC", danceStyle: null, statBonuses: { mana: 12, speed: 10 }, skillBonuses: { CREATIVITY: 18 }, specialEffects: ["contemporary_flow"] },
  { name: "Jazz Swing Shoes", description: "Swing into the jazz age", type: "SHOES", rarity: "RARE", danceStyle: null, statBonuses: { speed: 10, luck: 8 }, skillBonuses: { TIMING: 14 }, specialEffects: ["swing_rhythm"] },
  { name: "Ballroom Elegance Heels", description: "Refined elegance for the ballroom", type: "SHOES", rarity: "LEGENDARY", danceStyle: null, statBonuses: { charisma: 20, defense: 12 }, skillBonuses: { LEADING: 25, FOLLOWING: 25 }, specialEffects: ["ballroom_grace"] },

  // OUTFITS (20 items)
  { name: "Salsa King Suit", description: "Rule the salsa kingdom with style", type: "OUTFIT", rarity: "LEGENDARY", danceStyle: "SALSA", statBonuses: { attack: 15, charisma: 20 }, skillBonuses: { LEADING: 25 }, specialEffects: ["royal_presence"] },
  { name: "Bachata Queen Dress", description: "Reign over bachata with elegance", type: "OUTFIT", rarity: "LEGENDARY", danceStyle: "BACHATA", statBonuses: { charisma: 22, mana: 15 }, skillBonuses: { EXPRESSION: 25 }, specialEffects: ["queen_aura"] },
  { name: "Kizomba Mystic Robe", description: "Channel mystical kizomba energy", type: "OUTFIT", rarity: "EPIC", danceStyle: "KIZOMBA", statBonuses: { mana: 18, defense: 12 }, skillBonuses: { FOLLOWING: 20 }, specialEffects: ["mystic_energy"] },
  { name: "Zouk Wizard Cloak", description: "Master the magic of zouk", type: "OUTFIT", rarity: "MYTHIC", danceStyle: "ZOUK", statBonuses: { mana: 25, luck: 18 }, skillBonuses: { SPINS: 30 }, specialEffects: ["wizard_power"] },
  { name: "Cha-Cha Lightning Vest", description: "Electrify the dance floor", type: "OUTFIT", rarity: "EPIC", danceStyle: "CHACHA", statBonuses: { speed: 18, attack: 12 }, skillBonuses: { FOOTWORK: 20 }, specialEffects: ["electric_charge"] },
  { name: "Tango Passion Dress", description: "Embody the passion of Argentina", type: "OUTFIT", rarity: "LEGENDARY", danceStyle: null, statBonuses: { attack: 20, charisma: 18 }, skillBonuses: { EXPRESSION: 28 }, specialEffects: ["tango_passion"] },
  { name: "Samba Carnival Costume", description: "Bring Rio's carnival spirit", type: "OUTFIT", rarity: "RARE", danceStyle: null, statBonuses: { speed: 12, luck: 15 }, skillBonuses: { CREATIVITY: 18 }, specialEffects: ["carnival_spirit"] },
  { name: "Flamenco Fire Dress", description: "Spanish fire and fury", type: "OUTFIT", rarity: "EPIC", danceStyle: null, statBonuses: { attack: 16, defense: 10 }, skillBonuses: { EXPRESSION: 22 }, specialEffects: ["spanish_fire"] },
  { name: "Merengue Festival Shirt", description: "Celebrate Dominican culture", type: "OUTFIT", rarity: "COMMON", danceStyle: "MERENGUE", statBonuses: { stamina: 10, health: 8 }, skillBonuses: { STAMINA: 12 }, specialEffects: ["festival_joy"] },
  { name: "Reggaeton Urban Gear", description: "Street style meets dance floor", type: "OUTFIT", rarity: "RARE", danceStyle: "REGGAETON", statBonuses: { attack: 14, charisma: 10 }, skillBonuses: { CREATIVITY: 16 }, specialEffects: ["urban_style"] },
  { name: "Cumbia Traditional Dress", description: "Honor Colombian traditions", type: "OUTFIT", rarity: "EPIC", danceStyle: "CUMBIA", statBonuses: { mana: 16, charisma: 12 }, skillBonuses: { MUSICALITY: 20 }, specialEffects: ["traditional_spirit"] },
  { name: "Forró Country Outfit", description: "Brazilian countryside charm", type: "OUTFIT", rarity: "COMMON", danceStyle: null, statBonuses: { stamina: 12, health: 10 }, skillBonuses: { STAMINA: 15 }, specialEffects: ["country_charm"] },
  { name: "Lambada Forbidden Attire", description: "Dance the forbidden dance", type: "OUTFIT", rarity: "RARE", danceStyle: null, statBonuses: { charisma: 16, mana: 10 }, skillBonuses: { EXPRESSION: 18 }, specialEffects: ["forbidden_allure"] },
  { name: "Mambo Royal Suit", description: "Mambo royalty at its finest", type: "OUTFIT", rarity: "LEGENDARY", danceStyle: null, statBonuses: { attack: 22, charisma: 20 }, skillBonuses: { LEADING: 28 }, specialEffects: ["royal_mambo"] },
  { name: "Rumba Romance Gown", description: "Romantic elegance personified", type: "OUTFIT", rarity: "EPIC", danceStyle: null, statBonuses: { mana: 18, charisma: 14 }, skillBonuses: { EXPRESSION: 20 }, specialEffects: ["romantic_elegance"] },
  { name: "Bolero Ballad Suit", description: "Slow dance sophistication", type: "OUTFIT", rarity: "RARE", danceStyle: null, statBonuses: { defense: 14, mana: 12 }, skillBonuses: { MUSICALITY: 16 }, specialEffects: ["ballad_sophistication"] },
  { name: "Hip-Hop Fusion Outfit", description: "Urban meets Latin fusion", type: "OUTFIT", rarity: "COMMON", danceStyle: null, statBonuses: { speed: 8, attack: 6 }, skillBonuses: { CREATIVITY: 10 }, specialEffects: ["fusion_style"] },
  { name: "Contemporary Artist Attire", description: "Modern dance expression", type: "OUTFIT", rarity: "EPIC", danceStyle: null, statBonuses: { mana: 16, speed: 12 }, skillBonuses: { CREATIVITY: 22 }, specialEffects: ["artistic_expression"] },
  { name: "Jazz Age Ensemble", description: "Roaring twenties style", type: "OUTFIT", rarity: "RARE", danceStyle: null, statBonuses: { speed: 14, luck: 12 }, skillBonuses: { TIMING: 18 }, specialEffects: ["jazz_age_charm"] },
  { name: "Ballroom Champion Tuxedo", description: "Championship elegance", type: "OUTFIT", rarity: "LEGENDARY", danceStyle: null, statBonuses: { charisma: 25, defense: 15 }, skillBonuses: { LEADING: 30, FOLLOWING: 30 }, specialEffects: ["champion_elegance"] },

  // ACCESSORIES (20 items)
  { name: "Rhythm Master Watch", description: "Never miss a beat", type: "ACCESSORY", rarity: "EPIC", danceStyle: null, statBonuses: { luck: 15, mana: 10 }, skillBonuses: { TIMING: 25 }, specialEffects: ["perfect_timing"] },
  { name: "Salsa Fire Bracelet", description: "Burning passion on your wrist", type: "ACCESSORY", rarity: "RARE", danceStyle: "SALSA", statBonuses: { attack: 8, speed: 6 }, skillBonuses: { TIMING: 12 }, specialEffects: ["fire_passion"] },
  { name: "Bachata Heart Necklace", description: "Open hearts with every move", type: "ACCESSORY", rarity: "RARE", danceStyle: "BACHATA", statBonuses: { charisma: 12, mana: 8 }, skillBonuses: { EXPRESSION: 15 }, specialEffects: ["heart_connection"] },
  { name: "Kizomba Flow Ring", description: "Enhance your natural flow", type: "ACCESSORY", rarity: "EPIC", danceStyle: "KIZOMBA", statBonuses: { defense: 10, mana: 12 }, skillBonuses: { FOLLOWING: 18 }, specialEffects: ["flow_enhancement"] },
  { name: "Zouk Magic Amulet", description: "Channel mystical zouk power", type: "ACCESSORY", rarity: "LEGENDARY", danceStyle: "ZOUK", statBonuses: { mana: 20, luck: 15 }, skillBonuses: { SPINS: 25 }, specialEffects: ["magic_power"] },
  { name: "Cha-Cha Lightning Earrings", description: "Electric energy in your ears", type: "ACCESSORY", rarity: "EPIC", danceStyle: "CHACHA", statBonuses: { speed: 15, attack: 10 }, skillBonuses: { FOOTWORK: 18 }, specialEffects: ["lightning_energy"] },
  { name: "Tango Rose Brooch", description: "Passionate Argentine symbol", type: "ACCESSORY", rarity: "RARE", danceStyle: null, statBonuses: { charisma: 14, attack: 8 }, skillBonuses: { EXPRESSION: 16 }, specialEffects: ["passionate_symbol"] },
  { name: "Samba Feather Headpiece", description: "Carnival spirit on your head", type: "ACCESSORY", rarity: "EPIC", danceStyle: null, statBonuses: { speed: 12, luck: 14 }, skillBonuses: { CREATIVITY: 20 }, specialEffects: ["carnival_spirit"] },
  { name: "Flamenco Flower Hair Clip", description: "Spanish beauty and grace", type: "ACCESSORY", rarity: "RARE", danceStyle: null, statBonuses: { charisma: 10, defense: 8 }, skillBonuses: { EXPRESSION: 14 }, specialEffects: ["spanish_grace"] },
  { name: "Merengue Sun Pendant", description: "Dominican sunshine energy", type: "ACCESSORY", rarity: "COMMON", danceStyle: "MERENGUE", statBonuses: { stamina: 8, health: 6 }, skillBonuses: { STAMINA: 10 }, specialEffects: ["sunshine_energy"] },
  { name: "Reggaeton Gold Chain", description: "Urban luxury and style", type: "ACCESSORY", rarity: "RARE", danceStyle: "REGGAETON", statBonuses: { charisma: 12, attack: 8 }, skillBonuses: { CREATIVITY: 14 }, specialEffects: ["urban_luxury"] },
  { name: "Cumbia Traditional Belt", description: "Colombian heritage pride", type: "ACCESSORY", rarity: "COMMON", danceStyle: "CUMBIA", statBonuses: { defense: 8, mana: 6 }, skillBonuses: { MUSICALITY: 10 }, specialEffects: ["heritage_pride"] },
  { name: "Forró Leather Bracelet", description: "Rustic Brazilian charm", type: "ACCESSORY", rarity: "COMMON", danceStyle: null, statBonuses: { stamina: 6, health: 8 }, skillBonuses: { STAMINA: 8 }, specialEffects: ["rustic_charm"] },
  { name: "Lambada Silk Scarf", description: "Sensual forbidden elegance", type: "ACCESSORY", rarity: "RARE", danceStyle: null, statBonuses: { charisma: 14, mana: 8 }, skillBonuses: { EXPRESSION: 16 }, specialEffects: ["forbidden_elegance"] },
  { name: "Mambo Crown Jewel", description: "Royal mambo authority", type: "ACCESSORY", rarity: "LEGENDARY", danceStyle: null, statBonuses: { attack: 18, charisma: 20 }, skillBonuses: { LEADING: 25 }, specialEffects: ["royal_authority"] },
  { name: "Rumba Love Locket", description: "Romantic memories keeper", type: "ACCESSORY", rarity: "EPIC", danceStyle: null, statBonuses: { mana: 16, charisma: 12 }, skillBonuses: { EXPRESSION: 18 }, specialEffects: ["romantic_memories"] },
  { name: "Bolero Moonstone Ring", description: "Ballad under moonlight", type: "ACCESSORY", rarity: "RARE", danceStyle: null, statBonuses: { defense: 12, mana: 10 }, skillBonuses: { MUSICALITY: 14 }, specialEffects: ["moonlight_ballad"] },
  { name: "Hip-Hop Fusion Cap", description: "Street style meets Latin flair", type: "ACCESSORY", rarity: "COMMON", danceStyle: null, statBonuses: { speed: 6, attack: 4 }, skillBonuses: { CREATIVITY: 8 }, specialEffects: ["street_flair"] },
  { name: "Contemporary Art Glasses", description: "See dance as art", type: "ACCESSORY", rarity: "EPIC", danceStyle: null, statBonuses: { mana: 14, speed: 10 }, skillBonuses: { CREATIVITY: 20 }, specialEffects: ["artistic_vision"] },
  { name: "Jazz Pearl Necklace", description: "Timeless jazz elegance", type: "ACCESSORY", rarity: "RARE", danceStyle: null, statBonuses: { charisma: 16, luck: 10 }, skillBonuses: { TIMING: 16 }, specialEffects: ["timeless_elegance"] },

  // INSTRUMENTS (20 items)
  { name: "Magical Maracas", description: "Enchanted rhythm makers", type: "INSTRUMENT", rarity: "LEGENDARY", danceStyle: "SALSA", statBonuses: { mana: 20, luck: 15 }, skillBonuses: { MUSICALITY: 25 }, specialEffects: ["rhythm_mastery"] },
  { name: "Bachata Guitar Pick", description: "Channel bachata soul", type: "INSTRUMENT", rarity: "EPIC", danceStyle: "BACHATA", statBonuses: { mana: 16, charisma: 12 }, skillBonuses: { MUSICALITY: 20 }, specialEffects: ["soul_channeling"] },
  { name: "Kizomba Drum Stick", description: "Control the heartbeat of dance", type: "INSTRUMENT", rarity: "RARE", danceStyle: "KIZOMBA", statBonuses: { health: 12, stamina: 10 }, skillBonuses: { TIMING: 16 }, specialEffects: ["heartbeat_control"] },
  { name: "Zouk Magic Flute", description: "Mystical melodies of power", type: "INSTRUMENT", rarity: "MYTHIC", danceStyle: "ZOUK", statBonuses: { mana: 25, luck: 20 }, skillBonuses: { MUSICALITY: 30 }, specialEffects: ["mystical_melodies"] },
  { name: "Cha-Cha Lightning Sticks", description: "Electric rhythm controllers", type: "INSTRUMENT", rarity: "EPIC", danceStyle: "CHACHA", statBonuses: { speed: 16, attack: 12 }, skillBonuses: { TIMING: 20 }, specialEffects: ["electric_rhythm"] },
  { name: "Tango Bandoneón", description: "Soul of Argentine tango", type: "INSTRUMENT", rarity: "LEGENDARY", danceStyle: null, statBonuses: { mana: 22, charisma: 18 }, skillBonuses: { EXPRESSION: 28 }, specialEffects: ["tango_soul"] },
  { name: "Samba Whistle", description: "Command the carnival parade", type: "INSTRUMENT", rarity: "RARE", danceStyle: null, statBonuses: { speed: 10, luck: 12 }, skillBonuses: { CREATIVITY: 16 }, specialEffects: ["carnival_command"] },
  { name: "Flamenco Castanets", description: "Spanish percussion passion", type: "INSTRUMENT", rarity: "EPIC", danceStyle: null, statBonuses: { attack: 14, defense: 10 }, skillBonuses: { TIMING: 22 }, specialEffects: ["spanish_percussion"] },
  { name: "Merengue Güira", description: "Dominican metal scraper", type: "INSTRUMENT", rarity: "COMMON", danceStyle: "MERENGUE", statBonuses: { stamina: 8, health: 6 }, skillBonuses: { TIMING: 10 }, specialEffects: ["metal_rhythm"] },
  { name: "Reggaeton Beat Machine", description: "Urban rhythm generator", type: "INSTRUMENT", rarity: "RARE", danceStyle: "REGGAETON", statBonuses: { attack: 12, charisma: 10 }, skillBonuses: { CREATIVITY: 18 }, specialEffects: ["urban_beats"] },
  { name: "Cumbia Accordion", description: "Colombian folk harmony", type: "INSTRUMENT", rarity: "EPIC", danceStyle: "CUMBIA", statBonuses: { mana: 14, charisma: 12 }, skillBonuses: { MUSICALITY: 18 }, specialEffects: ["folk_harmony"] },
  { name: "Forró Triangle", description: "Brazilian metal percussion", type: "INSTRUMENT", rarity: "COMMON", danceStyle: null, statBonuses: { stamina: 6, health: 8 }, skillBonuses: { TIMING: 8 }, specialEffects: ["metal_percussion"] },
  { name: "Lambada Saxophone", description: "Sensual wind instrument", type: "INSTRUMENT", rarity: "RARE", danceStyle: null, statBonuses: { charisma: 14, mana: 10 }, skillBonuses: { EXPRESSION: 18 }, specialEffects: ["sensual_wind"] },
  { name: "Mambo Trumpet", description: "Royal brass announcement", type: "INSTRUMENT", rarity: "LEGENDARY", danceStyle: null, statBonuses: { attack: 20, charisma: 18 }, skillBonuses: { LEADING: 26 }, specialEffects: ["royal_brass"] },
  { name: "Rumba Bongos", description: "Romantic percussion duo", type: "INSTRUMENT", rarity: "EPIC", danceStyle: null, statBonuses: { mana: 16, charisma: 14 }, skillBonuses: { TIMING: 20 }, specialEffects: ["romantic_percussion"] },
  { name: "Bolero Guitar", description: "Ballad string emotions", type: "INSTRUMENT", rarity: "RARE", danceStyle: null, statBonuses: { defense: 12, mana: 14 }, skillBonuses: { MUSICALITY: 18 }, specialEffects: ["ballad_strings"] },
  { name: "Hip-Hop Turntables", description: "Street DJ equipment", type: "INSTRUMENT", rarity: "COMMON", danceStyle: null, statBonuses: { speed: 8, attack: 6 }, skillBonuses: { CREATIVITY: 12 }, specialEffects: ["street_dj"] },
  { name: "Contemporary Piano", description: "Modern expression keys", type: "INSTRUMENT", rarity: "EPIC", danceStyle: null, statBonuses: { mana: 18, speed: 12 }, skillBonuses: { CREATIVITY: 24 }, specialEffects: ["modern_keys"] },
  { name: "Jazz Saxophone", description: "Smooth jazz melodies", type: "INSTRUMENT", rarity: "RARE", danceStyle: null, statBonuses: { charisma: 16, luck: 12 }, skillBonuses: { TIMING: 18 }, specialEffects: ["smooth_jazz"] },
  { name: "Ballroom Orchestra Baton", description: "Conduct the dance", type: "INSTRUMENT", rarity: "LEGENDARY", danceStyle: null, statBonuses: { charisma: 25, defense: 15 }, skillBonuses: { LEADING: 32, FOLLOWING: 32 }, specialEffects: ["orchestra_conductor"] },

  // CHARMS (20 items)
  { name: "Lucky Dance Coin", description: "Fortune favors the dancer", type: "CHARM", rarity: "RARE", danceStyle: null, statBonuses: { luck: 15 }, skillBonuses: {}, specialEffects: ["fortune_favor"] },
  { name: "Energy Crystal", description: "Restore energy faster", type: "CHARM", rarity: "EPIC", danceStyle: null, statBonuses: { mana: 12, health: 8 }, skillBonuses: { STAMINA: 15 }, specialEffects: ["energy_regen"] },
  { name: "Perfectionist's Pendant", description: "Demand perfection", type: "CHARM", rarity: "LEGENDARY", danceStyle: null, statBonuses: { luck: 20, mana: 15 }, skillBonuses: { TIMING: 25 }, specialEffects: ["perfection_demand"] },
  { name: "Salsa Fire Ember", description: "Burning salsa passion", type: "CHARM", rarity: "RARE", danceStyle: "SALSA", statBonuses: { attack: 10, speed: 8 }, skillBonuses: { TIMING: 12 }, specialEffects: ["burning_passion"] },
  { name: "Bachata Soul Stone", description: "Connect with bachata spirit", type: "CHARM", rarity: "EPIC", danceStyle: "BACHATA", statBonuses: { charisma: 15, mana: 12 }, skillBonuses: { EXPRESSION: 18 }, specialEffects: ["soul_connection"] },
  { name: "Kizomba Flow Orb", description: "Enhance natural flow", type: "CHARM", rarity: "RARE", danceStyle: "KIZOMBA", statBonuses: { defense: 12, mana: 10 }, skillBonuses: { FOLLOWING: 16 }, specialEffects: ["flow_enhancement"] },
  { name: "Zouk Magic Sphere", description: "Mystical zouk power", type: "CHARM", rarity: "MYTHIC", danceStyle: "ZOUK", statBonuses: { mana: 25, luck: 20 }, skillBonuses: { SPINS: 30 }, specialEffects: ["mystical_power"] },
  { name: "Cha-Cha Lightning Bolt", description: "Electric dance energy", type: "CHARM", rarity: "EPIC", danceStyle: "CHACHA", statBonuses: { speed: 18, attack: 12 }, skillBonuses: { FOOTWORK: 20 }, specialEffects: ["electric_energy"] },
  { name: "Tango Passion Rose", description: "Argentine romantic symbol", type: "CHARM", rarity: "LEGENDARY", danceStyle: null, statBonuses: { attack: 18, charisma: 20 }, skillBonuses: { EXPRESSION: 25 }, specialEffects: ["romantic_symbol"] },
  { name: "Samba Carnival Mask", description: "Rio carnival spirit", type: "CHARM", rarity: "RARE", danceStyle: null, statBonuses: { speed: 12, luck: 14 }, skillBonuses: { CREATIVITY: 18 }, specialEffects: ["carnival_spirit"] },
  { name: "Flamenco Fire Fan", description: "Spanish passion tool", type: "CHARM", rarity: "EPIC", danceStyle: null, statBonuses: { attack: 14, defense: 12 }, skillBonuses: { EXPRESSION: 20 }, specialEffects: ["passion_tool"] },
  { name: "Merengue Sun Charm", description: "Dominican sunshine power", type: "CHARM", rarity: "COMMON", danceStyle: "MERENGUE", statBonuses: { stamina: 10, health: 8 }, skillBonuses: { STAMINA: 12 }, specialEffects: ["sunshine_power"] },
  { name: "Reggaeton Urban Tag", description: "Street credibility marker", type: "CHARM", rarity: "RARE", danceStyle: "REGGAETON", statBonuses: { charisma: 12, attack: 10 }, skillBonuses: { CREATIVITY: 16 }, specialEffects: ["street_cred"] },
  { name: "Cumbia Heritage Flag", description: "Colombian pride symbol", type: "CHARM", rarity: "COMMON", danceStyle: "CUMBIA", statBonuses: { defense: 8, mana: 8 }, skillBonuses: { MUSICALITY: 12 }, specialEffects: ["heritage_pride"] },
  { name: "Forró Country Star", description: "Brazilian countryside symbol", type: "CHARM", rarity: "COMMON", danceStyle: null, statBonuses: { stamina: 8, health: 10 }, skillBonuses: { STAMINA: 10 }, specialEffects: ["country_symbol"] },
  { name: "Lambada Forbidden Fruit", description: "Tempting dance symbol", type: "CHARM", rarity: "RARE", danceStyle: null, statBonuses: { charisma: 16, mana: 10 }, skillBonuses: { EXPRESSION: 18 }, specialEffects: ["forbidden_temptation"] },
  { name: "Mambo Royal Scepter", description: "Mambo kingdom authority", type: "CHARM", rarity: "LEGENDARY", danceStyle: null, statBonuses: { attack: 22, charisma: 20 }, skillBonuses: { LEADING: 28 }, specialEffects: ["royal_authority"] },
  { name: "Rumba Love Heart", description: "Romantic dance essence", type: "CHARM", rarity: "EPIC", danceStyle: null, statBonuses: { mana: 18, charisma: 15 }, skillBonuses: { EXPRESSION: 22 }, specialEffects: ["romantic_essence"] },
  { name: "Bolero Moon Crescent", description: "Ballad under moonlight", type: "CHARM", rarity: "RARE", danceStyle: null, statBonuses: { defense: 14, mana: 12 }, skillBonuses: { MUSICALITY: 16 }, specialEffects: ["moonlight_ballad"] },
  { name: "Universal Dance Spirit", description: "All dances united", type: "CHARM", rarity: "MYTHIC", danceStyle: null, statBonuses: { attack: 20, defense: 20, speed: 20, mana: 20, health: 20, stamina: 20, charisma: 20, luck: 20 }, skillBonuses: { TIMING: 25, FOOTWORK: 25, SPINS: 25, LEADING: 25, FOLLOWING: 25, MUSICALITY: 25, EXPRESSION: 25, STAMINA: 25, CREATIVITY: 25, CHARISMA: 25 }, specialEffects: ["universal_mastery"] }
];

async function seedEquipment() {
  try {
    logger.info('🎽 Seeding comprehensive equipment collection...');
    
    // First, delete all existing equipment
    await prisma.equipment.deleteMany({});
    logger.info('🗑️ Cleared existing equipment');
    
    // Then create all new equipment
    for (const item of equipmentItems) {
      await prisma.equipment.create({
        data: {
          name: item.name,
          description: item.description,
          type: item.type,
          rarity: item.rarity,
          danceStyle: item.danceStyle,
          statBonuses: item.statBonuses,
          skillBonuses: item.skillBonuses,
          specialEffects: item.specialEffects,
          isForSale: true,
          price: getPriceByRarity(item.rarity)
        }
      });
    }
    
    logger.info(`✅ Successfully seeded ${equipmentItems.length} equipment items`);
  } catch (error) {
    logger.error('❌ Error seeding equipment:', error);
    throw error;
  }
}

function getPriceByRarity(rarity) {
  const prices = {
    COMMON: 500,
    RARE: 2000,
    EPIC: 8000,
    LEGENDARY: 15000,
    MYTHIC: 25000
  };
  return prices[rarity] || 1000;
}

module.exports = { seedEquipment }; 