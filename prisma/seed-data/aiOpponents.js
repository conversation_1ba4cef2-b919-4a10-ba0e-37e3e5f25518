const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const logger = require('../../backend/utils/logger');

const prisma = new PrismaClient();

// Enhanced AI opponents with dance style specializations
const aiOpponents = [
  {
    username: 'salsa_sam',
    displayName: 'Salsa Sam',
    level: 3,
    primaryStyle: 'SALSA',
    traits: ['SALSA_FIRE', 'RHYTHM_WARRIOR'],
    stats: {
      health: 130,
      mana: 65,
      attack: 15,
      defense: 12,
      speed: 18,
      luck: 14
    },
    skills: {
      TIMING: 20,
      FOOTWORK: 25,
      SPINS: 15,
      MUSICALITY: 18,
      CHARISMA: 12
    },
    stylePoints: {
      SALSA: 50,
      BACHATA: 10,
      CHACHA: 20
    }
  },
  {
    username: 'bachata_betty',
    displayName: 'Bachata Betty',
    level: 5,
    primaryStyle: 'BACHATA',
    traits: ['BACHATA_SOUL', 'SMOOTH_LEADS', 'CROWD_PLEASER'],
    stats: {
      health: 150,
      mana: 85,
      attack: 12,
      defense: 18,
      speed: 14,
      luck: 16
    },
    skills: {
      TIMING: 25,
      LEADING: 30,
      FOLLOWING: 28,
      EXPRESSION: 35,
      MUSICALITY: 25,
      CHARISMA: 20
    },
    stylePoints: {
      BACHATA: 80,
      SALSA: 15,
      KIZOMBA: 25
    }
  },
  {
    username: 'zouk_zoe',
    displayName: 'Zouk Zoe',
    level: 7,
    primaryStyle: 'ZOUK',
    traits: ['ZOUK_MAGIC', 'DANCE_MYSTIC', 'PERFECTIONIST'],
    stats: {
      health: 170,
      mana: 120,
      attack: 20,
      defense: 15,
      speed: 22,
      luck: 25
    },
    skills: {
      TIMING: 35,
      SPINS: 40,
      MUSICALITY: 38,
      CREATIVITY: 35,
      EXPRESSION: 30,
      CHARISMA: 25
    },
    stylePoints: {
      ZOUK: 120,
      BACHATA: 30,
      SALSA: 20
    }
  },
  {
    username: 'kizomba_ken',
    displayName: 'Kizomba Ken',
    level: 10,
    primaryStyle: 'KIZOMBA',
    traits: ['KIZOMBA_FLOW', 'GROOVE_GUARDIAN', 'SHOWSTOPPER', 'IMPROVISER'],
    stats: {
      health: 200,
      mana: 150,
      attack: 25,
      defense: 22,
      speed: 20,
      luck: 18
    },
    skills: {
      TIMING: 45,
      FOLLOWING: 50,
      LEADING: 45,
      EXPRESSION: 48,
      MUSICALITY: 42,
      STAMINA: 40,
      CREATIVITY: 38
    },
    stylePoints: {
      KIZOMBA: 200,
      ZOUK: 50,
      BACHATA: 40,
      SALSA: 25
    }
  }
];

async function seedAiOpponents() {
  try {
    logger.info('Seeding AI opponents...');
    
    for (const opponent of aiOpponents) {
      // Create AI user account
      const aiUserId = `ai_${opponent.username}`;
      
      // Check if AI user already exists
      let aiUser = await prisma.user.findUnique({
        where: { id: aiUserId }
      });

      if (!aiUser) {
        // Create AI user
        aiUser = await prisma.user.create({
          data: {
            id: aiUserId,
            username: opponent.username,
            displayName: opponent.displayName,
            role: 'DANCER',
            credits: 0,
            passwordHash: await bcrypt.hash('ai_password', 10)
          }
        });
      }

      // Check if AI hero already exists
      let aiHero = await prisma.hero.findUnique({
        where: { userId: aiUserId }
      });

      if (!aiHero) {
        // Create AI hero
        aiHero = await prisma.hero.create({
          data: {
            userId: aiUserId,
            name: opponent.displayName,
            level: opponent.level,
            experience: Math.pow(opponent.level, 2) * 100, // Max XP for level
            energy: 10,
            health: opponent.stats.health,
            mana: opponent.stats.mana,
            attack: opponent.stats.attack,
            defense: opponent.stats.defense,
            speed: opponent.stats.speed,
            luck: opponent.stats.luck,
            primaryStyle: opponent.primaryStyle,
            secondaryStyle: getSecondaryStyle(opponent.stylePoints),
            stylePoints: opponent.stylePoints,
            skills: opponent.skills,
            equippedItems: {},
            inventory: [],
            traits: opponent.traits,
            traitPoints: 0,
            skillPoints: 0,
            wins: Math.floor(opponent.level * 8 + Math.random() * 20),
            losses: Math.floor(opponent.level * 3 + Math.random() * 10),
            draws: Math.floor(Math.random() * 5),
            winStreak: Math.floor(Math.random() * 8),
            bestWinStreak: Math.floor(opponent.level * 2 + Math.random() * 10),
            totalBattles: 0, // Will be calculated
            globalRank: null,
            styleRanks: calculateStyleRanks(opponent.level, opponent.stylePoints),
            trophies: Math.floor(opponent.level * 5),
            titles: generateTitles(opponent.level, opponent.primaryStyle),
            favoriteOpponents: [],
            blockedPlayers: [],
            prestige: 0,
            prestigePoints: 0
          }
        });

        // Calculate total battles
        const totalBattles = aiHero.wins + aiHero.losses + aiHero.draws;
        await prisma.hero.update({
          where: { id: aiHero.id },
          data: { totalBattles }
        });

        logger.info(`Created AI opponent: ${opponent.displayName} (Level ${opponent.level})`);
      }
    }

    logger.info('AI opponents seeded successfully');
  } catch (error) {
    logger.error('Error seeding AI opponents:', error);
    throw error;
  }
}

function getSecondaryStyle(stylePoints) {
  const styles = Object.entries(stylePoints);
  styles.sort((a, b) => b[1] - a[1]); // Sort by points descending
  
  // Return second highest style if it has significant points
  if (styles.length > 1 && styles[1][1] >= 20) {
    return styles[1][0];
  }
  
  return null;
}

function calculateStyleRanks(level, stylePoints) {
  const ranks = {};
  
  for (const [style, points] of Object.entries(stylePoints)) {
    // Calculate rank based on level and style points
    // Lower rank number = better rank
    const baseRank = 1000 - (level * 50) - points;
    ranks[style] = Math.max(1, baseRank + Math.floor(Math.random() * 100));
  }
  
  return ranks;
}

function generateTitles(level, primaryStyle) {
  const titles = [];
  
  // Level-based titles
  if (level >= 3) titles.push('Novice Dancer');
  if (level >= 5) titles.push('Skilled Performer');
  if (level >= 7) titles.push('Dance Virtuoso');
  if (level >= 10) titles.push('Master of Movement');
  
  // Style-specific titles
  const styleTitles = {
    SALSA: ['Salsa Specialist', 'Fire Dancer', 'Rhythm Master'],
    BACHATA: ['Bachata Expert', 'Soul Dancer', 'Heart Capturer'],
    KIZOMBA: ['Kizomba Master', 'Flow Artist', 'Connection Guru'],
    ZOUK: ['Zouk Wizard', 'Magic Mover', 'Mystical Dancer'],
    CHACHA: ['Cha-Cha Champion', 'Lightning Stepper', 'Thunder Dancer']
  };
  
  if (styleTitles[primaryStyle]) {
    titles.push(...styleTitles[primaryStyle].slice(0, Math.floor(level / 3) + 1));
  }
  
  return titles;
}

module.exports = { seedAiOpponents }; 