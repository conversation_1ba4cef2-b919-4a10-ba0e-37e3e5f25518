const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const combos = [
  // Basic Salsa Combos
  {
    name: "Salsa Basic Foundation",
    danceStyle: "SALSA",
    bodySequence: "LL+RL",
    beatCount: 4,
    timingWindow: 300,
    difficulty: 1,
    damage: 15,
    energyCost: 5,
    description: "The fundamental salsa step - master this first",
    unlockLevel: 1
  },
  {
    name: "Cross Body Lead",
    danceStyle: "SALSA",
    bodySequence: "LL+RL+LH+RH",
    beatCount: 8,
    timingWindow: 250,
    difficulty: 3,
    damage: 25,
    energyCost: 8,
    description: "Classic salsa move that opens up the dance floor",
    unlockLevel: 2
  },
  {
    name: "Salsa Spin Combo",
    danceStyle: "SALSA",
    bodySequence: "LL+RL+LH+RH+H",
    beatCount: 12,
    timingWindow: 200,
    difficulty: 5,
    damage: 40,
    energyCost: 12,
    description: "Add flair with spins and head movement",
    unlockLevel: 4
  },
  {
    name: "Fire Storm",
    danceStyle: "SALSA",
    bodySequence: "LL+RL+LH+RH+H+T+HP",
    beatCount: 20,
    timingWindow: 150,
    difficulty: 8,
    damage: 70,
    energyCost: 20,
    manaCost: 10,
    description: "Ultimate salsa combination using full body movement",
    unlockLevel: 8
  },

  // Basic Bachata Combos
  {
    name: "Bachata Basic Flow",
    danceStyle: "BACHATA",
    bodySequence: "LL+RL+HP",
    beatCount: 6,
    timingWindow: 300,
    difficulty: 2,
    damage: 20,
    energyCost: 6,
    description: "Sensual bachata basic with hip movement",
    unlockLevel: 1
  },
  {
    name: "Sensual Dip",
    danceStyle: "BACHATA",
    bodySequence: "LL+RL+T+LH+RH",
    beatCount: 10,
    timingWindow: 250,
    difficulty: 4,
    damage: 35,
    energyCost: 10,
    description: "Romantic dip that showcases connection",
    unlockLevel: 3
  },
  {
    name: "Soul Connection",
    danceStyle: "BACHATA",
    bodySequence: "LL+RL+HP+T+LH+RH+H",
    beatCount: 16,
    timingWindow: 180,
    difficulty: 7,
    damage: 60,
    energyCost: 16,
    manaCost: 8,
    description: "Deep emotional connection through full body expression",
    unlockLevel: 6
  },

  // Basic Kizomba Combos
  {
    name: "Kizomba Flow State",
    danceStyle: "KIZOMBA",
    bodySequence: "LL+RL+HP+T",
    beatCount: 8,
    timingWindow: 280,
    difficulty: 3,
    damage: 30,
    energyCost: 8,
    description: "Find the flow and connection in Kizomba",
    unlockLevel: 2
  },
  {
    name: "Perfect Embrace",
    danceStyle: "KIZOMBA",
    bodySequence: "LL+RL+HP+T+LH+RH",
    beatCount: 14,
    timingWindow: 200,
    difficulty: 6,
    damage: 50,
    energyCost: 14,
    manaCost: 5,
    description: "Master the intimate embrace of Kizomba",
    unlockLevel: 5
  },

  // Basic Zouk Combos
  {
    name: "Zouk Magic Sequence",
    danceStyle: "ZOUK",
    bodySequence: "LL+RL+LH+RH+H",
    beatCount: 12,
    timingWindow: 220,
    difficulty: 4,
    damage: 35,
    energyCost: 12,
    description: "Magical flowing movements of Brazilian Zouk",
    unlockLevel: 3
  },
  {
    name: "Levitation Master",
    danceStyle: "ZOUK",
    bodySequence: "LL+RL+LH+RH+H+T+HP",
    beatCount: 18,
    timingWindow: 160,
    difficulty: 9,
    damage: 75,
    energyCost: 18,
    manaCost: 12,
    description: "Defy gravity with advanced Zouk techniques",
    unlockLevel: 9
  },

  // Basic Cha-Cha Combos
  {
    name: "Lightning Steps",
    danceStyle: "CHACHA",
    bodySequence: "LL+RL+LL+RL",
    beatCount: 8,
    timingWindow: 200,
    difficulty: 3,
    damage: 25,
    energyCost: 8,
    description: "Quick, sharp cha-cha steps like lightning",
    unlockLevel: 2
  },
  {
    name: "Thunder Strike",
    danceStyle: "CHACHA",
    bodySequence: "LL+RL+LH+RH+H+T",
    beatCount: 16,
    timingWindow: 150,
    difficulty: 7,
    damage: 65,
    energyCost: 16,
    manaCost: 8,
    description: "Powerful cha-cha combination with explosive energy",
    unlockLevel: 7
  },

  // Universal Combos
  {
    name: "Perfect Timing",
    danceStyle: "UNIVERSAL",
    bodySequence: "LL+RL+LH+RH",
    beatCount: 8,
    timingWindow: 100,
    difficulty: 5,
    damage: 45,
    energyCost: 10,
    description: "Master timing across all dance styles",
    unlockLevel: 4
  },
  {
    name: "Dance Fusion Ultimate",
    danceStyle: "UNIVERSAL",
    bodySequence: "LL+RL+LH+RH+H+T+HP+LL+RL",
    beatCount: 24,
    timingWindow: 120,
    difficulty: 10,
    damage: 100,
    energyCost: 25,
    manaCost: 15,
    description: "Ultimate fusion of all dance styles",
    unlockLevel: 12
  },

  // Technical Combos
  {
    name: "Footwork Mastery",
    danceStyle: "UNIVERSAL",
    bodySequence: "LL+RL+LL+RL+LL+RL",
    beatCount: 12,
    timingWindow: 180,
    difficulty: 6,
    damage: 40,
    energyCost: 12,
    description: "Complex footwork patterns for technical dancers",
    unlockLevel: 5
  },
  {
    name: "Hip Isolation",
    danceStyle: "UNIVERSAL",
    bodySequence: "HP+HP+HP+T",
    beatCount: 6,
    timingWindow: 250,
    difficulty: 4,
    damage: 30,
    energyCost: 8,
    description: "Isolate and control hip movements",
    unlockLevel: 3
  },

  // Advanced Combinations
  {
    name: "Salsa Shine Spectacular",
    danceStyle: "SALSA",
    bodySequence: "LL+RL+LH+RH+LL+RL+H+T",
    beatCount: 16,
    timingWindow: 140,
    difficulty: 8,
    damage: 80,
    energyCost: 18,
    manaCost: 10,
    description: "Solo salsa shines that dazzle the crowd",
    unlockLevel: 8
  },
  {
    name: "Bachata Sensual Wave",
    danceStyle: "BACHATA",
    bodySequence: "HP+T+LH+RH+HP+T",
    beatCount: 12,
    timingWindow: 200,
    difficulty: 6,
    damage: 55,
    energyCost: 14,
    manaCost: 6,
    description: "Flowing body waves in bachata style",
    unlockLevel: 5
  },
  {
    name: "Kizomba Saida",
    danceStyle: "KIZOMBA",
    bodySequence: "LL+RL+T+HP+LH",
    beatCount: 10,
    timingWindow: 220,
    difficulty: 5,
    damage: 45,
    energyCost: 12,
    description: "Classic Kizomba exit move with style",
    unlockLevel: 4
  }
];

async function seedCombos() {
  try {
    console.log('🥊 Seeding battle combos...');
    
    // Clear existing combos
    await prisma.battleCombo.deleteMany({});
    
    // Create new combos
    for (const combo of combos) {
      await prisma.battleCombo.create({
        data: combo
      });
    }
    
    console.log(`✅ Successfully seeded ${combos.length} battle combos`);
  } catch (error) {
    console.error('❌ Error seeding combos:', error);
    throw error;
  }
}

module.exports = { seedCombos };

if (require.main === module) {
  seedCombos()
    .catch(console.error)
    .finally(() => prisma.$disconnect());
} 