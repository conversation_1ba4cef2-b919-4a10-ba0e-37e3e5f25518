const { PrismaClient } = require('@prisma/client');
const logger = require('../../backend/utils/logger');

const prisma = new PrismaClient();

// Enhanced battle moves with dance styles and audio
const enhancedBattleMoves = [
  // SALSA MOVES
  {
    name: "Salsa Basic",
    description: "The fundamental salsa step with perfect timing",
    danceStyle: "SALSA",
    type: "BASIC",
    power: 15,
    accuracy: 95,
    speed: 100,
    unlockLevel: 1,
    energyCost: 1,
    manaCost: 0,
    cooldown: 0,
    effects: [],
    soundEffect: "salsa_basic.mp3",
    animation: "salsa_basic",
    rarity: "COMMON"
  },
  {
    name: "Cross Body Lead",
    description: "Classic salsa move that opens up the dance floor",
    danceStyle: "SALSA",
    type: "TURN",
    power: 25,
    accuracy: 85,
    speed: 110,
    unlockLevel: 3,
    energyCost: 2,
    manaCost: 5,
    cooldown: 1,
    effects: ["position_advantage"],
    soundEffect: "salsa_cross_body.mp3",
    animation: "cross_body_lead",
    rarity: "COMMON"
  },
  {
    name: "Salsa Spin Combo",
    description: "Multiple spins that dazzle the audience",
    danceStyle: "SALSA",
    type: "COMBO",
    power: 40,
    accuracy: 70,
    speed: 120,
    unlockLevel: 8,
    energyCost: 3,
    manaCost: 15,
    cooldown: 2,
    effects: ["dizzy", "crowd_boost"],
    soundEffect: "salsa_spin_combo.mp3",
    animation: "salsa_spin_combo",
    rarity: "RARE"
  },
  {
    name: "Salsa Fire Storm",
    description: "Ultimate salsa move that ignites the dance floor",
    danceStyle: "SALSA",
    type: "ULTIMATE",
    power: 80,
    accuracy: 60,
    speed: 150,
    unlockLevel: 15,
    energyCost: 5,
    manaCost: 30,
    cooldown: 5,
    effects: ["burn", "intimidate", "energy_drain"],
    soundEffect: "salsa_fire_storm.mp3",
    animation: "salsa_fire_storm",
    rarity: "LEGENDARY"
  },

  // BACHATA MOVES
  {
    name: "Bachata Basic",
    description: "Smooth and sensual bachata foundation",
    danceStyle: "BACHATA",
    type: "BASIC",
    power: 12,
    accuracy: 98,
    speed: 90,
    unlockLevel: 1,
    energyCost: 1,
    manaCost: 0,
    cooldown: 0,
    effects: ["smooth"],
    soundEffect: "bachata_basic.mp3",
    animation: "bachata_basic",
    rarity: "COMMON"
  },
  {
    name: "Bachata Dip",
    description: "Romantic dip that captures hearts",
    danceStyle: "BACHATA",
    type: "SHINE",
    power: 30,
    accuracy: 80,
    speed: 85,
    unlockLevel: 5,
    energyCost: 2,
    manaCost: 10,
    cooldown: 1,
    effects: ["charm", "heal_minor"],
    soundEffect: "bachata_dip.mp3",
    animation: "bachata_dip",
    rarity: "RARE"
  },
  {
    name: "Soul Connection",
    description: "Deep bachata move that connects souls",
    danceStyle: "BACHATA",
    type: "SPECIAL",
    power: 50,
    accuracy: 75,
    speed: 80,
    unlockLevel: 12,
    energyCost: 4,
    manaCost: 20,
    cooldown: 3,
    effects: ["soul_bond", "mana_regen", "defense_boost"],
    soundEffect: "bachata_soul.mp3",
    animation: "soul_connection",
    rarity: "EPIC"
  },

  // KIZOMBA MOVES
  {
    name: "Kizomba Flow",
    description: "Hypnotic kizomba movement that flows like water",
    danceStyle: "KIZOMBA",
    type: "BASIC",
    power: 18,
    accuracy: 90,
    speed: 75,
    unlockLevel: 1,
    energyCost: 1,
    manaCost: 0,
    cooldown: 0,
    effects: ["flow"],
    soundEffect: "kizomba_flow.mp3",
    animation: "kizomba_flow",
    rarity: "COMMON"
  },
  {
    name: "Kizomba Embrace",
    description: "Intimate embrace that creates unbreakable connection",
    danceStyle: "KIZOMBA",
    type: "SPECIAL",
    power: 35,
    accuracy: 85,
    speed: 70,
    unlockLevel: 7,
    energyCost: 3,
    manaCost: 15,
    cooldown: 2,
    effects: ["embrace", "stamina_boost", "counter_protection"],
    soundEffect: "kizomba_embrace.mp3",
    animation: "kizomba_embrace",
    rarity: "RARE"
  },

  // ZOUK MOVES
  {
    name: "Zouk Magic",
    description: "Mystical zouk move that bends reality",
    danceStyle: "ZOUK",
    type: "BASIC",
    power: 20,
    accuracy: 88,
    speed: 95,
    unlockLevel: 1,
    energyCost: 1,
    manaCost: 5,
    cooldown: 0,
    effects: ["magic"],
    soundEffect: "zouk_magic.mp3",
    animation: "zouk_magic",
    rarity: "COMMON"
  },
  {
    name: "Zouk Levitation",
    description: "Gravity-defying zouk move that lifts spirits",
    danceStyle: "ZOUK",
    type: "ULTIMATE",
    power: 70,
    accuracy: 65,
    speed: 130,
    unlockLevel: 18,
    energyCost: 5,
    manaCost: 35,
    cooldown: 4,
    effects: ["levitate", "inspire", "mana_burst"],
    soundEffect: "zouk_levitation.mp3",
    animation: "zouk_levitation",
    rarity: "MYTHIC"
  },

  // CHACHA MOVES
  {
    name: "Cha-Cha Lightning",
    description: "Lightning-fast cha-cha steps that electrify",
    danceStyle: "CHACHA",
    type: "BASIC",
    power: 22,
    accuracy: 92,
    speed: 130,
    unlockLevel: 1,
    energyCost: 2,
    manaCost: 0,
    cooldown: 0,
    effects: ["lightning"],
    soundEffect: "chacha_lightning.mp3",
    animation: "chacha_lightning",
    rarity: "COMMON"
  },
  {
    name: "Thunder Strike",
    description: "Explosive cha-cha combo that shakes the ground",
    danceStyle: "CHACHA",
    type: "COMBO",
    power: 55,
    accuracy: 75,
    speed: 140,
    unlockLevel: 10,
    energyCost: 4,
    manaCost: 20,
    cooldown: 3,
    effects: ["thunder", "stun", "speed_boost"],
    soundEffect: "chacha_thunder.mp3",
    animation: "thunder_strike",
    rarity: "EPIC"
  },

  // UNIVERSAL MOVES
  {
    name: "Perfect Timing",
    description: "Universal move that showcases perfect musical timing",
    danceStyle: "SALSA", // Default, but works with all styles
    type: "SPECIAL",
    power: 45,
    accuracy: 100,
    speed: 100,
    unlockLevel: 6,
    energyCost: 3,
    manaCost: 10,
    cooldown: 2,
    effects: ["perfect_timing", "accuracy_boost"],
    soundEffect: "perfect_timing.mp3",
    animation: "perfect_timing",
    rarity: "RARE"
  },
  {
    name: "Dance Fusion",
    description: "Combines elements from all dance styles",
    danceStyle: "SALSA", // Default
    type: "ULTIMATE",
    power: 90,
    accuracy: 50,
    speed: 120,
    unlockLevel: 20,
    energyCost: 6,
    manaCost: 40,
    cooldown: 6,
    effects: ["fusion", "style_mastery", "ultimate_power"],
    soundEffect: "dance_fusion.mp3",
    animation: "dance_fusion",
    rarity: "MYTHIC"
  },

  // DEFENSIVE MOVES
  {
    name: "Defensive Stance",
    description: "Protective stance that reduces incoming damage",
    danceStyle: "SALSA",
    type: "BASIC",
    power: 5,
    accuracy: 100,
    speed: 50,
    unlockLevel: 2,
    energyCost: 1,
    manaCost: 0,
    cooldown: 0,
    effects: ["defense_boost", "damage_reduction"],
    soundEffect: "defensive_stance.mp3",
    animation: "defensive_stance",
    rarity: "COMMON"
  },
  {
    name: "Counter Attack",
    description: "Responds to opponent's move with a counter",
    danceStyle: "BACHATA",
    type: "SPECIAL",
    power: 35,
    accuracy: 90,
    speed: 110,
    unlockLevel: 9,
    energyCost: 2,
    manaCost: 15,
    cooldown: 2,
    effects: ["counter", "momentum_shift"],
    soundEffect: "counter_attack.mp3",
    animation: "counter_attack",
    rarity: "RARE"
  }
];

async function seedBattleMoves() {
  try {
    logger.info('Starting enhanced battle moves seeding...');
    
    // Clear existing battle moves
    await prisma.battleMove.deleteMany({});
    logger.info('Cleared existing battle moves');
    
    // Create enhanced battle moves
    for (const move of enhancedBattleMoves) {
      await prisma.battleMove.create({
        data: {
          name: move.name,
          description: move.description,
          danceStyle: move.danceStyle,
          type: move.type,
          power: move.power,
          accuracy: move.accuracy,
          speed: move.speed,
          unlockLevel: move.unlockLevel,
          energyCost: move.energyCost,
          manaCost: move.manaCost,
          cooldown: move.cooldown,
          effects: move.effects,
          soundEffect: move.soundEffect,
          animation: move.animation,
          rarity: move.rarity
        }
      });
    }
    
    logger.info(`Successfully seeded ${enhancedBattleMoves.length} enhanced battle moves`);
  } catch (error) {
    logger.error('Error seeding enhanced battle moves:', error);
    throw error;
  }
}

module.exports = { seedBattleMoves }; 