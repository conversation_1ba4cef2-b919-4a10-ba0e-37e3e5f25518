const { PrismaClient } = require('@prisma/client');
const logger = require('../../backend/utils/logger');

const prisma = new PrismaClient();

// Comprehensive dance-themed quest system with 100 quests
const enhancedQuests = [
  // DAILY QUESTS (30 items)
  { name: "Salsa Basic Master", description: "Master the salsa basic step by winning 3 salsa battles", type: "DAILY", category: "BATTLE", requirements: { salsa_battles_won: 3 }, danceStyle: "SALSA", minLevel: 1, maxLevel: null, xpReward: 150, coinReward: 100, itemRewards: [], skillPoints: 2, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 3 },
  { name: "Cross Body Lead Champion", description: "Execute 10 cross body leads in salsa battles", type: "DAILY", category: "SKILL", requirements: { cross_body_leads: 10 }, danceStyle: "SALSA", minLevel: 3, maxLevel: null, xpReward: 120, coinReward: 80, itemRewards: [], skillPoints: 3, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 10 },
  { name: "Bachata Sensual Flow", description: "Perform 8 sensual bachata moves", type: "DAILY", category: "SKILL", requirements: { bachata_sensual_moves: 8 }, danceStyle: "BACHATA", minLevel: 2, maxLevel: null, xpReward: 140, coinReward: 90, itemRewards: [], skillPoints: 2, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 8 },
  { name: "Kizomba Connection", description: "Maintain perfect connection for 5 consecutive moves", type: "DAILY", category: "SKILL", requirements: { kizomba_connection_streak: 5 }, danceStyle: "KIZOMBA", minLevel: 4, maxLevel: null, xpReward: 160, coinReward: 110, itemRewards: [], skillPoints: 3, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 1 },
  { name: "Zouk Head Movement", description: "Execute 12 zouk head movements", type: "DAILY", category: "SKILL", requirements: { zouk_head_movements: 12 }, danceStyle: "ZOUK", minLevel: 5, maxLevel: null, xpReward: 180, coinReward: 120, itemRewards: [], skillPoints: 4, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 12 },
  { name: "Cha-Cha Triple Step", description: "Perform 15 perfect cha-cha triple steps", type: "DAILY", category: "SKILL", requirements: { chacha_triple_steps: 15 }, danceStyle: "CHACHA", minLevel: 3, maxLevel: null, xpReward: 130, coinReward: 85, itemRewards: [], skillPoints: 2, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 15 },
  { name: "Salsa Shine Specialist", description: "Execute 6 salsa shine sequences", type: "DAILY", category: "SKILL", requirements: { salsa_shines: 6 }, danceStyle: "SALSA", minLevel: 6, maxLevel: null, xpReward: 200, coinReward: 140, itemRewards: [], skillPoints: 4, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 6 },
  { name: "Bachata Dip Master", description: "Perform 4 perfect bachata dips", type: "DAILY", category: "SKILL", requirements: { bachata_dips: 4 }, danceStyle: "BACHATA", minLevel: 7, maxLevel: null, xpReward: 220, coinReward: 150, itemRewards: [], skillPoints: 5, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 4 },
  { name: "Kizomba Saida", description: "Execute 8 kizomba saidas (exits)", type: "DAILY", category: "SKILL", requirements: { kizomba_saidas: 8 }, danceStyle: "KIZOMBA", minLevel: 5, maxLevel: null, xpReward: 170, coinReward: 115, itemRewards: [], skillPoints: 3, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 8 },
  { name: "Zouk Cambre", description: "Perform 3 zouk cambres (back bends)", type: "DAILY", category: "SKILL", requirements: { zouk_cambres: 3 }, danceStyle: "ZOUK", minLevel: 8, maxLevel: null, xpReward: 250, coinReward: 170, itemRewards: [], skillPoints: 6, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 3 },
  { name: "Perfect Timing Warrior", description: "Achieve perfect timing 10 times in battles", type: "DAILY", category: "SKILL", requirements: { perfect_timing_hits: 10 }, danceStyle: null, minLevel: 1, maxLevel: null, xpReward: 120, coinReward: 80, itemRewards: [], skillPoints: 3, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 10 },
  { name: "Social Dance Butterfly", description: "Battle against 5 different opponents", type: "DAILY", category: "SOCIAL", requirements: { unique_opponents_battled: 5 }, danceStyle: null, minLevel: 3, maxLevel: null, xpReward: 100, coinReward: 120, itemRewards: [], skillPoints: 1, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 5 },
  { name: "Energy Conservation", description: "Win 2 battles while maintaining 50% energy", type: "DAILY", category: "STRATEGY", requirements: { energy_efficient_wins: 2 }, danceStyle: null, minLevel: 4, maxLevel: null, xpReward: 140, coinReward: 100, itemRewards: [], skillPoints: 2, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 2 },
  { name: "Combo Specialist", description: "Execute 5 successful combo moves", type: "DAILY", category: "SKILL", requirements: { combo_moves_executed: 5 }, danceStyle: null, minLevel: 5, maxLevel: null, xpReward: 160, coinReward: 110, itemRewards: [], skillPoints: 3, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 5 },
  { name: "Defensive Master", description: "Successfully defend against 8 attacks", type: "DAILY", category: "STRATEGY", requirements: { successful_defenses: 8 }, danceStyle: null, minLevel: 6, maxLevel: null, xpReward: 180, coinReward: 120, itemRewards: [], skillPoints: 4, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 8 },
  { name: "Salsa Rueda Caller", description: "Lead 3 salsa rueda sequences", type: "DAILY", category: "SKILL", requirements: { salsa_rueda_calls: 3 }, danceStyle: "SALSA", minLevel: 10, maxLevel: null, xpReward: 300, coinReward: 200, itemRewards: [], skillPoints: 6, traitPoints: 1, isActive: true, repeatDaily: true, maxProgress: 3 },
  { name: "Bachata Moderna", description: "Execute 5 modern bachata moves", type: "DAILY", category: "SKILL", requirements: { bachata_moderna_moves: 5 }, danceStyle: "BACHATA", minLevel: 8, maxLevel: null, xpReward: 240, coinReward: 160, itemRewards: [], skillPoints: 5, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 5 },
  { name: "Kizomba Tarraxinha", description: "Perform 10 tarraxinha steps", type: "DAILY", category: "SKILL", requirements: { kizomba_tarraxinha: 10 }, danceStyle: "KIZOMBA", minLevel: 6, maxLevel: null, xpReward: 190, coinReward: 130, itemRewards: [], skillPoints: 4, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 10 },
  { name: "Zouk Elastic", description: "Execute 4 zouk elastic moves", type: "DAILY", category: "SKILL", requirements: { zouk_elastic_moves: 4 }, danceStyle: "ZOUK", minLevel: 9, maxLevel: null, xpReward: 270, coinReward: 180, itemRewards: [], skillPoints: 6, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 4 },
  { name: "Cha-Cha New York", description: "Perform 8 cha-cha New York moves", type: "DAILY", category: "SKILL", requirements: { chacha_new_york: 8 }, danceStyle: "CHACHA", minLevel: 4, maxLevel: null, xpReward: 150, coinReward: 100, itemRewards: [], skillPoints: 3, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 8 },
  { name: "Musicality Master", description: "Hit musical accents 12 times", type: "DAILY", category: "SKILL", requirements: { musical_accents: 12 }, danceStyle: null, minLevel: 7, maxLevel: null, xpReward: 210, coinReward: 140, itemRewards: [], skillPoints: 5, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 12 },
  { name: "Footwork Wizard", description: "Execute 15 complex footwork patterns", type: "DAILY", category: "SKILL", requirements: { complex_footwork: 15 }, danceStyle: null, minLevel: 8, maxLevel: null, xpReward: 230, coinReward: 155, itemRewards: [], skillPoints: 5, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 15 },
  { name: "Leading Confidence", description: "Successfully lead 6 complex moves", type: "DAILY", category: "SKILL", requirements: { successful_leads: 6 }, danceStyle: null, minLevel: 9, maxLevel: null, xpReward: 250, coinReward: 170, itemRewards: [], skillPoints: 6, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 6 },
  { name: "Following Grace", description: "Follow 8 complex moves perfectly", type: "DAILY", category: "SKILL", requirements: { perfect_follows: 8 }, danceStyle: null, minLevel: 9, maxLevel: null, xpReward: 250, coinReward: 170, itemRewards: [], skillPoints: 6, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 8 },
  { name: "Spin Control", description: "Execute 10 controlled spins", type: "DAILY", category: "SKILL", requirements: { controlled_spins: 10 }, danceStyle: null, minLevel: 5, maxLevel: null, xpReward: 170, coinReward: 115, itemRewards: [], skillPoints: 4, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 10 },
  { name: "Expression Artist", description: "Show exceptional expression in 4 moves", type: "DAILY", category: "SKILL", requirements: { expressive_moves: 4 }, danceStyle: null, minLevel: 6, maxLevel: null, xpReward: 190, coinReward: 130, itemRewards: [], skillPoints: 4, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 4 },
  { name: "Stamina Endurance", description: "Complete 3 battles without running out of stamina", type: "DAILY", category: "STRATEGY", requirements: { stamina_battles: 3 }, danceStyle: null, minLevel: 7, maxLevel: null, xpReward: 200, coinReward: 135, itemRewards: [], skillPoints: 4, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 3 },
  { name: "Creativity Burst", description: "Use 6 creative improvisation moves", type: "DAILY", category: "SKILL", requirements: { creative_moves: 6 }, danceStyle: null, minLevel: 8, maxLevel: null, xpReward: 220, coinReward: 150, itemRewards: [], skillPoints: 5, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 6 },
  { name: "Charisma Magnet", description: "Win 2 battles through charismatic performance", type: "DAILY", category: "STRATEGY", requirements: { charismatic_wins: 2 }, danceStyle: null, minLevel: 10, maxLevel: null, xpReward: 280, coinReward: 190, itemRewards: [], skillPoints: 6, traitPoints: 1, isActive: true, repeatDaily: true, maxProgress: 2 },
  { name: "Lucky Streak", description: "Win 3 battles with critical hits", type: "DAILY", category: "STRATEGY", requirements: { critical_wins: 3 }, danceStyle: null, minLevel: 5, maxLevel: null, xpReward: 180, coinReward: 120, itemRewards: [], skillPoints: 4, traitPoints: 0, isActive: true, repeatDaily: true, maxProgress: 3 },

  // WEEKLY QUESTS (25 items)
  { name: "Salsa Shine Explorer", description: "Master 5 different salsa shine patterns this week", type: "WEEKLY", category: "EXPLORATION", requirements: { salsa_shine_patterns: 5 }, danceStyle: "SALSA", minLevel: 8, maxLevel: null, xpReward: 500, coinReward: 350, itemRewards: ["salsa_shine_manual"], skillPoints: 8, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 5 },
  { name: "Bachata Sensual Explorer", description: "Learn 4 new bachata sensual techniques", type: "WEEKLY", category: "EXPLORATION", requirements: { bachata_sensual_techniques: 4 }, danceStyle: "BACHATA", minLevel: 6, maxLevel: null, xpReward: 450, coinReward: 300, itemRewards: ["bachata_sensual_guide"], skillPoints: 7, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 4 },
  { name: "Kizomba Flow Master", description: "Achieve perfect flow in 10 kizomba sequences", type: "WEEKLY", category: "SKILL", requirements: { kizomba_perfect_flows: 10 }, danceStyle: "KIZOMBA", minLevel: 7, maxLevel: null, xpReward: 550, coinReward: 400, itemRewards: ["kizomba_flow_crystal"], skillPoints: 9, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 10 },
  { name: "Zouk Lambazouk Specialist", description: "Execute 6 lambazouk moves perfectly", type: "WEEKLY", category: "SKILL", requirements: { zouk_lambazouk_moves: 6 }, danceStyle: "ZOUK", minLevel: 10, maxLevel: null, xpReward: 700, coinReward: 500, itemRewards: ["lambazouk_certificate"], skillPoints: 12, traitPoints: 2, isActive: true, repeatWeekly: true, maxProgress: 6 },
  { name: "Cha-Cha Guapea Master", description: "Perform 12 perfect guapea sequences", type: "WEEKLY", category: "SKILL", requirements: { chacha_guapea: 12 }, danceStyle: "CHACHA", minLevel: 5, maxLevel: null, xpReward: 400, coinReward: 280, itemRewards: ["guapea_trophy"], skillPoints: 6, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 12 },
  { name: "Cross-Training Champion", description: "Win battles in 3 different dance styles", type: "WEEKLY", category: "EXPLORATION", requirements: { different_styles_won: 3 }, danceStyle: null, minLevel: 8, maxLevel: null, xpReward: 500, coinReward: 400, itemRewards: ["style_mastery_token"], skillPoints: 8, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 3 },
  { name: "Equipment Collector", description: "Acquire 3 new pieces of equipment", type: "WEEKLY", category: "EXPLORATION", requirements: { equipment_acquired: 3 }, danceStyle: null, minLevel: 5, maxLevel: null, xpReward: 300, coinReward: 500, itemRewards: ["equipment_voucher"], skillPoints: 5, traitPoints: 0, isActive: true, repeatWeekly: true, maxProgress: 3 },
  { name: "Win Streak Champion", description: "Achieve a 7-battle win streak", type: "WEEKLY", category: "BATTLE", requirements: { win_streak: 7 }, danceStyle: null, minLevel: 10, maxLevel: null, xpReward: 800, coinReward: 600, itemRewards: ["champion_crown"], skillPoints: 12, traitPoints: 2, isActive: true, repeatWeekly: true, maxProgress: 1 },
  { name: "Salsa Casino Rueda", description: "Complete 3 casino rueda sequences", type: "WEEKLY", category: "SKILL", requirements: { casino_rueda_sequences: 3 }, danceStyle: "SALSA", minLevel: 12, maxLevel: null, xpReward: 600, coinReward: 450, itemRewards: ["casino_rueda_badge"], skillPoints: 10, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 3 },
  { name: "Bachata Dominican Style", description: "Master 4 Dominican bachata moves", type: "WEEKLY", category: "SKILL", requirements: { bachata_dominican_moves: 4 }, danceStyle: "BACHATA", minLevel: 9, maxLevel: null, xpReward: 520, coinReward: 380, itemRewards: ["dominican_flag"], skillPoints: 8, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 4 },
  { name: "Kizomba Angolan Roots", description: "Perform 5 traditional Angolan kizomba moves", type: "WEEKLY", category: "SKILL", requirements: { kizomba_angolan_moves: 5 }, danceStyle: "KIZOMBA", minLevel: 8, maxLevel: null, xpReward: 480, coinReward: 350, itemRewards: ["angolan_heritage_token"], skillPoints: 7, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 5 },
  { name: "Zouk Brazilian Power", description: "Execute 6 powerful Brazilian zouk moves", type: "WEEKLY", category: "SKILL", requirements: { zouk_brazilian_power: 6 }, danceStyle: "ZOUK", minLevel: 11, maxLevel: null, xpReward: 650, coinReward: 480, itemRewards: ["brazilian_power_gem"], skillPoints: 11, traitPoints: 2, isActive: true, repeatWeekly: true, maxProgress: 6 },
  { name: "Cha-Cha Cuban Motion", description: "Perfect 8 Cuban motion sequences", type: "WEEKLY", category: "SKILL", requirements: { chacha_cuban_motion: 8 }, danceStyle: "CHACHA", minLevel: 6, maxLevel: null, xpReward: 420, coinReward: 300, itemRewards: ["cuban_motion_guide"], skillPoints: 6, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 8 },
  { name: "Perfect Timing Week", description: "Achieve perfect timing 50 times this week", type: "WEEKLY", category: "SKILL", requirements: { perfect_timing_week: 50 }, danceStyle: null, minLevel: 7, maxLevel: null, xpReward: 600, coinReward: 400, itemRewards: ["timing_master_watch"], skillPoints: 10, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 50 },
  { name: "Social Dance Network", description: "Battle with 15 different opponents", type: "WEEKLY", category: "SOCIAL", requirements: { unique_opponents_week: 15 }, danceStyle: null, minLevel: 8, maxLevel: null, xpReward: 450, coinReward: 350, itemRewards: ["social_butterfly_badge"], skillPoints: 6, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 15 },
  { name: "Combo Mastery Week", description: "Execute 25 successful combo moves", type: "WEEKLY", category: "SKILL", requirements: { combo_moves_week: 25 }, danceStyle: null, minLevel: 9, maxLevel: null, xpReward: 550, coinReward: 400, itemRewards: ["combo_master_gloves"], skillPoints: 9, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 25 },
  { name: "Defensive Specialist", description: "Successfully defend 30 attacks this week", type: "WEEKLY", category: "STRATEGY", requirements: { defenses_week: 30 }, danceStyle: null, minLevel: 10, maxLevel: null, xpReward: 500, coinReward: 380, itemRewards: ["defensive_shield"], skillPoints: 8, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 30 },
  { name: "Energy Efficiency Expert", description: "Win 10 battles while maintaining 60% energy", type: "WEEKLY", category: "STRATEGY", requirements: { energy_efficient_week: 10 }, danceStyle: null, minLevel: 11, maxLevel: null, xpReward: 600, coinReward: 450, itemRewards: ["energy_crystal"], skillPoints: 10, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 10 },
  { name: "Musicality Virtuoso", description: "Hit 40 musical accents this week", type: "WEEKLY", category: "SKILL", requirements: { musical_accents_week: 40 }, danceStyle: null, minLevel: 12, maxLevel: null, xpReward: 650, coinReward: 500, itemRewards: ["virtuoso_baton"], skillPoints: 11, traitPoints: 2, isActive: true, repeatWeekly: true, maxProgress: 40 },
  { name: "Footwork Phenomenon", description: "Execute 50 complex footwork patterns", type: "WEEKLY", category: "SKILL", requirements: { complex_footwork_week: 50 }, danceStyle: null, minLevel: 13, maxLevel: null, xpReward: 700, coinReward: 550, itemRewards: ["footwork_legend_shoes"], skillPoints: 12, traitPoints: 2, isActive: true, repeatWeekly: true, maxProgress: 50 },
  { name: "Leadership Excellence", description: "Successfully lead 20 complex moves", type: "WEEKLY", category: "SKILL", requirements: { successful_leads_week: 20 }, danceStyle: null, minLevel: 14, maxLevel: null, xpReward: 750, coinReward: 600, itemRewards: ["leadership_crown"], skillPoints: 13, traitPoints: 2, isActive: true, repeatWeekly: true, maxProgress: 20 },
  { name: "Following Perfection", description: "Follow 25 complex moves perfectly", type: "WEEKLY", category: "SKILL", requirements: { perfect_follows_week: 25 }, danceStyle: null, minLevel: 14, maxLevel: null, xpReward: 750, coinReward: 600, itemRewards: ["perfection_pendant"], skillPoints: 13, traitPoints: 2, isActive: true, repeatWeekly: true, maxProgress: 25 },
  { name: "Spin Mastery", description: "Execute 35 controlled spins this week", type: "WEEKLY", category: "SKILL", requirements: { controlled_spins_week: 35 }, danceStyle: null, minLevel: 10, maxLevel: null, xpReward: 550, coinReward: 420, itemRewards: ["spin_master_ring"], skillPoints: 9, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 35 },
  { name: "Expression Virtuoso", description: "Show exceptional expression in 15 moves", type: "WEEKLY", category: "SKILL", requirements: { expressive_moves_week: 15 }, danceStyle: null, minLevel: 11, maxLevel: null, xpReward: 600, coinReward: 450, itemRewards: ["expression_mask"], skillPoints: 10, traitPoints: 1, isActive: true, repeatWeekly: true, maxProgress: 15 },
  { name: "Stamina Marathon", description: "Complete 12 battles without stamina depletion", type: "WEEKLY", category: "STRATEGY", requirements: { stamina_battles_week: 12 }, danceStyle: null, minLevel: 12, maxLevel: null, xpReward: 650, coinReward: 500, itemRewards: ["marathon_medal"], skillPoints: 11, traitPoints: 2, isActive: true, repeatWeekly: true, maxProgress: 12 },

  // MONTHLY QUESTS (20 items)
  { name: "Salsa Congress Champion", description: "Win 50 salsa battles this month", type: "MONTHLY", category: "BATTLE", requirements: { salsa_battles_month: 50 }, danceStyle: "SALSA", minLevel: 15, maxLevel: null, xpReward: 2000, coinReward: 1500, itemRewards: ["salsa_congress_trophy", "legendary_salsa_shoes"], skillPoints: 25, traitPoints: 5, isActive: true, maxProgress: 50 },
  { name: "Bachata Festival King/Queen", description: "Win 40 bachata battles this month", type: "MONTHLY", category: "BATTLE", requirements: { bachata_battles_month: 40 }, danceStyle: "BACHATA", minLevel: 15, maxLevel: null, xpReward: 1800, coinReward: 1300, itemRewards: ["bachata_crown", "legendary_bachata_dress"], skillPoints: 22, traitPoints: 4, isActive: true, maxProgress: 40 },
  { name: "Kizomba Soul Master", description: "Win 35 kizomba battles this month", type: "MONTHLY", category: "BATTLE", requirements: { kizomba_battles_month: 35 }, danceStyle: "KIZOMBA", minLevel: 15, maxLevel: null, xpReward: 1600, coinReward: 1200, itemRewards: ["soul_master_robe", "kizomba_spirit_orb"], skillPoints: 20, traitPoints: 4, isActive: true, maxProgress: 35 },
  { name: "Zouk Magic Wizard", description: "Win 30 zouk battles this month", type: "MONTHLY", category: "BATTLE", requirements: { zouk_battles_month: 30 }, danceStyle: "ZOUK", minLevel: 15, maxLevel: null, xpReward: 1500, coinReward: 1100, itemRewards: ["magic_wizard_cloak", "zouk_power_staff"], skillPoints: 18, traitPoints: 3, isActive: true, maxProgress: 30 },
  { name: "Cha-Cha Lightning Lord", description: "Win 45 cha-cha battles this month", type: "MONTHLY", category: "BATTLE", requirements: { chacha_battles_month: 45 }, danceStyle: "CHACHA", minLevel: 15, maxLevel: null, xpReward: 1900, coinReward: 1400, itemRewards: ["lightning_lord_vest", "electric_dance_boots"], skillPoints: 24, traitPoints: 4, isActive: true, maxProgress: 45 },
  { name: "Dance Floor Dominator", description: "Win 100 battles this month", type: "MONTHLY", category: "BATTLE", requirements: { battles_won_month: 100 }, danceStyle: null, minLevel: 20, maxLevel: null, xpReward: 3000, coinReward: 2500, itemRewards: ["dominator_crown", "legendary_equipment_box"], skillPoints: 40, traitPoints: 8, isActive: true, maxProgress: 100 },
  { name: "Perfect Timing Legend", description: "Achieve perfect timing 200 times this month", type: "MONTHLY", category: "SKILL", requirements: { perfect_timing_month: 200 }, danceStyle: null, minLevel: 18, maxLevel: null, xpReward: 2500, coinReward: 2000, itemRewards: ["timing_legend_watch", "precision_crystal"], skillPoints: 35, traitPoints: 6, isActive: true, maxProgress: 200 },
  { name: "Social Dance Ambassador", description: "Battle with 50 different opponents this month", type: "MONTHLY", category: "SOCIAL", requirements: { unique_opponents_month: 50 }, danceStyle: null, minLevel: 16, maxLevel: null, xpReward: 2000, coinReward: 1500, itemRewards: ["ambassador_badge", "social_network_token"], skillPoints: 25, traitPoints: 5, isActive: true, maxProgress: 50 },
  { name: "Combo Virtuoso", description: "Execute 100 successful combo moves this month", type: "MONTHLY", category: "SKILL", requirements: { combo_moves_month: 100 }, danceStyle: null, minLevel: 17, maxLevel: null, xpReward: 2200, coinReward: 1700, itemRewards: ["virtuoso_gloves", "combo_mastery_gem"], skillPoints: 30, traitPoints: 5, isActive: true, maxProgress: 100 },
  { name: "Equipment Master", description: "Collect 15 pieces of equipment this month", type: "MONTHLY", category: "EXPLORATION", requirements: { equipment_collected_month: 15 }, danceStyle: null, minLevel: 12, maxLevel: null, xpReward: 1500, coinReward: 2000, itemRewards: ["equipment_master_toolkit", "rare_equipment_box"], skillPoints: 20, traitPoints: 3, isActive: true, maxProgress: 15 },
  { name: "Defensive Fortress", description: "Successfully defend 150 attacks this month", type: "MONTHLY", category: "STRATEGY", requirements: { defenses_month: 150 }, danceStyle: null, minLevel: 19, maxLevel: null, xpReward: 2300, coinReward: 1800, itemRewards: ["fortress_shield", "defensive_mastery_armor"], skillPoints: 32, traitPoints: 6, isActive: true, maxProgress: 150 },
  { name: "Energy Efficiency Master", description: "Win 40 battles while maintaining 70% energy", type: "MONTHLY", category: "STRATEGY", requirements: { energy_efficient_month: 40 }, danceStyle: null, minLevel: 20, maxLevel: null, xpReward: 2400, coinReward: 1900, itemRewards: ["efficiency_crystal", "energy_mastery_amulet"], skillPoints: 33, traitPoints: 6, isActive: true, maxProgress: 40 },
  { name: "Musicality Maestro", description: "Hit 150 musical accents this month", type: "MONTHLY", category: "SKILL", requirements: { musical_accents_month: 150 }, danceStyle: null, minLevel: 21, maxLevel: null, xpReward: 2600, coinReward: 2100, itemRewards: ["maestro_baton", "musical_mastery_crown"], skillPoints: 36, traitPoints: 7, isActive: true, maxProgress: 150 },
  { name: "Footwork Legend", description: "Execute 200 complex footwork patterns this month", type: "MONTHLY", category: "SKILL", requirements: { complex_footwork_month: 200 }, danceStyle: null, minLevel: 22, maxLevel: null, xpReward: 2800, coinReward: 2300, itemRewards: ["legend_shoes", "footwork_mastery_scroll"], skillPoints: 38, traitPoints: 7, isActive: true, maxProgress: 200 },
  { name: "Leadership Supreme", description: "Successfully lead 80 complex moves this month", type: "MONTHLY", category: "SKILL", requirements: { successful_leads_month: 80 }, danceStyle: null, minLevel: 23, maxLevel: null, xpReward: 3000, coinReward: 2500, itemRewards: ["supreme_crown", "leadership_scepter"], skillPoints: 40, traitPoints: 8, isActive: true, maxProgress: 80 },
  { name: "Following Perfection Master", description: "Follow 100 complex moves perfectly this month", type: "MONTHLY", category: "SKILL", requirements: { perfect_follows_month: 100 }, danceStyle: null, minLevel: 23, maxLevel: null, xpReward: 3000, coinReward: 2500, itemRewards: ["perfection_tiara", "following_mastery_ring"], skillPoints: 40, traitPoints: 8, isActive: true, maxProgress: 100 },
  { name: "Spin Phenomenon", description: "Execute 150 controlled spins this month", type: "MONTHLY", category: "SKILL", requirements: { controlled_spins_month: 150 }, danceStyle: null, minLevel: 20, maxLevel: null, xpReward: 2400, coinReward: 1900, itemRewards: ["phenomenon_ring", "spin_mastery_boots"], skillPoints: 33, traitPoints: 6, isActive: true, maxProgress: 150 },
  { name: "Expression Grandmaster", description: "Show exceptional expression in 60 moves this month", type: "MONTHLY", category: "SKILL", requirements: { expressive_moves_month: 60 }, danceStyle: null, minLevel: 21, maxLevel: null, xpReward: 2500, coinReward: 2000, itemRewards: ["grandmaster_mask", "expression_mastery_cloak"], skillPoints: 35, traitPoints: 6, isActive: true, maxProgress: 60 },
  { name: "Stamina Immortal", description: "Complete 50 battles without stamina depletion this month", type: "MONTHLY", category: "STRATEGY", requirements: { stamina_battles_month: 50 }, danceStyle: null, minLevel: 24, maxLevel: null, xpReward: 3200, coinReward: 2700, itemRewards: ["immortal_amulet", "stamina_infinity_gem"], skillPoints: 42, traitPoints: 8, isActive: true, maxProgress: 50 },
  { name: "Universal Dance Master", description: "Win 20 battles in each of 5 different dance styles", type: "MONTHLY", category: "ACHIEVEMENT", requirements: { universal_mastery: 20 }, danceStyle: null, minLevel: 25, maxLevel: null, xpReward: 5000, coinReward: 4000, itemRewards: ["universal_master_crown", "dance_infinity_stone", "mythic_equipment_box"], skillPoints: 60, traitPoints: 12, isActive: true, maxProgress: 20 },

  // STORY QUESTS (15 items)
  { name: "The First Dance", description: "Begin your dance journey by winning your first battle", type: "STORY", category: "ACHIEVEMENT", requirements: { battles_won: 1 }, danceStyle: null, minLevel: 1, maxLevel: 5, xpReward: 100, coinReward: 50, itemRewards: ["beginner_shoes"], skillPoints: 2, traitPoints: 1, isActive: true, maxProgress: 1 },
  { name: "Salsa Awakening", description: "Discover your salsa passion by winning 5 salsa battles", type: "STORY", category: "ACHIEVEMENT", requirements: { salsa_battles_won: 5 }, danceStyle: "SALSA", minLevel: 3, maxLevel: 10, xpReward: 300, coinReward: 200, itemRewards: ["salsa_fire_bracelet"], skillPoints: 5, traitPoints: 2, isActive: true, maxProgress: 5 },
  { name: "Bachata Soul Connection", description: "Feel the bachata soul by winning 5 bachata battles", type: "STORY", category: "ACHIEVEMENT", requirements: { bachata_battles_won: 5 }, danceStyle: "BACHATA", minLevel: 3, maxLevel: 10, xpReward: 300, coinReward: 200, itemRewards: ["bachata_heart_necklace"], skillPoints: 5, traitPoints: 2, isActive: true, maxProgress: 5 },
  { name: "Kizomba Flow Discovery", description: "Find your kizomba flow by winning 5 kizomba battles", type: "STORY", category: "ACHIEVEMENT", requirements: { kizomba_battles_won: 5 }, danceStyle: "KIZOMBA", minLevel: 3, maxLevel: 10, xpReward: 300, coinReward: 200, itemRewards: ["kizomba_flow_ring"], skillPoints: 5, traitPoints: 2, isActive: true, maxProgress: 5 },
  { name: "Zouk Magic Revelation", description: "Unlock zouk magic by winning 5 zouk battles", type: "STORY", category: "ACHIEVEMENT", requirements: { zouk_battles_won: 5 }, danceStyle: "ZOUK", minLevel: 3, maxLevel: 10, xpReward: 300, coinReward: 200, itemRewards: ["zouk_magic_amulet"], skillPoints: 5, traitPoints: 2, isActive: true, maxProgress: 5 },
  { name: "Rising Star", description: "Reach level 10 to become a rising star", type: "STORY", category: "ACHIEVEMENT", requirements: { level_reached: 10 }, danceStyle: null, minLevel: 1, maxLevel: 10, xpReward: 500, coinReward: 300, itemRewards: ["rising_star_badge", "rare_equipment_box"], skillPoints: 10, traitPoints: 3, isActive: true, maxProgress: 1 },
  { name: "Style Specialist", description: "Master your first dance style by winning 25 battles in it", type: "STORY", category: "ACHIEVEMENT", requirements: { style_battles_won: 25 }, danceStyle: null, minLevel: 8, maxLevel: null, xpReward: 800, coinReward: 500, itemRewards: ["specialist_certificate", "style_specific_equipment"], skillPoints: 15, traitPoints: 4, isActive: true, maxProgress: 25 },
  { name: "Equipment Enthusiast", description: "Collect your first 10 pieces of equipment", type: "STORY", category: "ACHIEVEMENT", requirements: { equipment_collected: 10 }, danceStyle: null, minLevel: 5, maxLevel: 15, xpReward: 600, coinReward: 800, itemRewards: ["equipment_collector_badge"], skillPoints: 8, traitPoints: 2, isActive: true, maxProgress: 10 },
  { name: "Social Butterfly", description: "Battle with 25 different opponents", type: "STORY", category: "ACHIEVEMENT", requirements: { unique_opponents_total: 25 }, danceStyle: null, minLevel: 10, maxLevel: 20, xpReward: 1000, coinReward: 600, itemRewards: ["social_butterfly_wings"], skillPoints: 12, traitPoints: 3, isActive: true, maxProgress: 25 },
  { name: "Combo Pioneer", description: "Execute your first 50 combo moves", type: "STORY", category: "ACHIEVEMENT", requirements: { combo_moves_total: 50 }, danceStyle: null, minLevel: 7, maxLevel: 15, xpReward: 700, coinReward: 400, itemRewards: ["combo_pioneer_gloves"], skillPoints: 10, traitPoints: 2, isActive: true, maxProgress: 50 },
  { name: "Perfect Timing Prodigy", description: "Achieve perfect timing 100 times total", type: "STORY", category: "ACHIEVEMENT", requirements: { perfect_timing_total: 100 }, danceStyle: null, minLevel: 5, maxLevel: 20, xpReward: 1000, coinReward: 700, itemRewards: ["timing_prodigy_watch"], skillPoints: 15, traitPoints: 3, isActive: true, maxProgress: 100 },
  { name: "Dance Floor Veteran", description: "Reach level 20 to become a veteran", type: "STORY", category: "ACHIEVEMENT", requirements: { level_reached: 20 }, danceStyle: null, minLevel: 15, maxLevel: 20, xpReward: 1500, coinReward: 1000, itemRewards: ["veteran_badge", "epic_equipment_box"], skillPoints: 20, traitPoints: 5, isActive: true, maxProgress: 1 },
  { name: "Multi-Style Master", description: "Win 15 battles in 3 different dance styles", type: "STORY", category: "ACHIEVEMENT", requirements: { multi_style_mastery: 15 }, danceStyle: null, minLevel: 12, maxLevel: 25, xpReward: 1200, coinReward: 800, itemRewards: ["multi_style_crown"], skillPoints: 18, traitPoints: 4, isActive: true, maxProgress: 15 },
  { name: "Dance Legend", description: "Reach level 30 to become a legend", type: "STORY", category: "ACHIEVEMENT", requirements: { level_reached: 30 }, danceStyle: null, minLevel: 25, maxLevel: 30, xpReward: 3000, coinReward: 2000, itemRewards: ["legend_trophy", "legendary_equipment_box"], skillPoints: 40, traitPoints: 8, isActive: true, maxProgress: 1 },
  { name: "Dance Immortality", description: "Reach the ultimate level 50", type: "STORY", category: "ACHIEVEMENT", requirements: { level_reached: 50 }, danceStyle: null, minLevel: 40, maxLevel: 50, xpReward: 10000, coinReward: 5000, itemRewards: ["immortality_crown", "mythic_equipment_set", "universal_dance_spirit"], skillPoints: 100, traitPoints: 20, isActive: true, maxProgress: 1 },

  // SPECIAL QUESTS (10 items)
  { name: "Carnival Celebration", description: "Win 20 battles during carnival season", type: "SPECIAL", category: "EVENT", requirements: { carnival_battles: 20 }, danceStyle: null, minLevel: 8, maxLevel: null, xpReward: 800, coinReward: 600, itemRewards: ["carnival_mask", "samba_feathers"], skillPoints: 12, traitPoints: 2, isActive: true, maxProgress: 20 },
  { name: "Valentine's Dance Romance", description: "Win 14 battles with romantic moves", type: "SPECIAL", category: "EVENT", requirements: { romantic_battles: 14 }, danceStyle: null, minLevel: 10, maxLevel: null, xpReward: 700, coinReward: 500, itemRewards: ["valentine_rose", "love_potion"], skillPoints: 10, traitPoints: 2, isActive: true, maxProgress: 14 },
  { name: "Summer Salsa Festival", description: "Win 30 salsa battles during summer", type: "SPECIAL", category: "EVENT", requirements: { summer_salsa_battles: 30 }, danceStyle: "SALSA", minLevel: 12, maxLevel: null, xpReward: 1200, coinReward: 900, itemRewards: ["summer_salsa_crown", "festival_shoes"], skillPoints: 18, traitPoints: 3, isActive: true, maxProgress: 30 },
  { name: "New Year Resolution", description: "Win 365 battles in a year", type: "SPECIAL", category: "ACHIEVEMENT", requirements: { yearly_battles: 365 }, danceStyle: null, minLevel: 15, maxLevel: null, xpReward: 5000, coinReward: 3650, itemRewards: ["resolution_trophy", "yearly_champion_crown"], skillPoints: 50, traitPoints: 10, isActive: true, maxProgress: 365 },
  { name: "Halloween Dance Spook", description: "Win 31 battles with spooky moves", type: "SPECIAL", category: "EVENT", requirements: { spooky_battles: 31 }, danceStyle: null, minLevel: 13, maxLevel: null, xpReward: 1000, coinReward: 750, itemRewards: ["spooky_mask", "halloween_costume"], skillPoints: 15, traitPoints: 3, isActive: true, maxProgress: 31 },
  { name: "Christmas Dance Magic", description: "Win 25 battles with magical moves", type: "SPECIAL", category: "EVENT", requirements: { magical_battles: 25 }, danceStyle: null, minLevel: 11, maxLevel: null, xpReward: 900, coinReward: 675, itemRewards: ["christmas_star", "magic_wand"], skillPoints: 13, traitPoints: 2, isActive: true, maxProgress: 25 },
  { name: "Tournament Preparation", description: "Prepare for tournaments by winning 15 battles", type: "SPECIAL", category: "BATTLE", requirements: { battles_won: 15 }, danceStyle: null, minLevel: 12, maxLevel: null, xpReward: 600, coinReward: 400, itemRewards: ["tournament_ticket"], skillPoints: 10, traitPoints: 2, isActive: true, maxProgress: 15 },
  { name: "Equipment Mastery", description: "Use equipment special effects 20 times", type: "SPECIAL", category: "SKILL", requirements: { equipment_effects_used: 20 }, danceStyle: null, minLevel: 8, maxLevel: null, xpReward: 400, coinReward: 300, itemRewards: ["mastery_gem"], skillPoints: 6, traitPoints: 1, isActive: true, maxProgress: 20 },
  { name: "World Dance Day", description: "Celebrate by winning battles in all 5 dance styles", type: "SPECIAL", category: "EVENT", requirements: { world_dance_styles: 5 }, danceStyle: null, minLevel: 20, maxLevel: null, xpReward: 2000, coinReward: 1500, itemRewards: ["world_dance_trophy", "global_unity_badge"], skillPoints: 25, traitPoints: 5, isActive: true, maxProgress: 5 },
  { name: "Midnight Dance Challenge", description: "Win 12 battles between midnight and 6 AM", type: "SPECIAL", category: "CHALLENGE", requirements: { midnight_battles: 12 }, danceStyle: null, minLevel: 14, maxLevel: null, xpReward: 800, coinReward: 600, itemRewards: ["midnight_crown", "night_owl_badge"], skillPoints: 12, traitPoints: 2, isActive: true, maxProgress: 12 }
];

// Function to map old categories to new QuestCategory enum values
function mapCategory(oldCategory) {
  const categoryMap = {
    'BATTLE': 'BATTLE',
    'SKILL': 'SKILL_DEVELOPMENT', 
    'SOCIAL': 'SOCIAL',
    'STRATEGY': 'BATTLE',
    'EXPLORATION': 'EXPLORATION',
    'ACHIEVEMENT': 'ACHIEVEMENT',
    'EVENT': 'ACHIEVEMENT',
    'CHALLENGE': 'BATTLE'
  };
  return categoryMap[oldCategory] || 'ACHIEVEMENT';
}

async function seedQuests() {
  try {
    logger.info('🌱 Starting comprehensive quests seeding...');
    
    // First, delete all existing quests
    await prisma.quest.deleteMany({});
    logger.info('🗑️ Cleared existing quests');
    
    // Then create all new quests
    for (const quest of enhancedQuests) {
      await prisma.quest.create({
        data: {
          title: quest.name,
          description: quest.description,
          type: quest.type,
          category: mapCategory(quest.category),
          targetType: "BATTLE_WINS", // Default target type
          targetValue: quest.maxProgress,
          minLevel: quest.minLevel,
          maxLevel: quest.maxLevel,
          rewardXp: quest.xpReward,
          rewardCoins: quest.coinReward,
          rewardSkillPoints: quest.skillPoints,
          rewardTraitPoints: quest.traitPoints,
          specialRewards: quest.itemRewards,
          isActive: quest.isActive,
          repeatType: quest.repeatDaily ? "DAILY" : quest.repeatWeekly ? "WEEKLY" : null
        }
      });
    }
    
    logger.info(`✅ Successfully seeded ${enhancedQuests.length} comprehensive quests`);
  } catch (error) {
    logger.error('❌ Error seeding comprehensive quests:', error);
    throw error;
  }
}

module.exports = { seedQuests }; 