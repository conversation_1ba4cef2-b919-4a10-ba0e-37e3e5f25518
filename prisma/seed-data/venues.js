const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const venues = [
  // Dance Academies (4 venues)
  {
    name: "Elite Salsa Academy",
    type: "DANCE_ACADEMY",
    description: "Premier salsa training facility with world-class instructors",
    location: "Downtown Dance District",
    coordinates: { x: 200, y: 150 },
    isHidden: false,
    unlockLevel: 1,
    specialties: ["SALSA"],
    difficulty: 3,
    rewards: {
      bonusXp: 50,
      coins: 25,
      specialMoves: ["Advanced Salsa Techniques"],
      skillBonus: { TIMING: 5, FOOTWORK: 3 }
    }
  },
  {
    name: "Bachata Soul Studio",
    type: "DANCE_ACADEMY",
    description: "Intimate studio specializing in sensual bachata movements",
    location: "Romantic Quarter",
    coordinates: { x: 350, y: 200 },
    isHidden: false,
    unlockLevel: 2,
    specialties: ["BACHATA"],
    difficulty: 4,
    rewards: {
      bonusXp: 60,
      coins: 30,
      specialMoves: ["Sensual Bachata Flow"],
      skillBonus: { EXPRESSION: 5, MUSICALITY: 3 }
    }
  },
  {
    name: "Kizomba Connection Center",
    type: "DANCE_ACADEMY",
    description: "Master the art of connection through Kizomba",
    location: "Cultural Arts District",
    coordinates: { x: 500, y: 180 },
    isHidden: false,
    unlockLevel: 3,
    specialties: ["KIZOMBA"],
    difficulty: 5,
    rewards: {
      bonusXp: 70,
      coins: 35,
      specialMoves: ["Perfect Embrace Technique"],
      skillBonus: { LEADING: 4, FOLLOWING: 4 }
    }
  },
  {
    name: "Zouk Magic Institute",
    type: "DANCE_ACADEMY",
    description: "Learn the flowing magic of Brazilian Zouk",
    location: "Artistic Heights",
    coordinates: { x: 150, y: 300 },
    isHidden: false,
    unlockLevel: 4,
    specialties: ["ZOUK"],
    difficulty: 6,
    rewards: {
      bonusXp: 80,
      coins: 40,
      specialMoves: ["Zouk Levitation"],
      skillBonus: { CREATIVITY: 5, SPINS: 4 }
    }
  },

  // Underground Clubs (2 venues)
  {
    name: "The Underground Salsa Cave",
    type: "UNDERGROUND_CLUB",
    description: "Hidden underground venue where salsa legends are born",
    location: "Secret Underground Network",
    coordinates: { x: 100, y: 450 },
    isHidden: true,
    unlockLevel: 6,
    specialties: ["SALSA", "MERENGUE"],
    difficulty: 8,
    rewards: {
      bonusXp: 120,
      coins: 75,
      specialMoves: ["Underground Fire Combo"],
      skillBonus: { STAMINA: 6, CHARISMA: 5 },
      specialEquipment: ["Underground Champion Badge"]
    }
  },
  {
    name: "Midnight Bachata Lounge",
    type: "UNDERGROUND_CLUB",
    description: "Exclusive late-night bachata venue for advanced dancers",
    location: "Hidden Nightlife District",
    coordinates: { x: 600, y: 400 },
    isHidden: true,
    unlockLevel: 7,
    specialties: ["BACHATA", "KIZOMBA"],
    difficulty: 9,
    rewards: {
      bonusXp: 150,
      coins: 90,
      specialMoves: ["Midnight Sensual Fusion"],
      skillBonus: { EXPRESSION: 7, MUSICALITY: 6 },
      specialEquipment: ["Midnight Dancer Crown"]
    }
  },

  // Competition Halls (2 venues)
  {
    name: "Grand Ballroom Championship",
    type: "COMPETITION_HALL",
    description: "Prestigious venue hosting international dance competitions",
    location: "Championship Plaza",
    coordinates: { x: 400, y: 100 },
    isHidden: false,
    unlockLevel: 5,
    specialties: ["SALSA", "BACHATA", "CHACHA"],
    difficulty: 7,
    rewards: {
      bonusXp: 100,
      coins: 60,
      specialMoves: ["Championship Combo"],
      skillBonus: { TIMING: 5, FOOTWORK: 5, CHARISMA: 4 },
      trophies: 3
    }
  },
  {
    name: "International Dance Arena",
    type: "COMPETITION_HALL",
    description: "World-class arena for the most elite dance battles",
    location: "Global Dance Center",
    coordinates: { x: 550, y: 250 },
    isHidden: false,
    unlockLevel: 8,
    specialties: ["SALSA", "BACHATA", "KIZOMBA", "ZOUK", "CHACHA"],
    difficulty: 10,
    rewards: {
      bonusXp: 200,
      coins: 150,
      specialMoves: ["World Champion Technique"],
      skillBonus: { TIMING: 8, FOOTWORK: 8, CHARISMA: 7 },
      trophies: 10,
      titles: ["International Dance Champion"]
    }
  },

  // Festival Grounds (2 venues)
  {
    name: "Summer Salsa Festival",
    type: "FESTIVAL_GROUND",
    description: "Vibrant outdoor festival celebrating salsa culture",
    location: "Festival Park",
    coordinates: { x: 250, y: 350 },
    isHidden: false,
    unlockLevel: 3,
    specialties: ["SALSA", "MERENGUE", "CUMBIA"],
    difficulty: 4,
    rewards: {
      bonusXp: 75,
      coins: 45,
      specialMoves: ["Festival Fire Dance"],
      skillBonus: { STAMINA: 4, CHARISMA: 5 },
      festivalTokens: 5
    }
  },
  {
    name: "Tropical Dance Paradise",
    type: "FESTIVAL_GROUND",
    description: "Beach festival with all Latin dance styles",
    location: "Paradise Beach",
    coordinates: { x: 650, y: 500 },
    isHidden: false,
    unlockLevel: 6,
    specialties: ["BACHATA", "REGGAETON", "CUMBIA"],
    difficulty: 6,
    rewards: {
      bonusXp: 90,
      coins: 55,
      specialMoves: ["Tropical Fusion"],
      skillBonus: { EXPRESSION: 5, MUSICALITY: 4 },
      festivalTokens: 8
    }
  },

  // Mystical Studios (3 venues)
  {
    name: "The Enchanted Dance Chamber",
    type: "MYSTICAL_STUDIO",
    description: "Magical venue where dance transcends reality",
    location: "Mystical Realm",
    coordinates: { x: 50, y: 250 },
    isHidden: true,
    unlockLevel: 10,
    specialties: ["UNIVERSAL"],
    difficulty: 10,
    rewards: {
      bonusXp: 300,
      coins: 200,
      specialMoves: ["Mystical Dance Fusion"],
      skillBonus: { CREATIVITY: 10, EXPRESSION: 8, CHARISMA: 7 },
      magicalPowers: ["Dance Transcendence"],
      titles: ["Mystical Dance Master"]
    }
  },
  {
    name: "Rhythm Sanctuary",
    type: "MYSTICAL_STUDIO",
    description: "Sacred space where dancers commune with rhythm itself",
    location: "Sacred Grove",
    coordinates: { x: 700, y: 150 },
    isHidden: true,
    unlockLevel: 9,
    specialties: ["UNIVERSAL"],
    difficulty: 9,
    rewards: {
      bonusXp: 250,
      coins: 175,
      specialMoves: ["Rhythm Communion"],
      skillBonus: { MUSICALITY: 8, TIMING: 7, EXPRESSION: 6 },
      spiritualPowers: ["Rhythm Mastery"]
    }
  },
  {
    name: "Elemental Dance Studio",
    type: "MYSTICAL_STUDIO",
    description: "Harness the power of the elements through dance",
    location: "Elemental Plane",
    coordinates: { x: 400, y: 450 },
    isHidden: true,
    unlockLevel: 8,
    specialties: ["SALSA", "ZOUK"],
    difficulty: 8,
    rewards: {
      bonusXp: 180,
      coins: 120,
      specialMoves: ["Elemental Fire Dance", "Elemental Flow"],
      skillBonus: { CREATIVITY: 6, STAMINA: 5 },
      elementalPowers: ["Fire", "Water"]
    }
  },

  // Practice Rooms (2 venues)
  {
    name: "Beginner's Haven",
    type: "PRACTICE_ROOM",
    description: "Safe space for new dancers to learn and practice",
    location: "Learning Center",
    coordinates: { x: 300, y: 50 },
    isHidden: false,
    unlockLevel: 1,
    specialties: ["SALSA", "BACHATA"],
    difficulty: 1,
    rewards: {
      bonusXp: 25,
      coins: 15,
      specialMoves: ["Basic Foundation"],
      skillBonus: { TIMING: 2, FOOTWORK: 2 },
      encouragement: true
    }
  },
  {
    name: "Advanced Training Facility",
    type: "PRACTICE_ROOM",
    description: "High-tech training center for serious dancers",
    location: "Professional District",
    coordinates: { x: 450, y: 350 },
    isHidden: false,
    unlockLevel: 5,
    specialties: ["SALSA", "BACHATA", "KIZOMBA", "ZOUK"],
    difficulty: 6,
    rewards: {
      bonusXp: 85,
      coins: 50,
      specialMoves: ["Advanced Technique Mastery"],
      skillBonus: { TIMING: 4, FOOTWORK: 4, SPINS: 3 },
      trainingBonus: 2
    }
  },

  // Unique Venues (3 venues)
  {
    name: "Skyline Salsa Terrace",
    type: "ROOFTOP_TERRACE",
    description: "Dance under the stars on this exclusive rooftop venue",
    location: "City Skyline",
    coordinates: { x: 500, y: 50 },
    isHidden: false,
    unlockLevel: 4,
    specialties: ["SALSA", "CHACHA"],
    difficulty: 5,
    rewards: {
      bonusXp: 70,
      coins: 40,
      specialMoves: ["Skyline Spin Spectacular"],
      skillBonus: { SPINS: 5, CHARISMA: 4 },
      cityViews: true
    }
  },
  {
    name: "Sunset Beach Club",
    type: "BEACH_CLUB",
    description: "Dance on the sand as the sun sets over the ocean",
    location: "Paradise Coast",
    coordinates: { x: 750, y: 350 },
    isHidden: false,
    unlockLevel: 3,
    specialties: ["BACHATA", "REGGAETON"],
    difficulty: 3,
    rewards: {
      bonusXp: 55,
      coins: 35,
      specialMoves: ["Sunset Sensual Flow"],
      skillBonus: { EXPRESSION: 4, MUSICALITY: 3 },
      beachVibes: true
    }
  },
  {
    name: "Royal Ballroom",
    type: "HISTORIC_BALLROOM",
    description: "Historic ballroom where dance royalty once performed",
    location: "Royal Palace",
    coordinates: { x: 350, y: 500 },
    isHidden: false,
    unlockLevel: 7,
    specialties: ["SALSA", "CHACHA"],
    difficulty: 7,
    rewards: {
      bonusXp: 110,
      coins: 70,
      specialMoves: ["Royal Court Dance"],
      skillBonus: { CHARISMA: 6, EXPRESSION: 5 },
      royalTitles: ["Court Dancer"],
      historicalSignificance: true
    }
  }
];

async function seedVenues() {
  try {
    console.log('Seeding dance venues...');
    
    // Clear existing venues
    await prisma.danceVenue.deleteMany({});
    
    // Create new venues
    for (const venue of venues) {
      await prisma.danceVenue.create({
        data: venue
      });
    }
    
    console.log(`✅ Created ${venues.length} dance venues`);
  } catch (error) {
    console.error('❌ Error seeding venues:', error);
    throw error;
  }
}

module.exports = { seedVenues };

if (require.main === module) {
  seedVenues()
    .catch(console.error)
    .finally(() => prisma.$disconnect());
} 