const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const logger = require('../backend/utils/logger');
const { seedBattleMoves } = require('./seed-data/battleMoves');
const { seedQuests } = require('./seed-data/quests');
const { seedAiOpponents } = require('./seed-data/aiOpponents');
const { seedEquipment } = require('./seed-data/equipment');
const { seedVenues } = require('./seed-data/venues');
const { seedCombos } = require('./seed-data/combos');

const prisma = new PrismaClient();

const admins = [
  { username: 'frashev', password: 'Par0lata123' },
  { username: 'monsy', password: 'Par0lata123' },
  { username: 'vely', password: 'Test123!' },
];

const dancers = [
  { username: 'ivan', password: 'ivan' },
  { username: 'georgi', password: 'georgi' },
];

async function upsertUser({ username, password, role }) {
  const passwordHash = await bcrypt.hash(password, 10);
  await prisma.user.upsert({
    where: { username },
    update: {
      role: role,
      ...(role === 'ADMIN' && { credits: 100000 })
    },
    create: {
      username,
      passwordHash,
      role,
      credits: role === 'ADMIN' ? 100000 : 1000,
    },
  });
}

async function main() {
  try {
    logger.info('🌱 Starting enhanced database seeding...');

    // Seed users
    logger.info('👥 Seeding users...');
    for (const admin of admins) {
      await upsertUser({ ...admin, role: 'ADMIN' });
    }
    for (const dancer of dancers) {
      await upsertUser({ ...dancer, role: 'DANCER' });
    }
    logger.info('✅ Users seeded successfully');

    // Seed enhanced battle moves
    logger.info('⚔️ Seeding enhanced battle moves...');
    await seedBattleMoves();
    logger.info('✅ Enhanced battle moves seeded successfully');

    // Seed enhanced quests
    logger.info('📋 Seeding enhanced quests...');
    await seedQuests();
    logger.info('✅ Enhanced quests seeded successfully');

    // Seed equipment
    logger.info('🎽 Seeding equipment...');
    await seedEquipment();
    logger.info('✅ Equipment seeded successfully');

    // Seed AI opponents
    logger.info('🤖 Seeding AI opponents...');
    await seedAiOpponents();
    logger.info('✅ AI opponents seeded successfully');

    // Seed dance venues
    logger.info('🏛️ Seeding dance venues...');
    await seedVenues();
    logger.info('✅ Dance venues seeded successfully');

    // Seed battle combos
    logger.info('🥊 Seeding battle combos...');
    await seedCombos();
    logger.info('✅ Battle combos seeded successfully');

    logger.info('🎉 Enhanced database seeding completed successfully!');
    
    // Log summary
    const counts = {
      users: await prisma.user.count(),
      heroes: await prisma.hero.count(),
      battleMoves: await prisma.battleMove.count(),
      quests: await prisma.quest.count(),
      equipment: await prisma.equipment.count(),
      venues: await prisma.danceVenue.count(),
      combos: await prisma.battleCombo.count()
    };
    
    logger.info('📊 Seeding Summary:', counts);
    
  } catch (error) {
    logger.error('❌ Error during enhanced seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  }); 