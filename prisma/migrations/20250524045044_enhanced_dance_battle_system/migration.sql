/*
  Warnings:

  - The primary key for the `QuestProgress` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the `BattleMove` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Hero` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Quest` table. If the table is not empty, all the data it contains will be lost.

*/
-- <PERSON>reateEnum
CREATE TYPE "DanceStyle" AS ENUM ('SALSA', 'BACHATA', 'KIZOM<PERSON>', 'ZOUK', 'CHACHA', 'MERENGUE', 'REGGAETON', 'CUMBIA');

-- CreateEnum
CREATE TYPE "SkillType" AS ENUM ('TIMING', 'FOOTWORK', 'SPINS', 'LEADING', 'FOLLOWING', 'MUSICALITY', 'EXPRESSION', 'STAMINA', 'CREATIVITY', 'CHARISMA');

-- CreateEnum
CREATE TYPE "EquipmentType" AS ENUM ('SHOES', 'OUTFIT', 'ACCESSORY', 'INSTRUMENT', 'CHARM');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "EquipmentRarity" AS ENUM ('COMMON', 'RARE', 'EPIC', 'LEGENDARY', 'MYTHIC');

-- CreateEnum
CREATE TYPE "BattleType" AS ENUM ('AI_BATTLE', 'PLAYER_BATTLE', 'TOURNAMENT', 'PRACTICE', 'CHALLENGE');

-- CreateEnum
CREATE TYPE "BattleStatus" AS ENUM ('WAITING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'TIMEOUT');

-- CreateEnum
CREATE TYPE "TournamentStatus" AS ENUM ('UPCOMING', 'REGISTRATION', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED');

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "Trait" ADD VALUE 'SALSA_FIRE';
ALTER TYPE "Trait" ADD VALUE 'BACHATA_SOUL';
ALTER TYPE "Trait" ADD VALUE 'KIZOMBA_FLOW';
ALTER TYPE "Trait" ADD VALUE 'ZOUK_MAGIC';
ALTER TYPE "Trait" ADD VALUE 'CHACHA_LIGHTNING';
ALTER TYPE "Trait" ADD VALUE 'RHYTHM_WARRIOR';
ALTER TYPE "Trait" ADD VALUE 'BEAT_ASSASSIN';
ALTER TYPE "Trait" ADD VALUE 'GROOVE_GUARDIAN';
ALTER TYPE "Trait" ADD VALUE 'TEMPO_TITAN';
ALTER TYPE "Trait" ADD VALUE 'MELODY_MAGE';
ALTER TYPE "Trait" ADD VALUE 'CROWD_PLEASER';
ALTER TYPE "Trait" ADD VALUE 'PERFECTIONIST';
ALTER TYPE "Trait" ADD VALUE 'IMPROVISER';
ALTER TYPE "Trait" ADD VALUE 'SHOWSTOPPER';
ALTER TYPE "Trait" ADD VALUE 'DANCE_MYSTIC';

-- DropForeignKey
ALTER TABLE "BattleHistory" DROP CONSTRAINT "BattleHistory_heroId_fkey";

-- DropForeignKey
ALTER TABLE "BattleHistory" DROP CONSTRAINT "BattleHistory_opponentId_fkey";

-- DropForeignKey
ALTER TABLE "Hero" DROP CONSTRAINT "Hero_userId_fkey";

-- DropForeignKey
ALTER TABLE "QuestProgress" DROP CONSTRAINT "QuestProgress_heroId_fkey";

-- DropForeignKey
ALTER TABLE "QuestProgress" DROP CONSTRAINT "QuestProgress_questId_fkey";

-- AlterTable
ALTER TABLE "QuestProgress" DROP CONSTRAINT "QuestProgress_pkey",
ALTER COLUMN "questId" SET DATA TYPE TEXT,
ADD CONSTRAINT "QuestProgress_pkey" PRIMARY KEY ("heroId", "questId");

-- DropTable
DROP TABLE "BattleMove";

-- DropTable
DROP TABLE "Hero";

-- DropTable
DROP TABLE "Quest";

-- CreateTable
CREATE TABLE "heroes" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "level" INTEGER NOT NULL DEFAULT 1,
    "experience" INTEGER NOT NULL DEFAULT 0,
    "energy" INTEGER NOT NULL DEFAULT 10,
    "lastEnergyRefill" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "health" INTEGER NOT NULL DEFAULT 100,
    "mana" INTEGER NOT NULL DEFAULT 50,
    "attack" INTEGER NOT NULL DEFAULT 10,
    "defense" INTEGER NOT NULL DEFAULT 10,
    "speed" INTEGER NOT NULL DEFAULT 10,
    "luck" INTEGER NOT NULL DEFAULT 10,
    "primaryStyle" "DanceStyle" NOT NULL DEFAULT 'SALSA',
    "secondaryStyle" "DanceStyle",
    "stylePoints" JSONB NOT NULL DEFAULT '{}',
    "skills" JSONB NOT NULL DEFAULT '{}',
    "skillPoints" INTEGER NOT NULL DEFAULT 0,
    "equippedItems" JSONB NOT NULL DEFAULT '{}',
    "inventory" JSONB NOT NULL DEFAULT '[]',
    "traits" "Trait"[],
    "traitPoints" INTEGER NOT NULL DEFAULT 3,
    "wins" INTEGER NOT NULL DEFAULT 0,
    "losses" INTEGER NOT NULL DEFAULT 0,
    "draws" INTEGER NOT NULL DEFAULT 0,
    "winStreak" INTEGER NOT NULL DEFAULT 0,
    "bestWinStreak" INTEGER NOT NULL DEFAULT 0,
    "totalBattles" INTEGER NOT NULL DEFAULT 0,
    "globalRank" INTEGER,
    "styleRanks" JSONB NOT NULL DEFAULT '{}',
    "trophies" INTEGER NOT NULL DEFAULT 0,
    "titles" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "favoriteOpponents" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "blockedPlayers" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "prestige" INTEGER NOT NULL DEFAULT 0,
    "prestigePoints" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastActive" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "heroes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "quests" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "requirements" JSONB NOT NULL DEFAULT '{}',
    "danceStyle" "DanceStyle",
    "minLevel" INTEGER NOT NULL DEFAULT 1,
    "maxLevel" INTEGER,
    "xpReward" INTEGER NOT NULL DEFAULT 0,
    "coinReward" INTEGER NOT NULL DEFAULT 0,
    "itemRewards" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "skillPoints" INTEGER NOT NULL DEFAULT 0,
    "traitPoints" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "repeatDaily" BOOLEAN NOT NULL DEFAULT false,
    "repeatWeekly" BOOLEAN NOT NULL DEFAULT false,
    "maxProgress" INTEGER NOT NULL DEFAULT 1,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "quests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "battle_moves" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "danceStyle" "DanceStyle" NOT NULL,
    "type" TEXT NOT NULL,
    "power" INTEGER NOT NULL,
    "accuracy" INTEGER NOT NULL,
    "speed" INTEGER NOT NULL DEFAULT 100,
    "unlockLevel" INTEGER NOT NULL DEFAULT 1,
    "requiredSkill" "SkillType",
    "skillLevel" INTEGER NOT NULL DEFAULT 0,
    "energyCost" INTEGER NOT NULL DEFAULT 1,
    "manaCost" INTEGER NOT NULL DEFAULT 0,
    "cooldown" INTEGER NOT NULL DEFAULT 0,
    "effects" JSONB NOT NULL DEFAULT '[]',
    "soundEffect" TEXT,
    "animation" TEXT,
    "rarity" "EquipmentRarity" NOT NULL DEFAULT 'COMMON',
    "isSecret" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "battle_moves_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "equipment" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" "EquipmentType" NOT NULL,
    "rarity" "EquipmentRarity" NOT NULL DEFAULT 'COMMON',
    "danceStyle" "DanceStyle",
    "statBonuses" JSONB NOT NULL DEFAULT '{}',
    "skillBonuses" JSONB NOT NULL DEFAULT '{}',
    "specialEffects" JSONB NOT NULL DEFAULT '[]',
    "heroId" TEXT,
    "isForSale" BOOLEAN NOT NULL DEFAULT false,
    "price" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "equipment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "player_battles" (
    "id" TEXT NOT NULL,
    "battleType" "BattleType" NOT NULL DEFAULT 'PLAYER_BATTLE',
    "status" "BattleStatus" NOT NULL DEFAULT 'WAITING',
    "player1Id" TEXT NOT NULL,
    "player2Id" TEXT,
    "danceStyle" "DanceStyle" NOT NULL,
    "maxRounds" INTEGER NOT NULL DEFAULT 5,
    "currentRound" INTEGER NOT NULL DEFAULT 1,
    "timeLimit" INTEGER NOT NULL DEFAULT 30,
    "battleData" JSONB NOT NULL DEFAULT '{}',
    "moves" JSONB NOT NULL DEFAULT '[]',
    "winnerId" TEXT,
    "winnerScore" INTEGER,
    "loserScore" INTEGER,
    "xpReward" INTEGER,
    "coinReward" INTEGER,
    "startedAt" TIMESTAMP(3),
    "endedAt" TIMESTAMP(3),
    "lastMoveAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "player_battles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "battle_challenges" (
    "id" TEXT NOT NULL,
    "senderId" TEXT NOT NULL,
    "receiverId" TEXT NOT NULL,
    "danceStyle" "DanceStyle" NOT NULL,
    "message" TEXT,
    "wager" INTEGER,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "battleId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "battle_challenges_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tournaments" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "danceStyle" "DanceStyle" NOT NULL,
    "status" "TournamentStatus" NOT NULL DEFAULT 'UPCOMING',
    "maxParticipants" INTEGER NOT NULL DEFAULT 16,
    "entryFee" INTEGER NOT NULL DEFAULT 0,
    "firstPrize" INTEGER NOT NULL,
    "secondPrize" INTEGER NOT NULL,
    "thirdPrize" INTEGER NOT NULL,
    "participationReward" INTEGER NOT NULL DEFAULT 0,
    "registrationStart" TIMESTAMP(3) NOT NULL,
    "registrationEnd" TIMESTAMP(3) NOT NULL,
    "startTime" TIMESTAMP(3) NOT NULL,
    "endTime" TIMESTAMP(3),
    "bracket" JSONB NOT NULL DEFAULT '{}',
    "currentRound" INTEGER NOT NULL DEFAULT 1,
    "totalRounds" INTEGER NOT NULL DEFAULT 4,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tournaments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tournament_entries" (
    "id" TEXT NOT NULL,
    "tournamentId" TEXT NOT NULL,
    "heroId" TEXT NOT NULL,
    "seed" INTEGER,
    "eliminated" BOOLEAN NOT NULL DEFAULT false,
    "finalRank" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tournament_entries_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "heroes_userId_key" ON "heroes"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "tournament_entries_tournamentId_heroId_key" ON "tournament_entries"("tournamentId", "heroId");

-- AddForeignKey
ALTER TABLE "heroes" ADD CONSTRAINT "heroes_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QuestProgress" ADD CONSTRAINT "QuestProgress_heroId_fkey" FOREIGN KEY ("heroId") REFERENCES "heroes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QuestProgress" ADD CONSTRAINT "QuestProgress_questId_fkey" FOREIGN KEY ("questId") REFERENCES "quests"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BattleHistory" ADD CONSTRAINT "BattleHistory_heroId_fkey" FOREIGN KEY ("heroId") REFERENCES "heroes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BattleHistory" ADD CONSTRAINT "BattleHistory_opponentId_fkey" FOREIGN KEY ("opponentId") REFERENCES "heroes"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "equipment" ADD CONSTRAINT "equipment_heroId_fkey" FOREIGN KEY ("heroId") REFERENCES "heroes"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "player_battles" ADD CONSTRAINT "player_battles_player1Id_fkey" FOREIGN KEY ("player1Id") REFERENCES "heroes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "player_battles" ADD CONSTRAINT "player_battles_player2Id_fkey" FOREIGN KEY ("player2Id") REFERENCES "heroes"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "battle_challenges" ADD CONSTRAINT "battle_challenges_senderId_fkey" FOREIGN KEY ("senderId") REFERENCES "heroes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "battle_challenges" ADD CONSTRAINT "battle_challenges_receiverId_fkey" FOREIGN KEY ("receiverId") REFERENCES "heroes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tournament_entries" ADD CONSTRAINT "tournament_entries_tournamentId_fkey" FOREIGN KEY ("tournamentId") REFERENCES "tournaments"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tournament_entries" ADD CONSTRAINT "tournament_entries_heroId_fkey" FOREIGN KEY ("heroId") REFERENCES "heroes"("id") ON DELETE CASCADE ON UPDATE CASCADE;
