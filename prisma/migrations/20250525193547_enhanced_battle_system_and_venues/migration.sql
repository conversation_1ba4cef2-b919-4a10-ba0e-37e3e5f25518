/*
  Warnings:

  - The values [IN_PROGRESS,CANCELLED] on the enum `BattleStatus` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `coinReward` on the `quests` table. All the data in the column will be lost.
  - You are about to drop the column `danceStyle` on the `quests` table. All the data in the column will be lost.
  - You are about to drop the column `itemRewards` on the `quests` table. All the data in the column will be lost.
  - You are about to drop the column `maxProgress` on the `quests` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `quests` table. All the data in the column will be lost.
  - You are about to drop the column `repeatDaily` on the `quests` table. All the data in the column will be lost.
  - You are about to drop the column `repeatWeekly` on the `quests` table. All the data in the column will be lost.
  - You are about to drop the column `requirements` on the `quests` table. All the data in the column will be lost.
  - You are about to drop the column `skillPoints` on the `quests` table. All the data in the column will be lost.
  - You are about to drop the column `traitPoints` on the `quests` table. All the data in the column will be lost.
  - You are about to drop the column `xpReward` on the `quests` table. All the data in the column will be lost.
  - Added the required column `targetType` to the `quests` table without a default value. This is not possible if the table is not empty.
  - Added the required column `targetValue` to the `quests` table without a default value. This is not possible if the table is not empty.
  - Added the required column `title` to the `quests` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `type` on the `quests` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `category` on the `quests` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "VenueType" AS ENUM ('DANCE_ACADEMY', 'UNDERGROUND_CLUB', 'COMPETITION_HALL', 'FESTIVAL_GROUND', 'MYSTICAL_STUDIO', 'PRACTICE_ROOM', 'ROOFTOP_TERRACE', 'BEACH_CLUB', 'HISTORIC_BALLROOM');

-- CreateEnum
CREATE TYPE "EventType" AS ENUM ('TOURNAMENT', 'WORKSHOP', 'SOCIAL_DANCE', 'BATTLE_ROYALE', 'RHYTHM_CHALLENGE', 'STYLE_SHOWCASE', 'MASTER_CLASS');

-- CreateEnum
CREATE TYPE "QuestType" AS ENUM ('DAILY', 'WEEKLY', 'MONTHLY', 'STORY', 'SPECIAL', 'SOCIAL');

-- CreateEnum
CREATE TYPE "QuestCategory" AS ENUM ('BATTLE', 'EXPLORATION', 'SOCIAL', 'SKILL_DEVELOPMENT', 'COLLECTION', 'ACHIEVEMENT', 'TOURNAMENT', 'STYLE_MASTERY');

-- AlterEnum - Add new BattleType value first
ALTER TYPE "BattleType" ADD VALUE 'SINGLE_PLAYER';

-- AlterEnum - Add new DanceStyle value
ALTER TYPE "DanceStyle" ADD VALUE 'UNIVERSAL';

-- AlterEnum - Update BattleStatus enum
BEGIN;
CREATE TYPE "BattleStatus_new" AS ENUM ('WAITING', 'ACTIVE', 'COMPLETED', 'FORFEIT', 'TIMEOUT');
ALTER TABLE "player_battles" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "player_battles" ALTER COLUMN "status" TYPE "BattleStatus_new" USING ("status"::text::"BattleStatus_new");
ALTER TYPE "BattleStatus" RENAME TO "BattleStatus_old";
ALTER TYPE "BattleStatus_new" RENAME TO "BattleStatus";
DROP TYPE "BattleStatus_old";
ALTER TABLE "player_battles" ALTER COLUMN "status" SET DEFAULT 'WAITING';
COMMIT;

-- AlterTable
ALTER TABLE "quests" DROP COLUMN "coinReward",
DROP COLUMN "danceStyle",
DROP COLUMN "itemRewards",
DROP COLUMN "maxProgress",
DROP COLUMN "name",
DROP COLUMN "repeatDaily",
DROP COLUMN "repeatWeekly",
DROP COLUMN "requirements",
DROP COLUMN "skillPoints",
DROP COLUMN "traitPoints",
DROP COLUMN "xpReward",
ADD COLUMN     "repeatType" TEXT,
ADD COLUMN     "requiresVenueVisit" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "rewardCoins" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "rewardSkillPoints" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "rewardTraitPoints" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "rewardXp" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "specialRewards" JSONB,
ADD COLUMN     "targetType" TEXT NOT NULL,
ADD COLUMN     "targetValue" INTEGER NOT NULL,
ADD COLUMN     "title" TEXT NOT NULL,
ADD COLUMN     "venueId" TEXT,
DROP COLUMN "type",
ADD COLUMN     "type" "QuestType" NOT NULL,
DROP COLUMN "category",
ADD COLUMN     "category" "QuestCategory" NOT NULL;

-- CreateTable
CREATE TABLE "dance_venues" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "VenueType" NOT NULL,
    "description" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "coordinates" JSONB,
    "isHidden" BOOLEAN NOT NULL DEFAULT false,
    "unlockLevel" INTEGER NOT NULL DEFAULT 1,
    "specialties" "DanceStyle"[],
    "difficulty" INTEGER NOT NULL DEFAULT 1,
    "rewards" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "dance_venues_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "venue_events" (
    "id" TEXT NOT NULL,
    "venueId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "eventType" "EventType" NOT NULL,
    "startTime" TIMESTAMP(3) NOT NULL,
    "endTime" TIMESTAMP(3) NOT NULL,
    "rewardXp" INTEGER NOT NULL DEFAULT 0,
    "rewardCoins" INTEGER NOT NULL DEFAULT 0,
    "specialRewards" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "venue_events_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "hero_event_participation" (
    "id" TEXT NOT NULL,
    "heroId" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "participated" BOOLEAN NOT NULL DEFAULT false,
    "score" INTEGER,
    "rank" INTEGER,
    "rewardsClaimed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "hero_event_participation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "battle_combos" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "bodySequence" TEXT NOT NULL,
    "danceStyle" "DanceStyle" NOT NULL,
    "difficulty" INTEGER NOT NULL DEFAULT 1,
    "damage" INTEGER NOT NULL DEFAULT 10,
    "energyCost" INTEGER NOT NULL DEFAULT 5,
    "manaCost" INTEGER NOT NULL DEFAULT 0,
    "beatCount" INTEGER NOT NULL DEFAULT 4,
    "timingWindow" INTEGER NOT NULL DEFAULT 200,
    "unlockLevel" INTEGER NOT NULL DEFAULT 1,
    "requiredMoves" TEXT[],
    "audioFile" TEXT,
    "animationFile" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "battle_combos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "hero_combo_mastery" (
    "id" TEXT NOT NULL,
    "heroId" TEXT NOT NULL,
    "comboId" TEXT NOT NULL,
    "timesUsed" INTEGER NOT NULL DEFAULT 0,
    "perfectHits" INTEGER NOT NULL DEFAULT 0,
    "masteryLevel" INTEGER NOT NULL DEFAULT 1,
    "learnedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastUsedAt" TIMESTAMP(3),

    CONSTRAINT "hero_combo_mastery_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "hero_location_discovery" (
    "id" TEXT NOT NULL,
    "heroId" TEXT NOT NULL,
    "venueId" TEXT NOT NULL,
    "discoveredAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "firstVisit" BOOLEAN NOT NULL DEFAULT true,
    "visitCount" INTEGER NOT NULL DEFAULT 1,

    CONSTRAINT "hero_location_discovery_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "battles" (
    "id" TEXT NOT NULL,
    "hero1Id" TEXT NOT NULL,
    "hero2Id" TEXT,
    "aiOpponentId" TEXT,
    "venueId" TEXT,
    "danceStyle" "DanceStyle" NOT NULL,
    "battleType" "BattleType" NOT NULL DEFAULT 'SINGLE_PLAYER',
    "status" "BattleStatus" NOT NULL DEFAULT 'ACTIVE',
    "currentTurn" INTEGER NOT NULL DEFAULT 1,
    "maxTurns" INTEGER NOT NULL DEFAULT 10,
    "hero1Health" INTEGER NOT NULL,
    "hero1Mana" INTEGER NOT NULL,
    "hero2Health" INTEGER,
    "hero2Mana" INTEGER,
    "winnerId" TEXT,
    "winCondition" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "endedAt" TIMESTAMP(3),

    CONSTRAINT "battles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "battle_move_usage" (
    "id" TEXT NOT NULL,
    "battleId" TEXT NOT NULL,
    "heroId" TEXT NOT NULL,
    "moveId" TEXT,
    "comboId" TEXT,
    "timingScore" INTEGER NOT NULL,
    "damage" INTEGER NOT NULL,
    "turn" INTEGER NOT NULL,
    "bodySequence" TEXT,
    "beatAccuracy" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "battle_move_usage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "hero_event_participation_heroId_eventId_key" ON "hero_event_participation"("heroId", "eventId");

-- CreateIndex
CREATE UNIQUE INDEX "hero_combo_mastery_heroId_comboId_key" ON "hero_combo_mastery"("heroId", "comboId");

-- CreateIndex
CREATE UNIQUE INDEX "hero_location_discovery_heroId_venueId_key" ON "hero_location_discovery"("heroId", "venueId");

-- AddForeignKey
ALTER TABLE "quests" ADD CONSTRAINT "quests_venueId_fkey" FOREIGN KEY ("venueId") REFERENCES "dance_venues"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "venue_events" ADD CONSTRAINT "venue_events_venueId_fkey" FOREIGN KEY ("venueId") REFERENCES "dance_venues"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hero_event_participation" ADD CONSTRAINT "hero_event_participation_heroId_fkey" FOREIGN KEY ("heroId") REFERENCES "heroes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hero_event_participation" ADD CONSTRAINT "hero_event_participation_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "venue_events"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hero_combo_mastery" ADD CONSTRAINT "hero_combo_mastery_heroId_fkey" FOREIGN KEY ("heroId") REFERENCES "heroes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hero_combo_mastery" ADD CONSTRAINT "hero_combo_mastery_comboId_fkey" FOREIGN KEY ("comboId") REFERENCES "battle_combos"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hero_location_discovery" ADD CONSTRAINT "hero_location_discovery_heroId_fkey" FOREIGN KEY ("heroId") REFERENCES "heroes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "hero_location_discovery" ADD CONSTRAINT "hero_location_discovery_venueId_fkey" FOREIGN KEY ("venueId") REFERENCES "dance_venues"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "battles" ADD CONSTRAINT "battles_hero1Id_fkey" FOREIGN KEY ("hero1Id") REFERENCES "heroes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "battles" ADD CONSTRAINT "battles_hero2Id_fkey" FOREIGN KEY ("hero2Id") REFERENCES "heroes"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "battles" ADD CONSTRAINT "battles_venueId_fkey" FOREIGN KEY ("venueId") REFERENCES "dance_venues"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "battle_move_usage" ADD CONSTRAINT "battle_move_usage_battleId_fkey" FOREIGN KEY ("battleId") REFERENCES "battles"("id") ON DELETE CASCADE ON UPDATE CASCADE;
