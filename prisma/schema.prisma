generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                    String                     @id @default(cuid())
  username              String                     @unique
  passwordHash          String?
  role                  UserRole                   @default(DANCER)
  credits               Int                        @default(100)
  createdAt             DateTime                   @default(now())
  updatedAt             DateTime                   @updatedAt
  avatarUrl             String?
  displayName           String?
  email                 String?                    @unique
  googleId              String?                    @unique
  facebookId            String?                    @unique
  creditTx              CreditTransaction[]
  favoritedBy           FavoriteDancer[]           @relation("FavoritedUser")
  favorites             FavoriteDancer[]           @relation("FavoritingUser")
  hero                  Hero?
  addedItems            PlaylistItem[]
  sessions              Session[]
  suggestions           Suggestion[]
  achievements          UserAchievement[]
  votes                 Vote[]
  authoredFeedback      Feedback[]
  generatedPlaylists    GeneratedPlaylistHistory[]
  receivedNotifications Notification[]             @relation("ReceivedNotifications")
  sentNotifications     Notification[]             @relation("SentNotifications")
  comments              SuggestionComment[]
  profile               UserProfile?

  @@index([role])
  @@index([username])
  @@index([email])
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model PlaylistTemplate {
  id            String   @id @default(cuid())
  name          String   @unique
  description   String?
  styleRotation Json
  isActive      Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model ActivePlaylist {
  id                   String         @id @default(cuid())
  youtubePlaylistId    String?
  currentSongIndex     Int?
  isPlaying            Boolean?       @default(false)
  lastSyncedWithYT     DateTime?
  createdAt            DateTime       @default(now())
  updatedAt            DateTime       @updatedAt
  currentSongStartedAt DateTime?
  playlistItems        PlaylistItem[]

  @@index([createdAt])
}

model PlaylistItem {
  id               String         @id @default(cuid())
  activePlaylistId String
  youtubeVideoId   String
  title            String
  channelTitle     String?
  thumbnailUrl     String?
  durationSeconds  Int?
  danceStyle       String?
  order            Int
  isFixed          Boolean        @default(false)
  addedAt          DateTime       @default(now())
  addedByUserId    String?
  suggestionId     String?        @unique
  activePlaylist   ActivePlaylist @relation(fields: [activePlaylistId], references: [id], onDelete: Cascade)
  addedBy          User?          @relation(fields: [addedByUserId], references: [id])
  suggestion       Suggestion?    @relation(fields: [suggestionId], references: [id])

  @@unique([activePlaylistId, order])
  @@index([youtubeVideoId])
}

model Suggestion {
  id              String              @id @default(cuid())
  userId          String
  youtubeVideoId  String
  title           String
  channelTitle    String?
  thumbnailUrl    String?
  danceStyle      String
  status          SuggestionStatus    @default(PENDING)
  votes           Int                 @default(0)
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  isLocked        Boolean             @default(false)
  durationSeconds Int?
  creditTx        CreditTransaction[]
  playlistItem    PlaylistItem?
  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  voters          Vote[]
  comments        SuggestionComment[]

  @@index([youtubeVideoId])
  @@index([status])
  @@index([userId])
  @@index([createdAt])
  @@index([votes])
}

model CreditTransaction {
  id           String          @id @default(cuid())
  userId       String
  amount       Int
  notes        String?
  timestamp    DateTime        @default(now())
  suggestionId String?
  type         TransactionType
  suggestion   Suggestion?     @relation(fields: [suggestionId], references: [id])
  user         User            @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model AdminSettings {
  singletonLock                   Boolean      @unique @default(true)
  accessToken                     String?
  refreshToken                    String?
  tokenExpiry                     DateTime?
  createdAt                       DateTime     @default(now())
  updatedAt                       DateTime     @updatedAt
  isYouTubeConnected              Boolean      @default(false)
  youtubeEmail                    String?
  youtubeProfileId                String?
  dailySuggestionLimit            Int          @default(3)
  suggestionPlaylistIdsJson       Json?
  id                              Int          @id @default(autoincrement())
  activeYoutubePlaylistId         String?
  operationalMode                 PlaylistMode @default(GENERATED)
  styleSourcePlaylists            Json?
  lastWatchedVideoId              String?
  watchHistorySyncIntervalSeconds Int          @default(30)
  isWatchHistorySyncEnabled       Boolean      @default(true)
  externalMirrorPlaylistId        String?
  playlistSubmitterUsernames      String[]     @default([])
  styleSequentialExtractionPrefs  Json?        @default("{}")

  @@map("admin_settings")
}

model Vote {
  id           String     @id @default(cuid())
  userId       String
  suggestionId String
  createdAt    DateTime   @default(now())
  suggestion   Suggestion @relation(fields: [suggestionId], references: [id], onDelete: Cascade)
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, suggestionId])
  @@index([userId])
  @@index([suggestionId])
}

model UserProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  displayName String?
  avatarUrl   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

model Achievement {
  id          String            @id @default(cuid())
  name        String            @unique
  description String
  iconUrl     String?
  createdAt   DateTime          @default(now())
  users       UserAchievement[]
}

model UserAchievement {
  userId        String
  achievementId String
  achievedAt    DateTime    @default(now())
  achievement   Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, achievementId])
  @@index([userId])
  @@index([achievementId])
}

model SuggestionComment {
  id           String     @id @default(cuid())
  content      String
  createdAt    DateTime   @default(now())
  userId       String
  suggestionId String
  suggestion   Suggestion @relation(fields: [suggestionId], references: [id], onDelete: Cascade)
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([createdAt])
  @@map("suggestion_comments")
}

model GeneratedPlaylistHistory {
  id          String   @id @default(cuid())
  playlistId  String
  playlistUrl String
  name        String
  description String?
  songCount   Int
  adminUserId String
  createdAt   DateTime @default(now())
  admin       User     @relation(fields: [adminUserId], references: [id])

  @@index([adminUserId])
  @@map("generated_playlist_history")
}

model Author {
  id          String   @id @default(cuid())
  name        String
  role        String
  bio         String
  photoUrl    String?
  socialLinks Json?
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("authors")
}

model Contributor {
  id          String   @id @default(cuid())
  name        String
  role        String
  bio         String?
  photoUrl    String?
  socialLinks Json?
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("contributors")
}

model DanceClub {
  id           String   @id @default(cuid())
  name         String
  description  String
  address      String
  city         String
  state        String?
  country      String
  website      String?
  photoUrl     String?
  danceStyles  String[]
  openingHours Json?
  featured     Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  events       Event[]

  @@map("dance_clubs")
}

model Event {
  id          String     @id @default(cuid())
  title       String
  description String
  startDate   DateTime
  endDate     DateTime?
  location    String?
  address     String?
  photoUrl    String?
  ticketUrl   String?
  price       String?
  danceStyles String[]
  featured    Boolean    @default(false)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  clubId      String?
  club        DanceClub? @relation(fields: [clubId], references: [id])

  @@index([startDate])
  @@map("events")
}

model Feedback {
  id          String         @id @default(cuid())
  type        FeedbackType
  title       String
  description String
  status      FeedbackStatus @default(PENDING)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  userId      String
  response    String?
  respondedAt DateTime?
  user        User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([type])
  @@index([status])
  @@index([createdAt])
  @@map("feedback")
}

model Notification {
  id         String           @id @default(cuid())
  title      String
  message    String
  type       NotificationType @default(GENERAL)
  isRead     Boolean          @default(false)
  createdAt  DateTime         @default(now())
  senderId   String
  receiverId String?
  receiver   User?            @relation("ReceivedNotifications", fields: [receiverId], references: [id], onDelete: Cascade)
  sender     User             @relation("SentNotifications", fields: [senderId], references: [id], onDelete: Cascade)

  @@index([receiverId])
  @@index([isRead])
  @@index([createdAt])
  @@map("notifications")
}

model FavoriteDancer {
  favoritingUserId String
  favoritedUserId  String
  createdAt        DateTime @default(now())
  favoritedUser    User     @relation("FavoritedUser", fields: [favoritedUserId], references: [id], onDelete: Cascade)
  favoritingUser   User     @relation("FavoritingUser", fields: [favoritingUserId], references: [id], onDelete: Cascade)

  @@id([favoritingUserId, favoritedUserId])
  @@index([favoritingUserId])
  @@index([favoritedUserId])
}

model Hero {
  id                String   @id @default(cuid())
  userId            String   @unique
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  name              String
  level             Int      @default(1)
  experience        Int      @default(0)
  energy            Int      @default(10)
  lastEnergyRefill  DateTime @default(now())
  
  // Enhanced Stats
  health            Int      @default(100)
  mana              Int      @default(50)
  attack            Int      @default(10)
  defense           Int      @default(10)
  speed             Int      @default(10)
  luck              Int      @default(10)
  
  // Dance Styles & Specialization
  primaryStyle      DanceStyle @default(SALSA)
  secondaryStyle    DanceStyle?
  stylePoints       Json     @default("{}")  // {SALSA: 100, BACHATA: 50, ...}
  
  // Skills System
  skills            Json     @default("{}")  // {TIMING: 25, FOOTWORK: 30, ...}
  skillPoints       Int      @default(0)
  
  // Equipment & Inventory
  equippedItems     Json     @default("{}")  // {SHOES: "item_id", OUTFIT: "item_id", ...}
  inventory         Json     @default("[]")  // ["item_id1", "item_id2", ...]
  
  // Enhanced Traits
  traits            Trait[]
  traitPoints       Int      @default(3)
  
  // Battle Stats
  wins              Int      @default(0)
  losses            Int      @default(0)
  draws             Int      @default(0)
  winStreak         Int      @default(0)
  bestWinStreak     Int      @default(0)
  totalBattles      Int      @default(0)
  
  // Rankings & Achievements
  globalRank        Int?
  styleRanks        Json     @default("{}")  // {SALSA: 150, BACHATA: 200, ...}
  trophies          Int      @default(0)
  titles            String[] @default([])
  
  // Social Features
  favoriteOpponents String[] @default([])
  blockedPlayers    String[] @default([])
  
  // Progression
  prestige          Int      @default(0)
  prestigePoints    Int      @default(0)
  coins             Int      @default(1000)  // Starting coins for equipment purchases
  
  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  lastActive        DateTime @default(now())
  
  // Relations
  questProgress     QuestProgress[]
  battleHistory     BattleHistory[] @relation("HeroBattles")
  opponentBattles   BattleHistory[] @relation("OpponentBattles")
  playerBattles     PlayerBattle[] @relation("PlayerBattlePlayer1")
  playerBattles2    PlayerBattle[] @relation("PlayerBattlePlayer2")
  sentChallenges    BattleChallenge[] @relation("ChallengeSender")
  receivedChallenges BattleChallenge[] @relation("ChallengeReceiver")
  tournamentEntries TournamentEntry[]
  equipment         Equipment[]
  
  // New relationships for enhanced system
  eventParticipations HeroEventParticipation[]
  comboMastery        HeroComboMastery[]
  locationDiscoveries HeroLocationDiscovery[]
  battlesAsHero1      Battle[] @relation("BattleHero1")
  battlesAsHero2      Battle[] @relation("BattleHero2")
  
  @@map("heroes")
}

model Quest {
  id          String    @id @default(cuid())
  title       String
  description String
  type        QuestType
  category    QuestCategory
  
  // Quest requirements and rewards
  targetType  String    // "BATTLE_WINS", "PERFECT_TIMING", "STYLE_MASTERY", etc.
  targetValue Int
  minLevel    Int       @default(1)
  maxLevel    Int?
  
  rewardXp    Int       @default(0)
  rewardCoins Int       @default(0)
  rewardSkillPoints Int @default(0)
  rewardTraitPoints Int @default(0)
  specialRewards Json?  // Equipment, titles, etc.
  
  // Quest scheduling and availability
  isActive    Boolean   @default(true)
  startDate   DateTime?
  endDate     DateTime?
  repeatType  String?   // "DAILY", "WEEKLY", "MONTHLY"
  
  // Location-based quests
  venueId     String?
  venue       DanceVenue? @relation(fields: [venueId], references: [id])
  requiresVenueVisit Boolean @default(false)
  
  // Quest progression tracking
  heroQuests  QuestProgress[] @relation("QuestProgress")
  
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  @@map("quests")
}

model QuestProgress {
  heroId    String
  questId   String
  progress  Int       @default(0)
  completed Boolean   @default(false)
  claimedAt DateTime?
  hero      Hero      @relation(fields: [heroId], references: [id], onDelete: Cascade)
  quest     Quest     @relation("QuestProgress", fields: [questId], references: [id], onDelete: Cascade)

  @@id([heroId, questId])
}

model BattleMove {
  id            String     @id @default(cuid())
  name          String
  description   String
  danceStyle    DanceStyle
  type          String     // BASIC, TURN, SHINE, COMBO, SPECIAL, ULTIMATE
  
  // Stats
  power         Int
  accuracy      Int
  speed         Int        @default(100)
  
  // Requirements
  unlockLevel   Int        @default(1)
  requiredSkill SkillType?
  skillLevel    Int        @default(0)
  energyCost    Int        @default(1)
  manaCost      Int        @default(0)
  cooldown      Int        @default(0)
  
  // Effects
  effects       Json       @default("[]")  // ["stun", "heal", "buff_attack", ...]
  soundEffect   String?    // Audio file name
  animation     String?    // Animation identifier
  
  // Rarity & Availability
  rarity        EquipmentRarity @default(COMMON)
  isSecret      Boolean    @default(false)
  
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt
  
  @@map("battle_moves")
}

model BattleHistory {
  id         String   @id @default(cuid())
  heroId     String
  opponentId String?
  result     String
  turnCount  Int
  finalScore Int?
  createdAt  DateTime @default(now())
  hero       Hero     @relation("HeroBattles", fields: [heroId], references: [id], onDelete: Cascade)
  opponent   Hero?    @relation("OpponentBattles", fields: [opponentId], references: [id])

  @@index([heroId])
  @@index([opponentId])
  @@index([createdAt])
}

model Equipment {
  id          String          @id @default(cuid())
  name        String
  description String
  type        EquipmentType
  rarity      EquipmentRarity @default(COMMON)
  danceStyle  DanceStyle?
  
  // Stats Bonuses
  statBonuses Json            @default("{}")  // {attack: 5, defense: 3, ...}
  skillBonuses Json           @default("{}")  // {TIMING: 10, FOOTWORK: 5, ...}
  
  // Special Effects
  specialEffects Json         @default("[]")  // ["double_xp", "energy_regen", ...]
  
  // Ownership
  heroId      String?
  hero        Hero?           @relation(fields: [heroId], references: [id], onDelete: SetNull)
  
  // Market
  isForSale   Boolean         @default(false)
  price       Int?
  
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  
  @@map("equipment")
}

model PlayerBattle {
  id              String        @id @default(cuid())
  battleType      BattleType    @default(PLAYER_BATTLE)
  status          BattleStatus  @default(WAITING)
  
  // Participants
  player1Id       String
  player1         Hero          @relation("PlayerBattlePlayer1", fields: [player1Id], references: [id])
  player2Id       String?
  player2         Hero?         @relation("PlayerBattlePlayer2", fields: [player2Id], references: [id])
  
  // Battle Settings
  danceStyle      DanceStyle
  maxRounds       Int           @default(5)
  currentRound    Int           @default(1)
  timeLimit       Int           @default(30) // seconds per turn
  
  // Battle State
  battleData      Json          @default("{}")  // Current battle state
  moves           Json          @default("[]")  // Move history
  
  // Results
  winnerId        String?
  winnerScore     Int?
  loserScore      Int?
  xpReward        Int?
  coinReward      Int?
  
  // Timing
  startedAt       DateTime?
  endedAt         DateTime?
  lastMoveAt      DateTime?
  
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  
  @@map("player_battles")
}

model BattleChallenge {
  id            String        @id @default(cuid())
  senderId      String
  sender        Hero          @relation("ChallengeSender", fields: [senderId], references: [id])
  receiverId    String
  receiver      Hero          @relation("ChallengeReceiver", fields: [receiverId], references: [id])
  
  danceStyle    DanceStyle
  message       String?
  wager         Int?          // Optional coin wager
  
  status        String        @default("PENDING") // PENDING, ACCEPTED, DECLINED, EXPIRED
  expiresAt     DateTime
  
  battleId      String?       // Set when challenge is accepted
  
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  
  @@map("battle_challenges")
}

model Tournament {
  id              String            @id @default(cuid())
  name            String
  description     String
  danceStyle      DanceStyle
  status          TournamentStatus  @default(UPCOMING)
  
  // Tournament Settings
  maxParticipants Int               @default(16)
  entryFee        Int               @default(0)
  
  // Prizes
  firstPrize      Int
  secondPrize     Int
  thirdPrize      Int
  participationReward Int           @default(0)
  
  // Timing
  registrationStart DateTime
  registrationEnd   DateTime
  startTime         DateTime
  endTime           DateTime?
  
  // Tournament Data
  bracket         Json              @default("{}")
  currentRound    Int               @default(1)
  totalRounds     Int               @default(4)
  
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  
  // Relations
  entries         TournamentEntry[]
  
  @@map("tournaments")
}

model TournamentEntry {
  id           String     @id @default(cuid())
  tournamentId String
  tournament   Tournament @relation(fields: [tournamentId], references: [id], onDelete: Cascade)
  heroId       String
  hero         Hero       @relation(fields: [heroId], references: [id], onDelete: Cascade)
  
  seed         Int?       // Tournament seeding
  eliminated   Boolean    @default(false)
  finalRank    Int?
  
  createdAt    DateTime   @default(now())
  
  @@unique([tournamentId, heroId])
  @@map("tournament_entries")
}

enum UserRole {
  ADMIN
  DANCER
}

enum SuggestionStatus {
  PENDING
  APPROVED
  REJECTED
}

enum TransactionType {
  SUGGESTION_COST
  VOTE_REWARD
  ADMIN_ADJUSTMENT
  INITIAL_CREDITS
  DAILY_BONUS
}

enum PlaylistMode {
  GENERATED
  EXTERNAL_MIRROR
}

enum FeedbackType {
  FEATURE_REQUEST
  BUG_REPORT
  GENERAL
}

enum FeedbackStatus {
  PENDING
  UNDER_REVIEW
  COMPLETED
  REJECTED
}

enum NotificationType {
  GENERAL
  EVENT
  CLUB
  SYSTEM
}

enum Trait {
  // Basic Traits (existing)
  ON2_TIMING
  SPIN_MASTER
  SMOOTH_LEADS
  FOOTWORK_WIZARD
  
  // Advanced Dance Style Traits
  SALSA_FIRE
  BACHATA_SOUL
  KIZOMBA_FLOW
  ZOUK_MAGIC
  CHACHA_LIGHTNING
  
  // Combat Traits
  RHYTHM_WARRIOR
  BEAT_ASSASSIN
  GROOVE_GUARDIAN
  TEMPO_TITAN
  MELODY_MAGE
  
  // Special Traits
  CROWD_PLEASER
  PERFECTIONIST
  IMPROVISER
  SHOWSTOPPER
  DANCE_MYSTIC
}

enum DanceStyle {
  SALSA
  BACHATA
  KIZOMBA
  ZOUK
  CHACHA
  MERENGUE
  REGGAETON
  CUMBIA
  UNIVERSAL
}

enum SkillType {
  TIMING
  FOOTWORK
  SPINS
  LEADING
  FOLLOWING
  MUSICALITY
  EXPRESSION
  STAMINA
  CREATIVITY
  CHARISMA
}

enum EquipmentType {
  SHOES
  OUTFIT
  ACCESSORY
  INSTRUMENT
  CHARM
}

enum EquipmentRarity {
  COMMON
  RARE
  EPIC
  LEGENDARY
  MYTHIC
}

enum BattleType {
  AI_BATTLE
  PLAYER_BATTLE
  SINGLE_PLAYER
  TOURNAMENT
  PRACTICE
  CHALLENGE
}

enum BattleStatus {
  WAITING
  ACTIVE
  COMPLETED
  FORFEIT
  TIMEOUT
}

enum TournamentStatus {
  UPCOMING
  REGISTRATION
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

// Enhanced Dance World Map System
model DanceVenue {
  id          String   @id @default(cuid())
  name        String
  type        VenueType
  description String
  location    String   // Geographic location
  coordinates Json?    // Map coordinates {x, y}
  isHidden    Boolean  @default(false)
  unlockLevel Int      @default(1)
  
  // Venue-specific properties
  specialties DanceStyle[]
  difficulty  Int      @default(1) // 1-10 scale
  rewards     Json?    // Special rewards available
  
  // Relationships
  quests      Quest[]
  battles     Battle[]
  events      VenueEvent[]
  discoveries HeroLocationDiscovery[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("dance_venues")
}

model VenueEvent {
  id          String   @id @default(cuid())
  venueId     String
  venue       DanceVenue @relation(fields: [venueId], references: [id], onDelete: Cascade)
  
  name        String
  description String
  eventType   EventType
  startTime   DateTime
  endTime     DateTime
  
  // Event rewards
  rewardXp    Int      @default(0)
  rewardCoins Int      @default(0)
  specialRewards Json? // Special items, titles, etc.
  
  // Participation tracking
  participants HeroEventParticipation[]
  
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("venue_events")
}

model HeroEventParticipation {
  id        String   @id @default(cuid())
  heroId    String
  hero      Hero     @relation(fields: [heroId], references: [id], onDelete: Cascade)
  eventId   String
  event     VenueEvent @relation(fields: [eventId], references: [id], onDelete: Cascade)
  
  participated Boolean @default(false)
  score        Int?
  rank         Int?
  rewardsClaimed Boolean @default(false)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@unique([heroId, eventId])
  @@map("hero_event_participation")
}

// Enhanced Battle System with Body Movement Combinations
model BattleCombo {
  id          String   @id @default(cuid())
  name        String
  description String
  
  // Body part sequence (e.g., "LL+RL+HP" for Left Leg + Right Leg + Hip)
  bodySequence String
  danceStyle   DanceStyle
  
  // Combo properties
  difficulty   Int      @default(1) // 1-10 scale
  damage       Int      @default(10)
  energyCost   Int      @default(5)
  manaCost     Int      @default(0)
  
  // Timing requirements
  beatCount    Int      @default(4) // Number of beats in the combo
  timingWindow Int      @default(200) // Milliseconds for perfect timing
  
  // Unlock requirements
  unlockLevel  Int      @default(1)
  requiredMoves String[] // Required basic moves to learn this combo
  
  // Audio and visual
  audioFile    String?
  animationFile String?
  
  // Usage tracking
  heroComboMastery HeroComboMastery[]
  
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  @@map("battle_combos")
}

model HeroComboMastery {
  id        String   @id @default(cuid())
  heroId    String
  hero      Hero     @relation(fields: [heroId], references: [id], onDelete: Cascade)
  comboId   String
  combo     BattleCombo @relation(fields: [comboId], references: [id], onDelete: Cascade)
  
  // Mastery tracking
  timesUsed    Int      @default(0)
  perfectHits  Int      @default(0)
  masteryLevel Int      @default(1) // 1-5 mastery levels
  
  learnedAt    DateTime @default(now())
  lastUsedAt   DateTime?
  
  @@unique([heroId, comboId])
  @@map("hero_combo_mastery")
}

// Enhanced Location Discovery System
model HeroLocationDiscovery {
  id         String   @id @default(cuid())
  heroId     String
  hero       Hero     @relation(fields: [heroId], references: [id], onDelete: Cascade)
  venueId    String
  venue      DanceVenue @relation(fields: [venueId], references: [id], onDelete: Cascade)
  
  discoveredAt DateTime @default(now())
  firstVisit   Boolean  @default(true)
  visitCount   Int      @default(1)
  
  @@unique([heroId, venueId])
  @@map("hero_location_discovery")
}

// Enhanced Battle System
model Battle {
  id          String   @id @default(cuid())
  
  // Battle participants
  hero1Id     String
  hero1       Hero     @relation("BattleHero1", fields: [hero1Id], references: [id])
  hero2Id     String?  // Null for AI opponents
  hero2       Hero?    @relation("BattleHero2", fields: [hero2Id], references: [id])
  aiOpponentId String? // For AI battles
  
  // Battle location and context
  venueId     String?
  venue       DanceVenue? @relation(fields: [venueId], references: [id])
  danceStyle  DanceStyle
  battleType  BattleType @default(SINGLE_PLAYER)
  
  // Battle state
  status      BattleStatus @default(ACTIVE)
  currentTurn Int      @default(1)
  maxTurns    Int      @default(10)
  
  // Health and mana tracking
  hero1Health Int
  hero1Mana   Int
  hero2Health Int?
  hero2Mana   Int?
  
  // Battle results
  winnerId    String?
  winCondition String? // "KNOCKOUT", "POINTS", "FORFEIT"
  
  // Battle moves and combos used
  battleMoves BattleMoveUsage[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  endedAt     DateTime?
  
  @@map("battles")
}

model BattleMoveUsage {
  id        String   @id @default(cuid())
  battleId  String
  battle    Battle   @relation(fields: [battleId], references: [id], onDelete: Cascade)
  
  heroId    String
  moveId    String?  // Regular move
  comboId   String?  // Combo move
  
  // Timing and execution
  timingScore Int    // 0-100 timing accuracy
  damage      Int
  turn        Int
  
  // Body movement tracking (for combos)
  bodySequence String? // Actual sequence pressed
  beatAccuracy Float?  // Rhythm accuracy percentage
  
  createdAt   DateTime @default(now())
  
  @@map("battle_move_usage")
}

// New Enums
enum VenueType {
  DANCE_ACADEMY
  UNDERGROUND_CLUB
  COMPETITION_HALL
  FESTIVAL_GROUND
  MYSTICAL_STUDIO
  PRACTICE_ROOM
  ROOFTOP_TERRACE
  BEACH_CLUB
  HISTORIC_BALLROOM
}

enum EventType {
  TOURNAMENT
  WORKSHOP
  SOCIAL_DANCE
  BATTLE_ROYALE
  RHYTHM_CHALLENGE
  STYLE_SHOWCASE
  MASTER_CLASS
}

enum QuestType {
  DAILY
  WEEKLY
  MONTHLY
  STORY
  SPECIAL
  SOCIAL
}

enum QuestCategory {
  BATTLE
  EXPLORATION
  SOCIAL
  SKILL_DEVELOPMENT
  COLLECTION
  ACHIEVEMENT
  TOURNAMENT
  STYLE_MASTERY
}
