#!/usr/bin/env node

/**
 * Enterprise SEO Monitoring and Analytics Script
 * Provides comprehensive SEO performance tracking and reporting
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  domain: process.env.NODE_ENV === 'production' ? 'www.social-dance.org' : 'localhost:3001',
  useHttps: process.env.NODE_ENV === 'production',
  monitoringUrls: [
    '/',
    '/events',
    '/clubs', 
    '/dancers',
    '/djs',
    '/singers',
    '/article/salsa-history',
    '/article/bachata-moves',
    '/full-top-voted',
    '/leaderboard/all'
  ],
  coreWebVitalsThresholds: {
    lcp: 2500, // Largest Contentful Paint (ms)
    fid: 100,  // First Input Delay (ms)
    cls: 0.1   // Cumulative Layout Shift
  }
};

/**
 * Makes HTTP/HTTPS request and returns response data
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = CONFIG.useHttps ? https : require('http');
    const fullUrl = `${CONFIG.useHttps ? 'https' : 'http'}://${CONFIG.domain}${url}`;
    
    const requestOptions = {
      timeout: 10000,
      headers: {
        'User-Agent': 'SEO-Monitor/1.0 (Enterprise SEO Analytics)',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        ...options.headers
      }
    };
    
    const req = protocol.get(fullUrl, requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          url: fullUrl,
          statusCode: res.statusCode,
          headers: res.headers,
          body: data,
          responseTime: Date.now() - startTime
        });
      });
    });
    
    const startTime = Date.now();
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error(`Request timeout for ${fullUrl}`));
    });
  });
}

/**
 * Analyzes SEO elements in HTML content
 */
function analyzeSEOElements(html, url) {
  const analysis = {
    url,
    timestamp: new Date().toISOString(),
    seoScore: 0,
    issues: [],
    recommendations: []
  };
  
  // Title analysis
  const titleMatch = html.match(/<title>(.*?)<\/title>/i);
  if (titleMatch) {
    const title = titleMatch[1];
    analysis.title = title;
    analysis.titleLength = title.length;
    
    if (title.length < 30) {
      analysis.issues.push('Title too short (< 30 characters)');
    } else if (title.length > 60) {
      analysis.issues.push('Title too long (> 60 characters)');
    } else {
      analysis.seoScore += 10;
    }
  } else {
    analysis.issues.push('Missing title tag');
  }
  
  // Meta description analysis
  const descMatch = html.match(/<meta\s+name=["']description["']\s+content=["']([^"']+)["']/i);
  if (descMatch) {
    const description = descMatch[1];
    analysis.description = description;
    analysis.descriptionLength = description.length;
    
    if (description.length < 120) {
      analysis.issues.push('Meta description too short (< 120 characters)');
    } else if (description.length > 160) {
      analysis.issues.push('Meta description too long (> 160 characters)');
    } else {
      analysis.seoScore += 10;
    }
  } else {
    analysis.issues.push('Missing meta description');
  }
  
  // Canonical URL check
  const canonicalMatch = html.match(/<link\s+rel=["']canonical["']\s+href=["']([^"']+)["']/i);
  if (canonicalMatch) {
    analysis.canonicalUrl = canonicalMatch[1];
    analysis.seoScore += 5;
  } else {
    analysis.issues.push('Missing canonical URL');
  }
  
  // Open Graph tags
  const ogTags = html.match(/<meta\s+property=["']og:[^"']+["']\s+content=["']([^"']+)["']/gi) || [];
  analysis.openGraphTags = ogTags.length;
  if (ogTags.length >= 4) {
    analysis.seoScore += 10;
  } else {
    analysis.issues.push('Insufficient Open Graph tags');
  }
  
  // Twitter Card tags
  const twitterTags = html.match(/<meta\s+name=["']twitter:[^"']+["']\s+content=["']([^"']+)["']/gi) || [];
  analysis.twitterCardTags = twitterTags.length;
  if (twitterTags.length >= 3) {
    analysis.seoScore += 5;
  }
  
  // Structured data
  const structuredDataMatches = html.match(/<script[^>]+type=["']application\/ld\+json["'][^>]*>(.*?)<\/script>/gis) || [];
  analysis.structuredDataCount = structuredDataMatches.length;
  if (structuredDataMatches.length > 0) {
    analysis.seoScore += 15;
  } else {
    analysis.issues.push('No structured data found');
  }
  
  // Heading structure
  const h1Tags = html.match(/<h1[^>]*>(.*?)<\/h1>/gi) || [];
  const h2Tags = html.match(/<h2[^>]*>(.*?)<\/h2>/gi) || [];
  
  analysis.headingStructure = {
    h1Count: h1Tags.length,
    h2Count: h2Tags.length
  };
  
  if (h1Tags.length === 1) {
    analysis.seoScore += 10;
  } else if (h1Tags.length === 0) {
    analysis.issues.push('Missing H1 tag');
  } else {
    analysis.issues.push('Multiple H1 tags found');
  }
  
  // Image alt text analysis
  const imgTags = html.match(/<img[^>]*>/gi) || [];
  const imgsWithAlt = html.match(/<img[^>]*alt=["'][^"']*["'][^>]*>/gi) || [];
  
  analysis.images = {
    total: imgTags.length,
    withAlt: imgsWithAlt.length,
    missingAlt: imgTags.length - imgsWithAlt.length
  };
  
  if (analysis.images.missingAlt > 0) {
    analysis.issues.push(`${analysis.images.missingAlt} images missing alt text`);
  } else if (analysis.images.total > 0) {
    analysis.seoScore += 5;
  }
  
  // Mobile optimization
  const viewportMatch = html.match(/<meta\s+name=["']viewport["']/i);
  if (viewportMatch) {
    analysis.seoScore += 5;
  } else {
    analysis.issues.push('Missing viewport meta tag');
  }
  
  // Performance hints
  const preloadTags = html.match(/<link\s+rel=["']preload["'][^>]*>/gi) || [];
  const preconnectTags = html.match(/<link\s+rel=["']preconnect["'][^>]*>/gi) || [];
  
  analysis.performanceHints = {
    preload: preloadTags.length,
    preconnect: preconnectTags.length
  };
  
  if (preloadTags.length > 0 || preconnectTags.length > 0) {
    analysis.seoScore += 5;
  }
  
  // Security headers check
  const cspMatch = html.match(/<meta\s+http-equiv=["']Content-Security-Policy["']/i);
  if (cspMatch) {
    analysis.seoScore += 3;
  }
  
  // Calculate final score
  analysis.seoScore = Math.min(100, analysis.seoScore);
  
  // Generate recommendations
  if (analysis.seoScore < 70) {
    analysis.recommendations.push('Focus on fixing critical SEO issues');
  }
  if (analysis.structuredDataCount === 0) {
    analysis.recommendations.push('Implement structured data markup');
  }
  if (analysis.openGraphTags < 4) {
    analysis.recommendations.push('Add comprehensive Open Graph tags');
  }
  
  return analysis;
}

/**
 * Checks Core Web Vitals and performance metrics
 */
function analyzePerformance(responseTime, headers) {
  const performance = {
    responseTime,
    serverTiming: headers['server-timing'] || null,
    cacheControl: headers['cache-control'] || null,
    contentEncoding: headers['content-encoding'] || null,
    contentLength: parseInt(headers['content-length']) || 0,
    etag: headers['etag'] || null
  };
  
  // Response time analysis
  if (responseTime < 200) {
    performance.responseTimeScore = 'excellent';
  } else if (responseTime < 500) {
    performance.responseTimeScore = 'good';
  } else if (responseTime < 1000) {
    performance.responseTimeScore = 'fair';
  } else {
    performance.responseTimeScore = 'poor';
  }
  
  // Compression check
  performance.isCompressed = !!headers['content-encoding'];
  
  // Caching analysis
  performance.isCached = !!(headers['cache-control'] || headers['etag']);
  
  return performance;
}

/**
 * Monitors sitemap accessibility and structure
 */
async function checkSitemap() {
  try {
    const response = await makeRequest('/sitemap.xml');
    
    const analysis = {
      accessible: response.statusCode === 200,
      statusCode: response.statusCode,
      responseTime: response.responseTime
    };
    
    if (response.statusCode === 200) {
      const urlMatches = response.body.match(/<loc>(.*?)<\/loc>/g) || [];
      analysis.urlCount = urlMatches.length;
      analysis.urls = urlMatches.map(match => match.replace(/<\/?loc>/g, ''));
      
      // Check for canonical URLs
      const canonicalUrls = analysis.urls.filter(url => url.includes('www.social-dance.org'));
      analysis.canonicalUrlsCount = canonicalUrls.length;
      analysis.hasCanonicalUrls = canonicalUrls.length > 0;
    }
    
    return analysis;
  } catch (error) {
    return {
      accessible: false,
      error: error.message
    };
  }
}

/**
 * Checks robots.txt file
 */
async function checkRobotsTxt() {
  try {
    const response = await makeRequest('/robots.txt');
    
    const analysis = {
      accessible: response.statusCode === 200,
      statusCode: response.statusCode,
      responseTime: response.responseTime
    };
    
    if (response.statusCode === 200) {
      analysis.content = response.body;
      analysis.hasSitemap = response.body.includes('Sitemap:');
      analysis.hasUserAgent = response.body.includes('User-agent:');
      analysis.hasDisallow = response.body.includes('Disallow:');
    }
    
    return analysis;
  } catch (error) {
    return {
      accessible: false,
      error: error.message
    };
  }
}

/**
 * Generates comprehensive SEO report
 */
async function generateSEOReport() {
  console.log('🔍 Starting Enterprise SEO Monitoring...\n');
  
  const report = {
    timestamp: new Date().toISOString(),
    domain: CONFIG.domain,
    environment: process.env.NODE_ENV || 'development',
    pages: [],
    sitemap: null,
    robotsTxt: null,
    summary: {
      totalPages: 0,
      averageScore: 0,
      totalIssues: 0,
      averageResponseTime: 0
    }
  };
  
  // Check sitemap
  console.log('📄 Checking sitemap...');
  report.sitemap = await checkSitemap();
  
  // Check robots.txt
  console.log('🤖 Checking robots.txt...');
  report.robotsTxt = await checkRobotsTxt();
  
  // Analyze each page
  console.log('📊 Analyzing pages...\n');
  
  for (const url of CONFIG.monitoringUrls) {
    try {
      console.log(`Analyzing: ${url}`);
      const response = await makeRequest(url);
      
      if (response.statusCode === 200) {
        const seoAnalysis = analyzeSEOElements(response.body, url);
        const performanceAnalysis = analyzePerformance(response.responseTime, response.headers);
        
        const pageReport = {
          ...seoAnalysis,
          performance: performanceAnalysis,
          statusCode: response.statusCode
        };
        
        report.pages.push(pageReport);
        
        console.log(`  ✅ Score: ${seoAnalysis.seoScore}/100`);
        console.log(`  ⏱️  Response: ${response.responseTime}ms`);
        if (seoAnalysis.issues.length > 0) {
          console.log(`  ⚠️  Issues: ${seoAnalysis.issues.length}`);
        }
      } else {
        console.log(`  ❌ HTTP ${response.statusCode}`);
        report.pages.push({
          url,
          statusCode: response.statusCode,
          error: `HTTP ${response.statusCode}`
        });
      }
      
      // Add delay between requests
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      report.pages.push({
        url,
        error: error.message
      });
    }
  }
  
  // Calculate summary statistics
  const validPages = report.pages.filter(page => page.seoScore !== undefined);
  if (validPages.length > 0) {
    report.summary.totalPages = validPages.length;
    report.summary.averageScore = Math.round(
      validPages.reduce((sum, page) => sum + page.seoScore, 0) / validPages.length
    );
    report.summary.totalIssues = validPages.reduce((sum, page) => sum + page.issues.length, 0);
    report.summary.averageResponseTime = Math.round(
      validPages.reduce((sum, page) => sum + page.performance.responseTime, 0) / validPages.length
    );
  }
  
  return report;
}

/**
 * Saves report to file and displays summary
 */
function saveAndDisplayReport(report) {
  // Save detailed report
  const reportPath = path.join(__dirname, 'logs', `seo-report-${Date.now()}.json`);
  
  // Ensure logs directory exists
  const logsDir = path.join(__dirname, 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  // Display summary
  console.log('\n📈 SEO MONITORING SUMMARY');
  console.log('========================');
  console.log(`Domain: ${report.domain}`);
  console.log(`Environment: ${report.environment}`);
  console.log(`Timestamp: ${report.timestamp}`);
  console.log(`\n📊 Performance Metrics:`);
  console.log(`  Pages Analyzed: ${report.summary.totalPages}`);
  console.log(`  Average SEO Score: ${report.summary.averageScore}/100`);
  console.log(`  Total Issues Found: ${report.summary.totalIssues}`);
  console.log(`  Average Response Time: ${report.summary.averageResponseTime}ms`);
  
  console.log(`\n🗺️  Sitemap Status:`);
  if (report.sitemap.accessible) {
    console.log(`  ✅ Accessible (${report.sitemap.urlCount} URLs)`);
    console.log(`  📍 Canonical URLs: ${report.sitemap.canonicalUrlsCount}`);
  } else {
    console.log(`  ❌ Not accessible`);
  }
  
  console.log(`\n🤖 Robots.txt Status:`);
  if (report.robotsTxt.accessible) {
    console.log(`  ✅ Accessible`);
    console.log(`  📄 Has sitemap reference: ${report.robotsTxt.hasSitemap ? 'Yes' : 'No'}`);
  } else {
    console.log(`  ❌ Not accessible`);
  }
  
  // Show top issues
  const allIssues = report.pages.flatMap(page => page.issues || []);
  const issueFrequency = {};
  allIssues.forEach(issue => {
    issueFrequency[issue] = (issueFrequency[issue] || 0) + 1;
  });
  
  const topIssues = Object.entries(issueFrequency)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);
  
  if (topIssues.length > 0) {
    console.log(`\n⚠️  Top Issues:`);
    topIssues.forEach(([issue, count]) => {
      console.log(`  • ${issue} (${count} pages)`);
    });
  }
  
  console.log(`\n💾 Detailed report saved: ${reportPath}`);
  console.log('\n🎯 Next Steps:');
  console.log('1. Review detailed report for specific page issues');
  console.log('2. Fix critical SEO issues identified');
  console.log('3. Monitor Core Web Vitals in production');
  console.log('4. Set up automated monitoring for continuous improvement');
}

/**
 * Main execution function
 */
async function main() {
  try {
    const report = await generateSEOReport();
    saveAndDisplayReport(report);
  } catch (error) {
    console.error('❌ SEO monitoring failed:', error.message);
    process.exit(1);
  }
}

// Run if executed directly
if (require.main === module) {
  main();
}

module.exports = {
  generateSEOReport,
  analyzeSEOElements,
  checkSitemap,
  checkRobotsTxt
}; 