#!/usr/bin/env node

/**
 * SEO Enhancement Testing Script
 * Tests the implemented SEO improvements for social-dance.org
 */

const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_DOMAIN = 'localhost:3001'; // Change to 'www.social-dance.org' for production testing
const USE_HTTPS = false; // Change to true for production testing

// Test URLs
const TEST_URLS = [
  '/',
  '/events',
  '/clubs',
  '/dancers',
  '/article/salsa-history',
  '/full-top-voted',
  '/leaderboard/all'
];

/**
 * Makes an HTTP request and returns the response
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const protocol = USE_HTTPS ? https : http;
    const fullUrl = `${USE_HTTPS ? 'https' : 'http'}://${TEST_DOMAIN}${url}`;
    
    console.log(`Testing: ${fullUrl}`);
    
    const req = protocol.get(fullUrl, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          url: fullUrl,
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * Extracts meta tags from HTML
 */
function extractMetaTags(html) {
  const metaTags = {};
  
  // Extract title
  const titleMatch = html.match(/<title>(.*?)<\/title>/i);
  if (titleMatch) {
    metaTags.title = titleMatch[1];
  }
  
  // Extract meta tags
  const metaMatches = html.match(/<meta[^>]+>/gi) || [];
  metaMatches.forEach(meta => {
    const nameMatch = meta.match(/name=["']([^"']+)["']/i);
    const propertyMatch = meta.match(/property=["']([^"']+)["']/i);
    const contentMatch = meta.match(/content=["']([^"']+)["']/i);
    
    if (contentMatch) {
      const key = nameMatch ? nameMatch[1] : (propertyMatch ? propertyMatch[1] : null);
      if (key) {
        metaTags[key] = contentMatch[1];
      }
    }
  });
  
  // Extract canonical link
  const canonicalMatch = html.match(/<link[^>]+rel=["']canonical["'][^>]*href=["']([^"']+)["']/i);
  if (canonicalMatch) {
    metaTags.canonical = canonicalMatch[1];
  }
  
  // Extract structured data
  const structuredDataMatches = html.match(/<script[^>]+type=["']application\/ld\+json["'][^>]*>(.*?)<\/script>/gis) || [];
  metaTags.structuredData = structuredDataMatches.map(match => {
    const jsonMatch = match.match(/<script[^>]*>(.*?)<\/script>/is);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[1].trim());
      } catch (e) {
        return null;
      }
    }
    return null;
  }).filter(Boolean);
  
  return metaTags;
}

/**
 * Tests SEO enhancements for a single URL
 */
async function testUrl(url) {
  try {
    const response = await makeRequest(url);
    const metaTags = extractMetaTags(response.body);
    
    console.log(`\n=== SEO Test Results for ${url} ===`);
    console.log(`Status Code: ${response.statusCode}`);
    
    // Check essential SEO elements
    const checks = {
      'Title Tag': !!metaTags.title,
      'Meta Description': !!metaTags.description,
      'Canonical URL': !!metaTags.canonical,
      'Open Graph Title': !!metaTags['og:title'],
      'Open Graph Description': !!metaTags['og:description'],
      'Open Graph Image': !!metaTags['og:image'],
      'Twitter Card': !!metaTags['twitter:card'],
      'Robots Meta': !!metaTags.robots,
      'Structured Data': metaTags.structuredData.length > 0
    };
    
    console.log('\nSEO Elements Check:');
    Object.entries(checks).forEach(([check, passed]) => {
      console.log(`  ${passed ? '✅' : '❌'} ${check}`);
    });
    
    // Display key meta tags
    console.log('\nKey Meta Tags:');
    if (metaTags.title) console.log(`  Title: ${metaTags.title}`);
    if (metaTags.description) console.log(`  Description: ${metaTags.description.substring(0, 100)}...`);
    if (metaTags.canonical) console.log(`  Canonical: ${metaTags.canonical}`);
    if (metaTags.robots) console.log(`  Robots: ${metaTags.robots}`);
    
    // Check for canonical URL consistency
    if (metaTags.canonical) {
      const expectedCanonical = `https://www.social-dance.org${url}`;
      const canonicalCorrect = metaTags.canonical === expectedCanonical;
      console.log(`  Canonical URL Correct: ${canonicalCorrect ? '✅' : '❌'}`);
      if (!canonicalCorrect) {
        console.log(`    Expected: ${expectedCanonical}`);
        console.log(`    Found: ${metaTags.canonical}`);
      }
    }
    
    console.log(`\nStructured Data Found: ${metaTags.structuredData.length} items`);
    metaTags.structuredData.forEach((data, index) => {
      console.log(`  ${index + 1}. ${data['@type'] || 'Unknown Type'}`);
    });
    
    return {
      url,
      passed: Object.values(checks).every(Boolean),
      checks,
      metaTags
    };
    
  } catch (error) {
    console.error(`\n❌ Error testing ${url}:`, error.message);
    return {
      url,
      passed: false,
      error: error.message
    };
  }
}

/**
 * Tests sitemap accessibility
 */
async function testSitemap() {
  console.log('\n=== Testing Sitemap ===');
  
  try {
    const response = await makeRequest('/sitemap.xml');
    
    if (response.statusCode === 200) {
      console.log('✅ Sitemap accessible');
      
      // Check if sitemap contains canonical URLs
      const canonicalUrlsFound = response.body.includes('https://www.social-dance.org');
      console.log(`✅ Contains canonical URLs: ${canonicalUrlsFound ? 'Yes' : 'No'}`);
      
      // Count URLs in sitemap
      const urlMatches = response.body.match(/<loc>/g) || [];
      console.log(`📊 URLs in sitemap: ${urlMatches.length}`);
      
    } else {
      console.log(`❌ Sitemap not accessible (Status: ${response.statusCode})`);
    }
  } catch (error) {
    console.log(`❌ Error accessing sitemap: ${error.message}`);
  }
}

/**
 * Tests robots.txt
 */
async function testRobotsTxt() {
  console.log('\n=== Testing Robots.txt ===');
  
  try {
    const robotsPath = path.join(__dirname, 'robots.txt');
    if (fs.existsSync(robotsPath)) {
      const robotsContent = fs.readFileSync(robotsPath, 'utf8');
      console.log('✅ Robots.txt exists');
      
      // Check for sitemap reference
      const hasSitemap = robotsContent.includes('Sitemap:');
      console.log(`✅ Contains sitemap reference: ${hasSitemap ? 'Yes' : 'No'}`);
      
      // Check for canonical domain in sitemap URL
      const hasCanonicalSitemap = robotsContent.includes('https://www.social-dance.org/sitemap.xml');
      console.log(`✅ Uses canonical sitemap URL: ${hasCanonicalSitemap ? 'Yes' : 'No'}`);
      
    } else {
      console.log('❌ Robots.txt not found');
    }
  } catch (error) {
    console.log(`❌ Error checking robots.txt: ${error.message}`);
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🔍 Starting SEO Enhancement Tests');
  console.log(`Testing domain: ${TEST_DOMAIN}`);
  console.log(`Using HTTPS: ${USE_HTTPS}`);
  
  const results = [];
  
  // Test individual URLs
  for (const url of TEST_URLS) {
    const result = await testUrl(url);
    results.push(result);
    
    // Add delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Test sitemap and robots.txt
  await testSitemap();
  await testRobotsTxt();
  
  // Summary
  console.log('\n=== Test Summary ===');
  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  
  console.log(`Passed: ${passedTests}/${totalTests} URLs`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All SEO tests passed!');
  } else {
    console.log('⚠️  Some SEO tests failed. Check the details above.');
    
    const failedUrls = results.filter(r => !r.passed).map(r => r.url);
    console.log('Failed URLs:', failedUrls);
  }
  
  console.log('\n📋 Next Steps:');
  console.log('1. Deploy these changes to production');
  console.log('2. Submit updated sitemap to Google Search Console');
  console.log('3. Request re-indexing of key pages');
  console.log('4. Monitor Google Search Console for improvements');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testUrl,
  testSitemap,
  testRobotsTxt
}; 