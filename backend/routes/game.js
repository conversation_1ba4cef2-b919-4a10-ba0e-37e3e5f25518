const express = require('express');
const { body, param, validationResult } = require('express-validator');
const { authenticateJWT } = require('../middleware/auth');
const gameService = require('../services/gameService');
const battleService = require('../services/battleService');
const logger = require('../utils/logger');
const { PrismaClient } = require('@prisma/client');

const router = express.Router();
const prisma = new PrismaClient();

// ---- Hero Management Routes ---- //

// Create a new hero
router.post('/hero', 
  authenticateJWT,
  [
    body('name').isString().trim().isLength({ min: 2, max: 30 }).withMessage('Hero name must be between 2 and 30 characters'),
    body('trait').isIn(['ON2_TIMING', 'SPIN_MASTER', 'SMOOTH_LEADS', 'FOOTWORK_WIZARD', 'SALSA_FIRE', 'BACHATA_SOUL', 'RHYTHM_WARRIOR']).withMessage('Invalid trait')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, errors: errors.array() });
      }

      const { name, trait } = req.body;
      const userId = req.user.id;

      // Check if hero already exists
      const existingHero = await prisma.hero.findUnique({
        where: { userId }
      });

      if (existingHero) {
        return res.status(400).json({ success: false, error: 'Hero already exists for this user' });
      }

      // Create new hero
      const hero = await prisma.hero.create({
        data: {
          userId,
          name,
          level: 1,
          experience: 0,
          energy: 10,
          health: 100,
          mana: 50,
          attack: 10,
          defense: 10,
          speed: 10,
          luck: 10,
          primaryStyle: 'SALSA',
          stylePoints: { SALSA: 10 },
          skills: { TIMING: 5, FOOTWORK: 5 },
          equippedItems: {},
          inventory: [],
          traits: [trait],
          traitPoints: 3,
          skillPoints: 5
        },
        include: {
          user: true,
          equipment: true,
          questProgress: {
            include: { quest: true }
          }
        }
      });
      
      logger.info(`Created new hero for user ${userId}: ${hero.name}`);
      return res.status(201).json({ success: true, hero });
    } catch (error) {
      logger.error('Error creating hero', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// Get hero details
router.get('/hero', 
  authenticateJWT,
  async (req, res) => {
    try {
      const userId = req.user.id;
      
      // Find or create the hero for this user
      const hero = await gameService.getOrCreateHero(userId);
      
      return res.json({ success: true, hero });
    } catch (error) {
      logger.error('Error getting hero details', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// ---- Energy Management Routes ---- //

// Use energy
router.post('/energy/use',
  authenticateJWT,
  [
    body('amount').optional().isInt({ min: 1, max: 10 }).withMessage('Amount must be between 1 and 10')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, errors: errors.array() });
      }

      const userId = req.user.id;
      const amount = req.body.amount || 1;
      
      // Find the hero for this user
      const hero = await prisma.hero.findUnique({
        where: { userId }
      });
      
      if (!hero) {
        return res.status(404).json({ success: false, error: 'Hero not found' });
      }
      
      const result = await gameService.useEnergy(hero.id, amount);
      
      if (!result.success) {
        return res.status(400).json(result);
      }
      
      return res.json(result);
    } catch (error) {
      logger.error('Error using energy', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// ---- Quest Routes ---- //

// Get quests
router.get('/quests',
  authenticateJWT,
  async (req, res) => {
    try {
      const userId = req.user.id;
      const hero = await gameService.getOrCreateHero(userId);
      
      const quests = await gameService.getActiveQuests(hero.id);
      
      // Transform quest data to match frontend expectations
      const transformedQuests = quests.map(quest => {
        const progress = quest.progress && quest.progress.length > 0 ? quest.progress[0] : null;
        
        return {
          id: quest.id,
          title: quest.name,
          description: quest.description,
          type: quest.type,
          category: quest.category,
          targetValue: quest.maxProgress, // Map maxProgress to targetValue
          progress: progress ? progress.progress : 0,
          completed: progress ? progress.completed : false,
          claimedAt: progress ? progress.claimedAt : null,
          rewards: {
            experience: quest.xpReward,
            coins: quest.coinReward,
            skillPoints: quest.skillPoints,
            traitPoints: quest.traitPoints,
            equipment: quest.itemRewards || []
          },
          expiresAt: quest.endDate,
          danceStyle: quest.danceStyle,
          minLevel: quest.minLevel,
          maxLevel: quest.maxLevel
        };
      });
      
      return res.json({ success: true, quests: transformedQuests });
    } catch (error) {
      logger.error('Error getting quests', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// Claim quest reward
router.post('/quests/:questId/claim',
  authenticateJWT,
  [
    param('questId').isString().withMessage('Quest ID is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, errors: errors.array() });
      }

      const userId = req.user.id;
      const { questId } = req.params;
      
      const hero = await gameService.getOrCreateHero(userId);
      
      // Find quest progress
      const questProgress = await prisma.questProgress.findUnique({
        where: {
          heroId_questId: {
            heroId: hero.id,
            questId: questId
          }
        },
        include: {
          quest: true
        }
      });

      if (!questProgress || !questProgress.completed || questProgress.claimedAt) {
        return res.status(400).json({ success: false, error: 'Quest not completed or already claimed' });
      }

      // Award rewards
      const result = await gameService.awardQuestRewards(hero.id, questProgress.quest);
      
      // Mark as claimed
      await prisma.questProgress.update({
        where: {
          heroId_questId: {
            heroId: hero.id,
            questId: questId
          }
        },
        data: {
          claimedAt: new Date()
        }
      });
      
      return res.json({ success: true, ...result });
    } catch (error) {
      logger.error('Error claiming quest reward', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// ---- Battle Routes ---- //

// Get available AI opponents
router.get('/opponents',
  authenticateJWT,
  async (req, res) => {
    try {
      const userId = req.user.id;
      
      // Find the hero for this user
      const hero = await prisma.hero.findUnique({
        where: { userId }
      });
      
      if (!hero) {
        return res.status(404).json({ success: false, error: 'Hero not found' });
      }
      
      // Get AI opponents (users with AI usernames)
      const aiOpponents = await prisma.hero.findMany({
        where: {
          user: {
            username: {
              endsWith: '_ai'
            }
          }
        },
        include: {
          user: {
            select: {
              username: true
            }
          }
        },
        orderBy: {
          level: 'asc'
        }
      });
      
      // Format the opponents for the frontend
      const formattedOpponents = aiOpponents.map(opponent => ({
        id: opponent.id,
        name: opponent.name,
        level: opponent.level,
        primaryStyle: opponent.primaryStyle || 'SALSA',
        maxHealth: 100 + ((opponent.level - 1) * 10), // Calculate maxHealth
        wins: opponent.wins,
        losses: opponent.losses,
        traits: opponent.traits
      }));
      
      return res.json({ 
        success: true, 
        opponents: formattedOpponents 
      });
    } catch (error) {
      logger.error('Error getting AI opponents', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// Get available moves
router.get('/moves',
  authenticateJWT,
  async (req, res) => {
    try {
      const userId = req.user.id;
      
      // Find the hero for this user
      const hero = await prisma.hero.findUnique({
        where: { userId }
      });
      
      if (!hero) {
        return res.status(404).json({ success: false, error: 'Hero not found' });
      }
      
      const result = await battleService.getAvailableMoves(hero.id);
      
      return res.json(result);
    } catch (error) {
      logger.error('Error getting available moves', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// Start a battle
router.post('/battle/start',
  authenticateJWT,
  [
    body('opponentId').isString().withMessage('Opponent ID is required'),
    body('danceStyle').isIn(['SALSA', 'BACHATA', 'KIZOMBA', 'ZOUK', 'CHACHA']).withMessage('Invalid dance style')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, errors: errors.array() });
      }

      const userId = req.user.id;
      const { opponentId, danceStyle } = req.body;
      
      const hero = await gameService.getOrCreateHero(userId);
      
      if (hero.energy < 1) {
        return res.status(400).json({ success: false, error: 'Not enough energy to battle' });
      }
      
      const battle = await gameService.startAIBattle(hero.id, opponentId, danceStyle);
      
      return res.json({ success: true, battle });
    } catch (error) {
      logger.error('Error starting battle', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// Submit battle turn
router.post('/battle/:battleId/turn',
  authenticateJWT,
  [
    param('battleId').isString().withMessage('Battle ID is required'),
    body('moveId').isString().withMessage('Move ID is required'),
    body('timingScore').optional().isInt({ min: 0, max: 100 }).withMessage('Timing score must be between 0 and 100')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, errors: errors.array() });
      }

      const userId = req.user.id;
      const { battleId } = req.params;
      const { moveId, timingScore = 85 } = req.body;
      
      const hero = await gameService.getOrCreateHero(userId);
      
      const result = await gameService.executeBattleMove(battleId, hero.id, moveId, timingScore);
      
      return res.json({ success: true, ...result });
    } catch (error) {
      logger.error('Error executing battle move', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// ---- WebSocket Event Route (Emotes) ---- //

// Send emote (routed to WebSocket)
router.post('/emote',
  authenticateJWT,
  [
    body('emoji').isString().withMessage('Emoji is required'),
    body('battleId').isString().withMessage('Battle ID is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, errors: errors.array() });
      }

      const userId = req.user.id;
      const { emoji, battleId } = req.body;
      
      // In a real implementation, we would:
      // 1. Validate that this battle exists and is active
      // 2. Broadcast the emote via WebSocket
      
      // For now, just log it
      logger.info(`Emote sent: ${emoji} by user ${userId} in battle ${battleId}`);
      
      // This would normally be handled by the WebSocket implementation
      // global.io.to(`battle_${battleId}`).emit('emote', {
      //   userId,
      //   emoji
      // });
      
      return res.json({
        success: true,
        message: 'Emote sent'
      });
    } catch (error) {
      logger.error('Error sending emote', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// ===== HERO MANAGEMENT =====

// Get or create hero for current user
router.get('/hero', authenticateJWT, async (req, res) => {
  try {
    const hero = await gameService.getOrCreateHero(req.user.id);
    const heroStats = await gameService.getHeroStats(hero.id);
    
    res.json({
      success: true,
      hero: heroStats
    });
  } catch (error) {
    logger.error('Error getting hero:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get hero data'
    });
  }
});

// Update hero name
router.put('/hero/name', authenticateJWT, async (req, res) => {
  try {
    const { name } = req.body;
    
    if (!name || name.trim().length < 2) {
      return res.status(400).json({
        success: false,
        error: 'Hero name must be at least 2 characters long'
      });
    }

    const hero = await gameService.getOrCreateHero(req.user.id);
    const updatedHero = await gameService.updateHeroStats(hero.id, { name: name.trim() });
    
    res.json({
      success: true,
      hero: updatedHero
    });
  } catch (error) {
    logger.error('Error updating hero name:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update hero name'
    });
  }
});

// ===== BATTLE SYSTEM =====

// Get available battle moves
router.get('/battle/moves',
  authenticateJWT,
  async (req, res) => {
    try {
      const moves = await prisma.battleMove.findMany({
        orderBy: [
          { unlockLevel: 'asc' },
          { power: 'desc' }
        ]
      });
      
      return res.json({ success: true, moves });
    } catch (error) {
      logger.error('Error getting battle moves', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// Get AI opponents
router.get('/battle/opponents',
  authenticateJWT,
  async (req, res) => {
    try {
      // Get AI opponents (heroes with user IDs starting with 'ai_')
      const opponents = await prisma.hero.findMany({
        where: {
          user: {
            id: {
              startsWith: 'ai_'
            }
          }
        },
        include: {
          user: true
        },
        orderBy: {
          level: 'asc'
        }
      });
      
      return res.json({ success: true, opponents });
    } catch (error) {
      logger.error('Error getting AI opponents', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// Start AI battle
router.post('/battle/start', authenticateJWT, async (req, res) => {
  try {
    const { opponentId, danceStyle } = req.body;
    
    if (!opponentId || !danceStyle) {
      return res.status(400).json({
        success: false,
        error: 'Opponent ID and dance style are required'
      });
    }

    const hero = await gameService.getOrCreateHero(req.user.id);
    const battle = await gameService.startAIBattle(hero.id, opponentId, danceStyle);
    
    res.json({
      success: true,
      battle
    });
  } catch (error) {
    logger.error('Error starting battle:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Execute battle move
router.post('/battle/:battleId/turn', authenticateJWT, async (req, res) => {
  try {
    const { battleId } = req.params;
    const { moveId, timingScore = 0 } = req.body;
    
    if (!moveId) {
      return res.status(400).json({
        success: false,
        error: 'Move ID is required'
      });
    }

    const hero = await gameService.getOrCreateHero(req.user.id);
    const result = await gameService.executeBattleMove(battleId, hero.id, moveId, timingScore);
    
    // Check for level up after battle
    if (result.moveResult.battleEnded && result.moveResult.winner === hero.id) {
      const levelUpResult = await gameService.checkLevelUp(hero.id);
      if (levelUpResult) {
        result.levelUp = levelUpResult;
      }
      
      // Update quest progress
      await gameService.updateQuestProgress(hero.id, 'BATTLE', 'battles_won', 1);
    }
    
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Error executing battle move:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Get battle history
router.get('/battle/history', authenticateJWT, async (req, res) => {
  try {
    const hero = await gameService.getOrCreateHero(req.user.id);
    
    const battles = await prisma.battleHistory.findMany({
      where: { heroId: hero.id },
      include: {
        opponent: {
          include: {
            user: {
              select: {
                username: true,
                displayName: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 20
    });

    res.json({
      success: true,
      battles
    });
  } catch (error) {
    logger.error('Error getting battle history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get battle history'
    });
  }
});

// ===== EQUIPMENT SYSTEM =====

// Get available equipment for purchase
router.get('/equipment/shop',
  authenticateJWT,
  async (req, res) => {
    try {
      const userId = req.user.id;
      const hero = await gameService.getOrCreateHero(userId);
      
      const equipment = await gameService.getAvailableEquipment(hero.level);
      
      return res.json({ success: true, equipment });
    } catch (error) {
      logger.error('Error getting equipment shop', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// Get hero's equipment inventory
router.get('/equipment/inventory',
  authenticateJWT,
  async (req, res) => {
    try {
      const userId = req.user.id;
      const hero = await gameService.getOrCreateHero(userId);
      
      const equipment = await prisma.equipment.findMany({
        where: {
          heroId: hero.id
        }
      });
      
      return res.json({ success: true, equipment });
    } catch (error) {
      logger.error('Error getting inventory', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// Purchase equipment
router.post('/equipment/purchase',
  authenticateJWT,
  [
    body('equipmentId').isString().withMessage('Equipment ID is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ success: false, errors: errors.array() });
      }

      const userId = req.user.id;
      const { equipmentId } = req.body;
      
      const hero = await gameService.getOrCreateHero(userId);
      
      const result = await gameService.purchaseEquipment(hero.id, equipmentId);
      
      return res.json(result);
    } catch (error) {
      logger.error('Error purchasing equipment', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// Equip item
router.post('/equipment/equip', authenticateJWT, async (req, res) => {
  try {
    const { equipmentId } = req.body;
    
    if (!equipmentId) {
      return res.status(400).json({
        success: false,
        error: 'Equipment ID is required'
      });
    }

    const hero = await gameService.getOrCreateHero(req.user.id);
    const result = await gameService.equipItem(hero.id, equipmentId);
    
    res.json(result);
  } catch (error) {
    logger.error('Error equipping item:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// ===== QUEST SYSTEM =====

// Get active quests
router.get('/quests', authenticateJWT, async (req, res) => {
  try {
    const hero = await gameService.getOrCreateHero(req.user.id);
    const quests = await gameService.getActiveQuests(hero.id);
    
    res.json({
      success: true,
      quests
    });
  } catch (error) {
    logger.error('Error getting quests:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get quests'
    });
  }
});

// Claim quest reward
router.post('/quests/:questId/claim', authenticateJWT, async (req, res) => {
  try {
    const { questId } = req.params;
    const hero = await gameService.getOrCreateHero(req.user.id);
    
    // Check if quest is completed
    const questProgress = await prisma.questProgress.findUnique({
      where: {
        heroId_questId: {
          heroId: hero.id,
          questId
        }
      },
      include: { quest: true }
    });

    if (!questProgress || !questProgress.completed) {
      return res.status(400).json({
        success: false,
        error: 'Quest not completed'
      });
    }

    if (questProgress.claimedAt) {
      return res.status(400).json({
        success: false,
        error: 'Quest reward already claimed'
      });
    }

    // Award rewards
    await gameService.awardQuestRewards(hero.id, questProgress.quest);
    
    // Mark as claimed
    await prisma.questProgress.update({
      where: {
        heroId_questId: {
          heroId: hero.id,
          questId
        }
      },
      data: { claimedAt: new Date() }
    });

    res.json({
      success: true,
      message: 'Quest reward claimed successfully'
    });
  } catch (error) {
    logger.error('Error claiming quest reward:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to claim quest reward'
    });
  }
});

// ===== SKILL SYSTEM =====

// Allocate skill points
router.post('/skills/allocate', authenticateJWT, async (req, res) => {
  try {
    const { skill, points } = req.body;
    
    if (!skill || !points || points < 1) {
      return res.status(400).json({
        success: false,
        error: 'Valid skill and points are required'
      });
    }

    const hero = await gameService.getOrCreateHero(req.user.id);
    
    if (hero.skillPoints < points) {
      return res.status(400).json({
        success: false,
        error: 'Not enough skill points'
      });
    }

    const currentSkills = hero.skills || {};
    const newSkillLevel = (currentSkills[skill] || 0) + points;
    
    const updatedSkills = {
      ...currentSkills,
      [skill]: newSkillLevel
    };

    await gameService.updateHeroStats(hero.id, {
      skills: updatedSkills,
      skillPoints: hero.skillPoints - points
    });

    res.json({
      success: true,
      message: `Allocated ${points} points to ${skill}`,
      newSkillLevel
    });
  } catch (error) {
    logger.error('Error allocating skill points:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to allocate skill points'
    });
  }
});

// Add trait
router.post('/traits/add', authenticateJWT, async (req, res) => {
  try {
    const { trait } = req.body;
    
    if (!trait) {
      return res.status(400).json({
        success: false,
        error: 'Trait is required'
      });
    }

    const hero = await gameService.getOrCreateHero(req.user.id);
    
    if (hero.traitPoints < 1) {
      return res.status(400).json({
        success: false,
        error: 'Not enough trait points'
      });
    }

    if (hero.traits.includes(trait)) {
      return res.status(400).json({
        success: false,
        error: 'Trait already acquired'
      });
    }

    const updatedTraits = [...hero.traits, trait];

    await gameService.updateHeroStats(hero.id, {
      traits: updatedTraits,
      traitPoints: hero.traitPoints - 1
    });

    res.json({
      success: true,
      message: `Acquired trait: ${trait}`,
      traits: updatedTraits
    });
  } catch (error) {
    logger.error('Error adding trait:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to add trait'
    });
  }
});

// ===== LEADERBOARDS =====

// Get global leaderboard
router.get('/leaderboard',
  authenticateJWT,
  async (req, res) => {
    try {
      const { type = 'level', limit = 10 } = req.query;
      
      let orderBy = {};
      switch (type) {
        case 'level':
          orderBy = [{ level: 'desc' }, { experience: 'desc' }];
          break;
        case 'wins':
          orderBy = [{ wins: 'desc' }, { level: 'desc' }];
          break;
        case 'winStreak':
          orderBy = [{ winStreak: 'desc' }, { wins: 'desc' }];
          break;
        default:
          orderBy = [{ level: 'desc' }, { experience: 'desc' }];
      }
      
      const leaderboard = await prisma.hero.findMany({
        take: parseInt(limit),
        orderBy,
        include: {
          user: {
            select: {
              id: true,
              username: true
            }
          }
        }
      });
      
      return res.json({ success: true, leaderboard });
    } catch (error) {
      logger.error('Error getting leaderboard', { error: error.message, stack: error.stack });
      return res.status(500).json({ success: false, error: 'Internal server error' });
    }
  }
);

// ===== LEGACY COMPATIBILITY =====

// Legacy skill check endpoint (for existing frontend)
router.post('/skill-check', authenticateJWT, async (req, res) => {
  try {
    const { score = 0 } = req.body;
    const hero = await gameService.getOrCreateHero(req.user.id);
    
    logger.debug(`SkillCheck hero=${hero.id} score=${score}`);
    
    // Update quest progress for timing-related quests
    if (score >= 95) {
      await gameService.updateQuestProgress(hero.id, 'SKILL', 'perfect_timing_hits', 1);
    }
    
    res.json({
      success: true,
      score,
      message: 'Skill check processed'
    });
  } catch (error) {
    logger.error('Error processing skill check:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process skill check'
    });
  }
});

// Legacy hero creation endpoint
router.post('/create-hero', authenticateJWT, async (req, res) => {
  try {
    const { name, trait } = req.body;
    
    // Check if user already has a hero
    const existingHero = await prisma.hero.findUnique({
      where: { userId: req.user.id }
    });

    if (existingHero) {
      return res.json({
        success: false,
        error: 'User already has a hero',
        hero: existingHero
      });
    }

    // Create hero with provided name and trait
    const hero = await prisma.hero.create({
      data: {
        userId: req.user.id,
        name: name || `Hero_${Date.now()}`,
        level: 1,
        experience: 0,
        energy: 10,
        health: 100,
        mana: 50,
        attack: 10,
        defense: 10,
        speed: 10,
        luck: 10,
        primaryStyle: 'SALSA',
        stylePoints: { SALSA: 10 },
        skills: { TIMING: 5, FOOTWORK: 5 },
        equippedItems: {},
        inventory: [],
        traits: trait ? [trait] : ['ON2_TIMING'],
        traitPoints: 3,
        skillPoints: 5
      }
    });

    logger.info(`Created new hero: ${name} with trait ${trait} for user ${req.user.id}`);
    
    res.json({
      success: true,
      hero
    });
  } catch (error) {
    logger.error('Error creating hero:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create hero'
    });
  }
});

// ===== WORLD MAP & VENUES =====

// Get venues for world map
router.get('/venues', authenticateJWT, async (req, res) => {
  try {
    const venues = await prisma.danceVenue.findMany({
      orderBy: { unlockLevel: 'asc' }
    });

    const formattedVenues = venues.map(venue => ({
      id: venue.id,
      name: venue.name,
      type: venue.type,
      description: venue.description,
      location: venue.location,
      coordinates: venue.coordinates,
      isHidden: venue.isHidden,
      unlockLevel: venue.unlockLevel,
      specialties: venue.specialties,
      difficulty: venue.difficulty,
      rewards: venue.rewards
    }));

    res.json({ success: true, venues: formattedVenues });
  } catch (error) {
    logger.error('Error fetching venues:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch venues' });
  }
});

// ===== COMBO SYSTEM =====

// Get combos for combo trainer
router.get('/combos', authenticateJWT, async (req, res) => {
  try {
    const hero = await gameService.getOrCreateHero(req.user.id);
    
    const combos = await prisma.battleCombo.findMany({
      include: {
        heroComboMastery: {
          where: { heroId: hero.id },
          take: 1
        }
      },
      orderBy: { unlockLevel: 'asc' }
    });

    const formattedCombos = combos.map(combo => ({
      id: combo.id,
      name: combo.name,
      danceStyle: combo.danceStyle,
      sequence: combo.bodySequence.split('+'), // Convert "LL+RL+HP" to ["LL", "RL", "HP"]
      beatCount: combo.beatCount,
      timingWindow: combo.timingWindow,
      difficulty: combo.difficulty,
      power: combo.damage, // Map damage to power for frontend compatibility
      description: combo.description,
      unlockLevel: combo.unlockLevel,
      masteryLevel: combo.heroComboMastery[0]?.masteryLevel || 0,
      usageCount: combo.heroComboMastery[0]?.timesUsed || 0
    }));

    res.json({ success: true, combos: formattedCombos });
  } catch (error) {
    logger.error('Error fetching combos:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch combos' });
  }
});

// Practice combo
router.post('/combos/:comboId/practice', authenticateJWT, async (req, res) => {
  try {
    const { comboId } = req.params;
    const { accuracy, timingData } = req.body;
    
    const hero = await gameService.getOrCreateHero(req.user.id);
    
    // Find or create combo mastery record
    let comboMastery = await prisma.heroComboMastery.findUnique({
      where: {
        heroId_comboId: {
          heroId: hero.id,
          comboId
        }
      }
    });

    if (!comboMastery) {
      comboMastery = await prisma.heroComboMastery.create({
        data: {
          heroId: hero.id,
          comboId,
          masteryLevel: 1,
          timesUsed: 1,
          perfectHits: accuracy >= 95 ? 1 : 0
        }
      });
    } else {
      // Update mastery
      const newUsageCount = comboMastery.timesUsed + 1;
      const newPerfectHits = comboMastery.perfectHits + (accuracy >= 95 ? 1 : 0);
      let newMasteryLevel = comboMastery.masteryLevel;
      
      // Increase mastery level based on usage and accuracy
      if (accuracy >= 90 && newUsageCount >= newMasteryLevel * 10) {
        newMasteryLevel = Math.min(5, newMasteryLevel + 1);
      }
      
      await prisma.heroComboMastery.update({
        where: { id: comboMastery.id },
        data: {
          timesUsed: newUsageCount,
          masteryLevel: newMasteryLevel,
          perfectHits: newPerfectHits,
          lastUsedAt: new Date()
        }
      });
    }

    // Award XP for successful practice
    if (accuracy >= 70) {
      const xpGain = Math.floor(accuracy / 10);
      await gameService.updateHeroStats(hero.id, {
        experience: hero.experience + xpGain
      });
      
      // Update quest progress
      await gameService.updateQuestProgress(hero.id, 'SKILL_DEVELOPMENT', 'combo_practice', 1);
      if (accuracy >= 95) {
        await gameService.updateQuestProgress(hero.id, 'SKILL_DEVELOPMENT', 'perfect_timing', 1);
      }
    }

    res.json({
      success: true,
      message: 'Combo practice recorded',
      masteryLevel: comboMastery.masteryLevel,
      accuracy
    });
  } catch (error) {
    logger.error('Error recording combo practice:', error);
    res.status(500).json({ success: false, error: 'Failed to record combo practice' });
  }
});

// ===== VENUE BATTLE SYSTEM =====

// Start venue battle
router.post('/battle/venue/start', authenticateJWT, async (req, res) => {
  try {
    const { venueId, danceStyle } = req.body;
    
    if (!venueId || !danceStyle) {
      return res.status(400).json({
        success: false,
        error: 'Venue ID and dance style are required'
      });
    }

    const hero = await gameService.getOrCreateHero(req.user.id);
    const result = await battleService.startVenueBattle(hero.id, venueId, danceStyle);
    
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Error starting venue battle:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Execute combo in battle
router.post('/battle/:battleId/combo', authenticateJWT, async (req, res) => {
  try {
    const { battleId } = req.params;
    const { comboId, bodySequence, timingAccuracy = 85 } = req.body;
    
    if (!comboId || !bodySequence) {
      return res.status(400).json({
        success: false,
        error: 'Combo ID and body sequence are required'
      });
    }

    const hero = await gameService.getOrCreateHero(req.user.id);
    const result = await battleService.executeCombo(
      battleId, 
      hero.id, 
      comboId, 
      bodySequence, 
      timingAccuracy
    );
    
    // Check for level up after successful combo
    if (result.comboResult.battleEnded && result.comboResult.winner === hero.id) {
      const levelUpResult = await gameService.checkLevelUp(hero.id);
      if (levelUpResult) {
        result.levelUp = levelUpResult;
      }
      
      // Update quest progress
      await gameService.updateQuestProgress(hero.id, 'BATTLE', 'venue_battles_won', 1);
      await gameService.updateQuestProgress(hero.id, 'SKILL_DEVELOPMENT', 'combo_usage', 1);
      
      if (result.comboResult.effectiveness === 'PERFECT') {
        await gameService.updateQuestProgress(hero.id, 'SKILL_DEVELOPMENT', 'perfect_combos', 1);
      }
    }
    
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Error executing combo:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Discover venue
router.post('/venues/:venueId/discover', authenticateJWT, async (req, res) => {
  try {
    const { venueId } = req.params;
    
    const hero = await gameService.getOrCreateHero(req.user.id);
    const result = await battleService.discoverVenue(hero.id, venueId);
    
    // Update quest progress for exploration
    if (result.success) {
      await gameService.updateQuestProgress(hero.id, 'EXPLORATION', 'venues_discovered', 1);
      
      // Check for level up from discovery XP
      const levelUpResult = await gameService.checkLevelUp(hero.id);
      if (levelUpResult) {
        result.levelUp = levelUpResult;
      }
    }
    
    res.json(result);
  } catch (error) {
    logger.error('Error discovering venue:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to discover venue'
    });
  }
});

// Get hero's discovered venues
router.get('/venues/discovered', authenticateJWT, async (req, res) => {
  try {
    const hero = await gameService.getOrCreateHero(req.user.id);
    
    const discoveries = await prisma.heroLocationDiscovery.findMany({
      where: { heroId: hero.id },
      include: {
        venue: true
      }
    });
    
    const discoveredVenues = discoveries.map(d => ({
      venueId: d.venueId,
      venue: d.venue,
      discoveredAt: d.discoveredAt,
      visitCount: d.visitCount,
      firstVisit: d.firstVisit
    }));
    
    res.json({
      success: true,
      discoveredVenues
    });
  } catch (error) {
    logger.error('Error getting discovered venues:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get discovered venues'
    });
  }
});

// Get battle history with venues
router.get('/battle/venue/history', authenticateJWT, async (req, res) => {
  try {
    const hero = await gameService.getOrCreateHero(req.user.id);
    
    const battles = await prisma.battle.findMany({
      where: { 
        hero1Id: hero.id,
        battleType: 'SINGLE_PLAYER'
      },
      include: {
        venue: true,
        battleMoves: {
          include: {
            combo: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 20
    });

    const formattedBattles = battles.map(battle => ({
      id: battle.id,
      venue: battle.venue,
      danceStyle: battle.danceStyle,
      status: battle.status,
      winnerId: battle.winnerId,
      isVictory: battle.winnerId === hero.id,
      turns: battle.currentTurn,
      combosUsed: battle.battleMoves.filter(m => m.comboId).length,
      createdAt: battle.createdAt,
      endedAt: battle.endedAt
    }));

    res.json({
      success: true,
      battles: formattedBattles
    });
  } catch (error) {
    logger.error('Error getting venue battle history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get battle history'
    });
  }
});

module.exports = router; 