const express = require('express');
const logger = require('../utils/logger');
const { authenticateJWT } = require('../middleware/auth');
const { getAuthorizedYoutubeClient } = require('../utils/youtubeClient');

const router = express.Router();

// GET /api/youtube/search - Proxy for YouTube video search
router.get('/search', authenticateJWT, async (req, res) => {
    const query = req.query.q;
    const userId = req.user?.id;

    if (!query) {
        logger.warn('YouTube search called without query', { userId });
        return res.status(400).json({ message: 'Search query parameter \'q\' is required.' });
    }

    logger.info('Performing YouTube search', { query, userId });

    try {
        const youtube = await getAuthorizedYoutubeClient();

        try {
            const response = await youtube.search.list({
                part: ['snippet'], // Get title, description, thumbnails, channel title
                q: query,
                type: 'video', // Only search for videos
                maxResults: 50, // Limit results
                // safeSearch: 'moderate' // Optional: Filter results
            });

            const searchResults = response.data.items.map(item => ({
                videoId: item.id.videoId,
                title: item.snippet.title,
                channelTitle: item.snippet.channelTitle,
                thumbnailUrl: item.snippet.thumbnails?.default?.url || item.snippet.thumbnails?.medium?.url || '/img/placeholder-youtube.png' // Use default or medium thumbnail
            }));

            logger.info(`YouTube search successful for query "${query}"`, { userId, count: searchResults.length });
            res.json(searchResults);

        } catch (ytError) {
            logger.error('Error calling YouTube Search API', {
                 query, userId, error: ytError.message, code: ytError.code
            });

            // Check for quota error
            const isQuotaError = (ytError.code === 403 && ytError.errors?.some(e => e.reason === 'quotaExceeded'));
            if (isQuotaError) {
                logger.error('YouTube API Quota Exceeded during search', { query, userId });
                return res.status(429).json({ message: 'YouTube API quota limit reached. Cannot perform search at this time.' });
            }
            
            // Handle other specific YouTube API errors if needed
            // Example: Video not found (though less likely for search)

            // General API error
            return res.status(503).json({ message: 'Failed to communicate with YouTube API.' });
        }

    } catch (authError) {
        // Error getting the authorized client
        logger.error('Failed to get authorized YouTube client for search', { error: authError.message, userId });
        return res.status(503).json({ message: 'Server configuration error: Cannot authorize with YouTube.' });
    }
});

module.exports = router; 