const express = require('express');
const logger = require('../utils/logger');
const { PrismaClient, PlaylistMode } = require('@prisma/client');
const { getPlaylistItems } = require('../utils/youtubeApi'); // Import youtube helper
const { parseISO8601Duration } = require('../utils/durationUtils'); // Import duration helper
const { getAuthorizedYoutubeClient } = require('../utils/youtubeClient'); // Import client getter
const ytdl = require('ytdl-core'); // Import ytdl-core for YouTube audio extraction

const prisma = new PrismaClient();
const router = express.Router();

// GET /api/public/stream/:youtubeVideoId.mp3 - Stream audio from a YouTube video
router.get('/stream/:youtubeVideoId.mp3', async (req, res) => {
    const videoId = req.params.youtubeVideoId;
    
    if (!videoId || !ytdl.validateID(videoId)) {
        logger.error(`Invalid YouTube video ID requested: ${videoId}`);
        return res.status(400).send('Invalid YouTube video ID');
    }

    try {
        // Check if we're in EXTERNAL_MIRROR mode - in that case we shouldn't stream
        const settings = await prisma.adminSettings.findUnique({ 
            where: { singletonLock: true },
            select: { operationalMode: true }
        });
        
        // If in EXTERNAL_MIRROR mode, don't attempt streaming
        if (settings?.operationalMode === PlaylistMode.EXTERNAL_MIRROR) {
            logger.info(`Streaming for ${videoId} skipped - in EXTERNAL_MIRROR mode`);
            return res.status(204).end(); // No content - client should handle this gracefully
        }
        
        logger.info(`Streaming audio for YouTube video: ${videoId}`);
        
        // Set headers for audio streaming
        res.setHeader('Content-Type', 'audio/mpeg');
        res.setHeader('Transfer-Encoding', 'chunked');
        res.setHeader('Accept-Ranges', 'bytes');
        
        // Add cache control headers
        res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
        
        // Stream the audio-only format from YouTube
        const ytdlOptions = {
            quality: 'highestaudio',
            filter: 'audioonly',
            highWaterMark: 1 << 25, // 32MB buffer for better streaming
        };

        try {
            // Get stream info first to handle errors before we start streaming
            const info = await ytdl.getInfo(videoId);
            
            if (!info || !info.formats || info.formats.length === 0) {
                logger.error(`No formats available for video ${videoId}`);
                return res.status(404).send('Video formats not available');
            }
            
            // Add CORS headers for cross-origin requests
            res.setHeader('Access-Control-Allow-Origin', '*');
            
            // Start streaming
            const stream = ytdl(videoId, ytdlOptions);
            
            // Handle stream errors
            stream.on('error', (err) => {
                logger.error(`Stream error for video ${videoId}:`, err);
                // Only attempt to send an error if headers haven't been sent yet
                if (!res.headersSent) {
                    res.status(500).send('Error streaming video');
                } else {
                    // If headers already sent, just end the response
                    res.end();
                }
            });
            
            // Make sure the client is still connected before streaming
            req.on('close', () => {
                if (stream) {
                    stream.destroy();
                    logger.debug(`Client disconnected, stream destroyed for ${videoId}`);
                }
            });
            
            // Pipe the audio stream to the response
            stream.pipe(res);
            
        } catch (ytError) {
            logger.error(`YouTube error for video ${videoId}:`, ytError);
            res.status(500).send('Error streaming YouTube video');
        }
    } catch (error) {
        logger.error(`General error streaming video ${videoId}:`, error);
        res.status(500).send('Server error');
    }
});

// GET /api/public/player-state - Returns the current player state for public view
router.get('/player-state', async (req, res) => {
    logger.info('Public player state requested');
    
    try {
        // 1. Get Current Operational Mode & Settings
        const settings = await prisma.adminSettings.findUnique({ 
            where: { singletonLock: true },
            select: { 
                operationalMode: true, 
                externalMirrorPlaylistId: true 
            } // Fetch both mode and mirror ID
        });
        const mode = settings?.operationalMode ?? PlaylistMode.GENERATED; // Default if not set
        const externalMirrorId = settings?.externalMirrorPlaylistId; // Get the mirror ID
        logger.debug(`Public player state: Current mode is ${mode}`, { externalMirrorId });

        // 2. Initialize the base response structure
        let playerState = {
            youtubePlaylistId: null,
            externalMirrorPlaylistId: null,
            currentSong: null,
            upcomingSongs: [],
            playlistItems: [], // Initialize with empty array
            isPlaying: false,
            currentSongProgressSeconds: 0,
            operationalMode: mode, // Include mode in response
            lastUpdatedAt: new Date().toISOString(),
            activeStyleRotation: [], // Added: To store the expanded style rotation
        };

        // 3. Populate based on operational mode
        if (mode === PlaylistMode.GENERATED) {
            logger.debug('Public player state: Handling GENERATED mode.');
            
            // Fetch the active PlaylistTemplate to get its style rotation
            const activeTemplate = await prisma.playlistTemplate.findFirst({
                where: { isActive: true },
                select: { styleRotation: true }
            });

            let expandedStyleRotation = [];
            if (activeTemplate && activeTemplate.styleRotation && Array.isArray(activeTemplate.styleRotation)) {
                activeTemplate.styleRotation.forEach(block => {
                    if (block && typeof block.style === 'string' && typeof block.count === 'number') {
                        for (let i = 0; i < block.count; i++) {
                            expandedStyleRotation.push(block.style);
                        }
                    }
                });
                playerState.activeStyleRotation = expandedStyleRotation;
                logger.debug('Public player state: Active template rotation loaded', { rotationLength: expandedStyleRotation.length });
            } else {
                logger.warn('Public player state: No active playlist template found or rotation is invalid.');
            }

            const activePlaylist = await prisma.activePlaylist.findFirst({
                orderBy: { createdAt: 'desc' },
                include: {
                    playlistItems: {
                        orderBy: { order: 'asc' },
                        select: {
                            id: true, youtubeVideoId: true, title: true, thumbnailUrl: true,
                            channelTitle: true, durationSeconds: true, order: true,
                        }
                    }
                }
            });

            if (activePlaylist && activePlaylist.youtubePlaylistId) {
                let items = activePlaylist.playlistItems || [];
                
                // If we have an expanded style rotation, assign danceStyle to items based on it
                if (expandedStyleRotation.length > 0) {
                    items = items.map((item, index) => ({
                        ...item,
                        danceStyle: expandedStyleRotation[index % expandedStyleRotation.length]
                    }));
                }

                playerState.youtubePlaylistId = activePlaylist.youtubePlaylistId;
                playerState.lastUpdatedAt = activePlaylist.updatedAt.toISOString();
                playerState.playlistItems = items;

                const currentIndex = activePlaylist.currentSongIndex ?? -1;
                // Ensure currentSong also gets the danceStyle from rotation if applicable
                let currentSong = (currentIndex >= 0 && currentIndex < items.length) ? items[currentIndex] : null;
                if (currentSong && expandedStyleRotation.length > 0 && currentIndex >= 0) {
                    currentSong.danceStyle = expandedStyleRotation[currentIndex % expandedStyleRotation.length];
                }

                const upcomingSongs = (currentIndex >= -1 && items.length > 0) 
                    ? items.slice(currentIndex + 1, currentIndex + 1 + 15)
                    : [];
                
                let estimatedProgressSeconds = 0;
                if (activePlaylist.isPlaying && currentSong && activePlaylist.currentSongStartedAt && currentSong.durationSeconds && currentSong.durationSeconds > 0) {
                    const elapsedMs = Date.now() - activePlaylist.currentSongStartedAt.getTime();
                    estimatedProgressSeconds = Math.max(0, Math.min(currentSong.durationSeconds, Math.floor(elapsedMs / 1000)));
                }
                
                playerState.currentSong = currentSong;
                playerState.upcomingSongs = upcomingSongs;
                playerState.isPlaying = activePlaylist.isPlaying ?? false;
                playerState.currentSongProgressSeconds = estimatedProgressSeconds;
                 logger.debug('Public player state constructed for GENERATED mode', { 
                     currentSong: !!currentSong,
                     danceStyleForCurrent: currentSong?.danceStyle,
                     upcoming: upcomingSongs.length, 
                     isPlaying: playerState.isPlaying 
                 });
            } else {
                 logger.warn('Public player state: GENERATED mode active, but no active playlist with a YouTube ID found in DB.');
            }

        } else if (mode === PlaylistMode.EXTERNAL_MIRROR) {
            logger.debug('Public player state: Handling EXTERNAL_MIRROR mode.');
            playerState.externalMirrorPlaylistId = externalMirrorId; // Use the ID from settings
            playerState.youtubePlaylistId = null; // Ensure this is null for mirror mode
            playerState.isPlaying = false; // App is not controlling playback
            playerState.currentSong = null; // App doesn't track current song in mirror mode
            playerState.upcomingSongs = []; // No upcoming songs from app perspective
            playerState.currentSongProgressSeconds = 0; // App doesn't track progress

            // --- Fetch items from EXTERNAL playlist --- 
            if (externalMirrorId) {
                try {
                    logger.info(`EXTERNAL_MIRROR mode: Fetching items for playlist ID: ${externalMirrorId}`);
                    const rawExternalItems = await getPlaylistItems(externalMirrorId);

                    if (rawExternalItems && Array.isArray(rawExternalItems) && rawExternalItems.length > 0) {
                        const videoIds = rawExternalItems.map(item => item.snippet?.resourceId?.videoId).filter(Boolean);
                        let durationMap = {};

                        if (videoIds.length > 0) {
                            try {
                                logger.debug(`Fetching video details for ${videoIds.length} items from external playlist ${externalMirrorId}`);
                                const youtube = await getAuthorizedYoutubeClient();
                                const BATCH_SIZE = 50;
                                for (let i = 0; i < videoIds.length; i += BATCH_SIZE) {
                                    const batchIds = videoIds.slice(i, i + BATCH_SIZE);
                                    const videoResponse = await youtube.videos.list({
                                        part: ['contentDetails'],
                                        id: batchIds,
                                        maxResults: BATCH_SIZE
                                    });
                                    videoResponse.data.items?.forEach(video => {
                                        if (video.id && video.contentDetails?.duration) {
                                            durationMap[video.id] = parseISO8601Duration(video.contentDetails.duration);
                                        }
                                    });
                                }
                                logger.debug(`Successfully fetched durations for ${Object.keys(durationMap).length} videos.`);
                            } catch (videoError) {
                                logger.error(`Failed to fetch video durations for external playlist ${externalMirrorId}`, { error: videoError.message });
                                durationMap = {}; // Reset map on error
                            }
                        }

                        // Map to the simplified structure expected by frontend
                        playerState.playlistItems = rawExternalItems.map(item => {
                            const videoId = item.snippet?.resourceId?.videoId;
                            return {
                                id: item.id, 
                                youtubeVideoId: videoId,
                                title: item.snippet?.title || 'Unknown Title',
                                thumbnailUrl: item.snippet?.thumbnails?.high?.url || item.snippet?.thumbnails?.default?.url,
                                channelTitle: item.snippet?.channelTitle,
                                durationSeconds: videoId ? (durationMap[videoId] ?? 0) : 0,
                                order: item.snippet?.position ?? 0
                            };
                        }).filter(item => item.youtubeVideoId); // Ensure item has video ID
                        
                         logger.info(`Successfully fetched and mapped ${playerState.playlistItems.length} items from external playlist ${externalMirrorId}`);
                    } else {
                        logger.warn(`No items returned or invalid format from getPlaylistItems for ${externalMirrorId}`);
                    }
                } catch (ytError) {
                    logger.error(`Failed to fetch or map items from external YouTube playlist ${externalMirrorId}`, { error: ytError.message });
                    // Keep playlistItems empty on error
                }
            } else {
                logger.warn('EXTERNAL_MIRROR mode active, but no externalMirrorPlaylistId is set in settings.');
            }
             logger.debug('Public player state constructed for EXTERNAL_MIRROR mode', { 
                 externalMirrorId: playerState.externalMirrorPlaylistId,
                 itemCount: playerState.playlistItems.length
             });
        }

        // 4. Send JSON response
        // Update lastUpdatedAt timestamp just before sending
        playerState.lastUpdatedAt = new Date().toISOString(); 
        res.status(200).json(playerState);

    } catch (error) {
        logger.error('Error fetching public player state:', { error: error.message, stack: error.stack });
        res.status(500).json({ error: 'Failed to fetch player state', message: error.message });
    }
});

// Add a simple handler for the base path /api/public
router.get('/', (req, res) => {
    logger.info('Public API base route accessed');
    res.status(200).json({ message: 'Public API is available' });
});

module.exports = router;