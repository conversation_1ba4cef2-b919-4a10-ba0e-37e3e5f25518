const express = require('express');
const { PrismaClient, Prisma } = require('@prisma/client');
const logger = require('../utils/logger');
const { requireAdmin, authenticateJWT } = require('../middleware/auth');
const { body, param, query, validationResult } = require('express-validator'); // Import validation functions
const multer = require('multer'); // Import multer
const sharp = require('sharp'); // Import sharp
const path = require('path');
const fs = require('fs');
const { getIo, emitUserCreditsUpdate } = require('../websocket');
const crypto = require('crypto');

const router = express.Router();
const prisma = new PrismaClient();

// --- Multer Configuration for Avatar Uploads ---
const AVATAR_UPLOAD_DIR = path.join(__dirname, '..', '..', 'uploads', 'avatars');
const AVATAR_MAX_SIZE_MB = 5;
const AVATAR_SIZE_PX = 256; // Size to resize avatars to (square)

// Ensure directory exists (already done in index.js, but good defense)
if (!fs.existsSync(AVATAR_UPLOAD_DIR)) {
  try {
    fs.mkdirSync(AVATAR_UPLOAD_DIR, { recursive: true });
    logger.info(`Ensured avatars directory exists: ${AVATAR_UPLOAD_DIR}`);
  } catch (err) {
    logger.error(`Failed to create avatars directory: ${AVATAR_UPLOAD_DIR}`, err);
  }
}

const storage = multer.memoryStorage(); // Store file in memory for processing

const fileFilter = (req, file, cb) => {
  // Accept only image files
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    logger.warn('Avatar upload rejected: Invalid file type', { userId: req.user?.id, mimetype: file.mimetype });
    // Reject the file, don't pass an error here. Check req.file in the handler.
    cb(null, false); 
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: AVATAR_MAX_SIZE_MB * 1024 * 1024 // Limit file size
  },
  fileFilter: fileFilter
});

// GET /api/users/me - Get current logged-in user's basic info
router.get('/me', authenticateJWT, async (req, res) => {
  if (!req.user) {
    return res.status(401).json({ error: 'Not authenticated' });
  }
  // Return basic user info stored in JWT payload or refetch from DB if needed
  // Exclude sensitive info like passwordHash
  logger.debug('Fetching basic info for user', { userId: req.user.id });
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { id: true, username: true, role: true, credits: true } // Select non-sensitive fields
    });
    if (!user) {
      logger.warn('Authenticated user not found in DB', { userId: req.user.id });
      return res.status(404).json({ error: 'User not found' });
    }
    res.json(user);
  } catch (error) {
    logger.error('Error fetching user basic info:', { userId: req.user.id, error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/users/me/profile - Get current user's profile (displayName, avatarUrl)
router.get('/me/profile', authenticateJWT, async (req, res) => {
  const userId = req.user?.id;
  if (!userId) {
    return res.status(401).json({ error: 'Not authenticated' });
  }
  logger.debug('Fetching profile for user', { userId });
  try {
    const userProfile = await prisma.userProfile.findUnique({
      where: { userId: userId },
      select: { displayName: true, avatarUrl: true } // Only select profile fields
    });
    if (!userProfile) {
      // If no profile exists yet, return default/empty values
      logger.debug('No profile found for user, returning defaults.', { userId });
      return res.json({ displayName: null, avatarUrl: null });
    }
    
    // Format the response to include the full path for avatarUrl
    const formattedProfile = {
      displayName: userProfile.displayName,
      avatarUrl: userProfile.avatarUrl ? `/uploads/avatars/${userProfile.avatarUrl}` : null
    };
    
    res.json(formattedProfile);
  } catch (error) {
    logger.error('Error fetching user profile:', { userId: userId, error: error.message });
    res.status(500).json({ error: 'Internal server error fetching profile' });
  }
});

// Shared validation error handler (can be moved to a shared middleware file later)
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.warn('User API Validation failed', { 
      route: req.originalUrl,
      errors: errors.array(), 
      ip: req.ip,
      userId: req.user?.id 
    });
    return res.status(400).json({ error: errors.array()[0].msg });
  }
  next();
};

// Validation rules for profile update
const validateProfileUpdate = [
  // Use optional() so validation only runs if the field is provided
  body('displayName')
    .optional()
    .trim()
    .notEmpty().withMessage('Display name cannot be empty if provided')
    .isLength({ min: 1, max: 50 }).withMessage('Display name must be between 1 and 50 characters'),
  body('avatarUrl')
    .optional({ checkFalsy: true }) // checkFalsy: true allows empty string to clear avatar
    .trim()
    .isURL({ protocols: ['http', 'https'], require_protocol: true }).withMessage('Avatar URL must be a valid HTTP/HTTPS URL'),
  // Custom check to ensure at least one field is provided
  body().custom((value, { req }) => {
    if (req.body.displayName === undefined && req.body.avatarUrl === undefined) {
      throw new Error('At least one field (displayName or avatarUrl) must be provided for update');
    }
    return true;
  }),
  handleValidationErrors
];

// Validation rules for admin user list query params
const validateAdminUserListQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 }).withMessage('Page must be a positive integer')
    .toInt(),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
    .toInt(),
  handleValidationErrors
];

// PUT /api/users/me/profile - Update or create current user's profile
router.put('/me/profile', authenticateJWT, validateProfileUpdate, async (req, res) => {
  const userId = req.user?.id;
  const { displayName, avatarUrl } = req.body;

  if (!userId) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  const dataToUpdate = {};
  if (displayName !== undefined) dataToUpdate.displayName = displayName;
  if (avatarUrl !== undefined) dataToUpdate.avatarUrl = avatarUrl;

  logger.debug('Attempting to update profile for user', { userId, data: dataToUpdate });

  try {
    const updatedProfile = await prisma.userProfile.upsert({
      where: { userId: userId },
      update: dataToUpdate,
      create: {
        userId: userId,
        displayName: displayName, // Use provided value or default to null if not provided
        avatarUrl: avatarUrl    // Use provided value or default to null if not provided
      },
      select: { displayName: true, avatarUrl: true } // Return updated fields
    });

    logger.info('User profile updated successfully', { userId, updatedFields: Object.keys(dataToUpdate) });
    res.json(updatedProfile);

  } catch (error) {
    logger.error('Error updating user profile:', { userId: userId, error: error.message });
    res.status(500).json({ error: 'Internal server error updating profile' });
  }
});

// GET /api/users - List all users (admin only)
router.get('/', requireAdmin, validateAdminUserListQuery, async (req, res) => {
  const { page = 1, limit = 20 } = req.query;
  const adminUserId = req.user?.id;
  logger.debug('Admin fetching all users', { adminUserId, page, limit });

  try {
    const skip = (page - 1) * limit;
    const take = limit;

    // Get total count and paginated data
    const [totalCount, users] = await prisma.$transaction([
      prisma.user.count(), // Count all users
      prisma.user.findMany({
        skip: skip,
        take: take,
        orderBy: { createdAt: 'asc' },
        select: { id: true, username: true, role: true, credits: true, createdAt: true } // Exclude passwordHash
      })
    ]);

    const totalPages = Math.ceil(totalCount / limit);
    
    logger.info(`Admin fetched ${users.length} users (Page ${page}/${totalPages}, Total: ${totalCount})`, { adminUserId });

    res.json({
      data: users,
      currentPage: page,
      totalPages: totalPages,
      totalCount: totalCount,
      limit: limit
    });

  } catch (error) {
    logger.error('Error fetching all users:', { adminUserId: req.user?.id, error: error.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/users/search/admin - Search for users by username (Admin Only)
router.get('/search/admin', requireAdmin, async (req, res) => {
  const query = req.query.q;
  const adminUserId = req.user?.id;

  // Original logic restored
  if (!query || typeof query !== 'string' || query.trim().length < 1) {
    return res.status(400).json({ message: 'Search query parameter "q" is required.' });
  }

  const searchQuery = query.trim();
  logger.debug('Admin searching for users', { query: searchQuery, adminUserId });

  try {
    const users = await prisma.user.findMany({
      where: {
        username: {
          contains: searchQuery,
          mode: 'insensitive', // Case-insensitive search
        },
        // Optionally filter out the admin making the request?
        // id: { not: adminUserId }
      },
      select: {
        id: true,
        username: true,
        credits: true,
        role: true, // Include role for context?
      },
      orderBy: {
        username: 'asc',
      },
      take: 20, // Limit results for performance
    });

    logger.info(`Admin user search for "${searchQuery}" returned ${users.length} results`);
    // Return only necessary fields for the admin UI
    res.json(users.map(u => ({ id: u.id, username: u.username, credits: u.credits })));

  } catch (error) {
    logger.error('Error searching users:', { query: searchQuery, adminUserId, error: error.message });
    res.status(500).json({ message: 'Internal server error during user search' });
  }
});

// PATCH /api/users/admin/:userId/credits - Update credits for a specific user (Admin Only)
router.patch('/admin/:userId/credits', requireAdmin, async (req, res) => {
    const targetUserId = req.params.userId;
    const { amount, action } = req.body; // Expecting amount (number) and action ('set', 'add', 'remove')
    const adminUserId = req.user?.id;
    const reason = req.body.reason || 'Admin Update'; // Optional reason

    logger.info('Admin attempting credit update', { adminUserId, targetUserId, action, amount });

    // Validate input
    if (typeof amount !== 'number' || !Number.isInteger(amount)) {
         return res.status(400).json({ message: 'Amount must be an integer.' });
    }
    if (!['set', 'add', 'remove'].includes(action)) {
         return res.status(400).json({ message: 'Invalid action specified. Use "set", "add", or "remove".' });
    }
    if (amount < 0) {
        // Allow negative amount only for 'remove' action internally, but validate input amount >= 0
        if (action === 'add' || action === 'set') {
             return res.status(400).json({ message: 'Amount cannot be negative for set/add actions.'});
        }
        // For remove action, frontend sends positive amount, we negate it later if needed.
    }

    try {
        const result = await prisma.$transaction(async (tx) => {
            const user = await tx.user.findUnique({ where: { id: targetUserId } });

            if (!user) {
                throw new Error('UserNotFound');
            }

            let updateData;
            let changeAmount = 0;

            if (action === 'set') {
                if (amount < 0) throw new Error('SetAmountNegative');
                changeAmount = amount - user.credits;
                updateData = { credits: amount };
            } else if (action === 'add') {
                if (amount < 0) throw new Error('AddAmountNegative');
                changeAmount = amount;
                updateData = { credits: { increment: amount } };
            } else { // action === 'remove'
                if (amount < 0) throw new Error('RemoveAmountNegative');
                 if (amount > user.credits) {
                     logger.warn('Admin tried to remove more credits than user has', { adminUserId, targetUserId, current: user.credits, attempted: amount });
                     changeAmount = -user.credits;
                     updateData = { credits: 0 }; 
                 } else {
                     changeAmount = -amount;
                     updateData = { credits: { decrement: amount } };
                 }
            }

            // Perform the update
            const updatedUser = await tx.user.update({
                where: { id: targetUserId },
                data: updateData,
                select: { id: true, username: true, credits: true }
            });

            // Log the transaction
            await tx.creditTransaction.create({
                data: {
                    userId: targetUserId,
                    amount: changeAmount, // Log the actual change
                    type: 'ADMIN_ADJUSTMENT',
                    notes: `Adjusted by Admin ${req.user?.username} (ID: ${adminUserId}). Action: ${action}, Value: ${amount}. Reason: ${reason}.`,
                }
            });
            
            logger.info('Admin successfully updated user credits', { adminUserId, targetUserId, action, amount, newCredits: updatedUser.credits });
            
            // Emit WebSocket event to notify the user of the credits update
            emitUserCreditsUpdate(targetUserId, updatedUser.credits);
            logger.debug(`Emitted credit update to user ${targetUserId}`, { credits: updatedUser.credits });
            
            return updatedUser;
        });

        res.json({ id: result.id, credits: result.credits }); // Return updated user ID and credits

    } catch (error) {
        if (error.message === 'UserNotFound') {
            logger.warn('Admin credit update failed: User not found', { adminUserId, targetUserId });
            return res.status(404).json({ message: 'User not found.' });
        }
         if (error.message.includes('AmountNegative')) {
            logger.warn('Admin credit update failed: Invalid amount for action', { adminUserId, targetUserId, action, amount });
            return res.status(400).json({ message: 'Invalid amount provided for the specified action.' });
        }
        logger.error('Error updating user credits by admin:', { adminUserId, targetUserId, action, amount, error: error.message });
        res.status(500).json({ message: 'Internal server error during credit update.' });
    }
});

// POST /api/users/admin/bulk-credit-add - Add credits to all DANCER users (Admin Only)
router.post('/admin/bulk-credit-add', requireAdmin, async (req, res) => {
  const { amount } = req.body;
  const adminUserId = req.user?.id;
  const reason = req.body.reason || 'Bulk Admin Update';

  logger.info('Admin attempting bulk credit add', { adminUserId, amount, reason });

  if (typeof amount !== 'number' || !Number.isInteger(amount) || amount <= 0) {
    return res.status(400).json({ message: 'Amount must be a positive integer.' });
  }

  try {
    // Use updateMany to increment credits for all users with the DANCER role
    const updateResult = await prisma.user.updateMany({
      where: {
        role: 'DANCER' // Target only dancers
      },
      data: {
        credits: {
          increment: amount
        }
      }
    });

    const count = updateResult.count;
    logger.info(`Admin successfully added ${amount} credits to ${count} DANCER users.`, { adminUserId, reason });

    // Fetch the list of affected users to emit WebSocket events
    const affectedUsers = await prisma.user.findMany({
      where: { role: 'DANCER' },
      select: { id: true, credits: true }
    });
    
    // Emit WebSocket events for each affected user
    for (const user of affectedUsers) {
      emitUserCreditsUpdate(user.id, user.credits);
    }
    logger.debug(`Emitted credit updates to ${affectedUsers.length} users`);

    res.json({ success: true, message: `Successfully added ${amount} credits to ${count} users.` });

  } catch (error) {
    logger.error('Error during bulk credit update:', { adminUserId, amount, reason, error: error.message, stack: error.stack });
    res.status(500).json({ message: 'Internal server error during bulk credit update.' });
  }
});

// GET /api/users/leaderboard - Get top users based on approved suggestions
router.get('/leaderboard', authenticateJWT, async (req, res) => {
  const userId = req.user?.id; // Get current user ID for context logging
  const limit = parseInt(req.query.limit) || 10; // Default to top 10, allow query param override

  logger.debug('Fetching leaderboard data', { requestedByUserId: userId, limit });

  try {
    logger.debug('Leaderboard: About to execute Prisma query', { requestedByUserId: userId }); // Added log
    // Aggregate approved suggestions count per user
    const usersWithSuggestionCounts = await prisma.user.findMany({
      where: {
        role: 'DANCER', // Only include dancers on the leaderboard
        suggestions: {
          some: { status: 'APPROVED' } // Only include users with at least one approved suggestion
        }
      },
      select: {
        id: true,
        username: true,
        profile: {
          select: { displayName: true, avatarUrl: true }
        },
        _count: {
          select: { 
            suggestions: { 
              where: { status: 'APPROVED' } 
            } 
          }
        }
      },
      orderBy: {
        suggestions: {
          _count: 'desc' // Order by the count of approved suggestions
        }
      },
      take: limit // Limit the results
    });
    logger.debug('Leaderboard: Prisma query executed successfully', { requestedByUserId: userId, resultCount: usersWithSuggestionCounts.length }); // Added log

    // Format the data for the leaderboard response
    logger.debug('Leaderboard: About to format results', { requestedByUserId: userId }); // Added log
    let leaderboard = []; // Initialize leaderboard array
    try {
      leaderboard = usersWithSuggestionCounts.map(user => ({
        userId: user.id,
        username: user.username, 
        displayName: user.profile?.displayName ?? user.username, // Fallback to username
        avatarUrl: user.profile?.avatarUrl ? `/uploads/avatars/${user.profile.avatarUrl}` : null, 
        score: user._count.suggestions // The count of approved suggestions is the score
      }));
      logger.debug('Leaderboard: Results formatted successfully', { requestedByUserId: userId }); // Added log
    } catch(mapError) {
        logger.error('!!! Leaderboard: Error during data mapping !!!', { requestedByUserId: userId, error: mapError, stack: mapError.stack });
        // Re-throw the error to be caught by the outer catch block
        throw mapError; 
    }

    logger.info(`Leaderboard data fetched successfully`, { requestedByUserId: userId, count: leaderboard.length });
    res.json(leaderboard);

  } catch (error) {
    // Log the full error object for more details
    logger.error('Error fetching leaderboard data:', { requestedByUserId: userId, error: error, stack: error.stack }); 
    res.status(500).json({ error: 'Internal server error fetching leaderboard' });
  }
});

// GET /api/users/top-voted - Get users with the most votes on their suggestions
router.get('/top-voted', authenticateJWT, async (req, res) => {
  const userId = req.user?.id; // Get current user ID for context logging
  const limit = parseInt(req.query.limit) || 10; // Default to top 10, allow query param override
  const timeframe = req.query.timeframe || 'all'; // 'week', 'month', 'all'

  logger.debug('Fetching top voted users data', { requestedByUserId: userId, limit, timeframe });

  try {
    // Set date filter based on timeframe
    let dateFilter = {};
    if (timeframe === 'week') {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      dateFilter = { createdAt: { gte: oneWeekAgo } };
    } else if (timeframe === 'month') {
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
      dateFilter = { createdAt: { gte: oneMonthAgo } };
    }

    // Find users with suggestions that have votes, and aggregate the total votes
    const usersWithTotalVotes = await prisma.user.findMany({
      where: {
        role: 'DANCER',
        suggestions: {
          some: { 
            votes: { gt: 0 },
            ...dateFilter
          }
        }
      },
      select: {
        id: true,
        username: true,
        profile: {
          select: { displayName: true, avatarUrl: true }
        },
        suggestions: {
          where: {
            votes: { gt: 0 },
            ...dateFilter
          },
          select: {
            id: true,
            votes: true,
            status: true
          }
        }
      }
    });

    // Calculate total votes and format the results
    const topVotedUsers = usersWithTotalVotes
      .map(user => {
        const totalVotes = user.suggestions.reduce((sum, suggestion) => sum + suggestion.votes, 0);
        const approvedSuggestions = user.suggestions.filter(s => s.status === 'APPROVED').length;
        
        return {
          userId: user.id,
          username: user.username,
          displayName: user.profile?.displayName ?? user.username,
          avatarUrl: user.profile?.avatarUrl ? `/uploads/avatars/${user.profile.avatarUrl}` : null,
          totalVotes,
          approvedSuggestions,
          suggestionCount: user.suggestions.length,
          averageVotesPerSuggestion: user.suggestions.length > 0 
            ? (totalVotes / user.suggestions.length).toFixed(1) 
            : '0'
        };
      })
      // Sort by total votes
      .sort((a, b) => b.totalVotes - a.totalVotes)
      // Take the top N
      .slice(0, limit);

    logger.info(`Top voted users data fetched successfully`, { 
      requestedByUserId: userId, 
      count: topVotedUsers.length,
      timeframe
    });
    
    res.json(topVotedUsers);

  } catch (error) {
    logger.error('Error fetching top voted users data:', { 
      requestedByUserId: userId, 
      error: error.message, 
      stack: error.stack,
      timeframe
    });
    res.status(500).json({ error: 'Internal server error fetching top voted users' });
  }
});

// POST /api/users/me/avatar - Upload a new avatar
router.post('/me/avatar', authenticateJWT, upload.single('avatar'), async (req, res, next) => {
  const userId = req.user?.id;
  const file = req.file;

  // Check if multer rejected the file (e.g., due to fileFilter)
  if (!file) {
    logger.warn('Avatar upload failed: No file present after multer processing (likely invalid type or no file uploaded)', { userId });
    return res.status(400).json({ error: 'Invalid file type or no file uploaded. Only images are allowed.' });
  }

  logger.info('Received avatar upload request', { userId, file: { name: file.originalname, size: file.size, mimetype: file.mimetype } });

  try {
    logger.debug('Entering avatar processing try block', { userId }); // Add this log
    // Generate a unique filename (e.g., userId-timestamp.webp)
    const timestamp = Date.now();
    const filename = `${userId}-${timestamp}.webp`;
    const filepath = path.join(AVATAR_UPLOAD_DIR, filename);

    // Process image with Sharp: resize, convert to webp
    await sharp(file.buffer)
      .resize(AVATAR_SIZE_PX, AVATAR_SIZE_PX, { fit: 'cover' }) // Resize and crop if needed
      .webp({ quality: 80 }) // Convert to WebP for efficiency
      .toFile(filepath);

    logger.debug(`Avatar processed and saved`, { userId, filepath, size: AVATAR_SIZE_PX });

    // --- Update UserProfile in Database ---
    const updatedProfile = await prisma.userProfile.upsert({
      where: { userId: userId },
      update: { avatarUrl: filename }, // Store only the filename
      create: {
        userId: userId,
        displayName: req.user?.username || 'User', // Use username if creating profile
        avatarUrl: filename
      },
      select: { avatarUrl: true, displayName: true } // Select needed fields for response/emit
    });

    logger.info(`User profile updated with new avatar`, { userId, filename });
    
    // Prepare response data with full path
    const responseData = { 
        ...updatedProfile,
        avatarUrl: updatedProfile.avatarUrl ? `/uploads/avatars/${updatedProfile.avatarUrl}` : null
    };

    // TODO: Emit WebSocket event to update other sessions of this user
    // getIo().to(`user:${userId}`).emit('user:profileUpdated', responseData);

    res.status(200).json({ 
      message: 'Avatar uploaded successfully', 
      data: responseData
    });

  } catch (err) {
    // Log the error before passing to the global handler
    logger.error('Error processing or saving avatar upload in route handler', { userId, error: err.message, stack: err.stack });

    // Specific error handling remains useful here for multer/sharp errors *after* filtering
    if (err instanceof multer.MulterError && err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: `File too large. Maximum size is ${AVATAR_MAX_SIZE_MB}MB.` });
    }
    // Remove the check for the filter error message, as it's handled by !req.file now
    // if (err.message === 'Invalid file type. Only images are allowed.') { ... }
    if (err.message.includes('Input buffer contains unsupported image format')) {
         return res.status(400).json({ error: 'Unsupported image format.'});
    }
    // For other errors during processing (e.g., sharp failure, DB error), pass to global handler
    next(err); 
  }
});

// Get a user's profile by ID
router.get('/:userId/profile', async (req, res) => {
  try {
    const { userId } = req.params;
    
    // Validate input
    if (!userId) {
      return res.status(400).json({ success: false, message: 'User ID is required' });
    }
    
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        createdAt: true,
        profile: {
          select: {
            displayName: true,
            avatarUrl: true
          }
        }
      }
    });
    
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }
    
    // Count total suggestions
    const suggestionCount = await prisma.suggestion.count({
      where: { userId: userId }
    });
    
    // Count approved suggestions
    const approvedCount = await prisma.suggestion.count({
      where: { 
        userId: userId,
        status: 'APPROVED'
      }
    });
    
    // Format the response
    const responseData = {
      id: user.id,
      username: user.username,
      displayName: user.profile?.displayName || user.username,
      avatarUrl: user.profile?.avatarUrl ? `/uploads/avatars/${user.profile.avatarUrl}` : null,
      danceExperience: {}, // Provide empty object as default since field doesn't exist in DB
      createdAt: user.createdAt,
      suggestionCount,
      approvedCount
    };
    
    res.json({ 
      success: true, 
      data: responseData
    });
  } catch (error) {
    logger.error('Error fetching user profile', { userId: req.params.userId, error: error.message });
    res.status(500).json({ success: false, message: 'Failed to fetch user profile' });
  }
});

// Get all suggestions by a specific user
router.get('/:userId/suggestions', authenticateJWT, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUserId = req.user.id;
    
    // Validate input
    if (!userId) {
      return res.status(400).json({ success: false, message: 'User ID is required' });
    }
    
    const suggestions = await prisma.suggestion.findMany({
      where: { userId: userId },
      include: {
        user: {
          select: {
            id: true,
            username: true
          }
        },
        voters: {
          select: {
            user: {
              select: {
                id: true,
                username: true,
                profile: { select: { displayName: true, avatarUrl: true } }
              }
            }
          }
        }
      },
      orderBy: [
        { status: 'asc' }, // APPROVED comes before PENDING alphabetically
        { createdAt: 'desc' } // Newest first
      ]
    });
    
    // Transform the data to include vote count and hasVoted
    const transformedSuggestions = suggestions.map(suggestion => {
      const { voters, ...rest } = suggestion;
      const mappedVoters = voters.map(v => v.user);
      return {
        ...rest,
        votes: mappedVoters.length,
        voters: mappedVoters,
        hasVoted: mappedVoters.some(u => u.id === currentUserId)
      };
    });
    
    res.json({ success: true, data: transformedSuggestions });
  } catch (error) {
    logger.error('Error fetching user suggestions', { userId: req.params.userId, error: error.message, stack: error.stack }); // Log stack trace
    res.status(500).json({ success: false, message: 'Failed to fetch user suggestions' });
  }
});

// --- Favorite Dancer Endpoints ---

// POST /api/users/:userId/favorite - Mark a user as favorite
router.post('/:userId/favorite', 
  authenticateJWT,
  [ 
    param('userId').matches(/^c[a-zA-Z0-9]{24}$/).withMessage('Invalid target user ID format.')
  ],
  handleValidationErrors, 
  async (req, res) => {
    const favoritingUserId = req.user?.id;
    const favoritedUserId = req.params.userId;

    if (!favoritingUserId) {
      return res.status(401).json({ error: 'Authentication required.' });
    }

    if (favoritingUserId === favoritedUserId) {
      logger.warn('User attempted to favorite themselves', { userId: favoritingUserId });
      return res.status(400).json({ error: 'You cannot favorite yourself.' });
    }

    try {
      // Check if the target user exists
      const targetUserExists = await prisma.user.findUnique({
        where: { id: favoritedUserId },
        select: { id: true } 
      });

      if (!targetUserExists) {
        logger.warn('Favorite attempt failed: Target user not found', { favoritingUserId, favoritedUserId });
        return res.status(404).json({ error: 'Target user not found.' });
      }

      // Upsert to handle potential re-favorite without error, or create new
      const favorite = await prisma.favoriteDancer.upsert({
        where: {
          favoritingUserId_favoritedUserId: {
            favoritingUserId,
            favoritedUserId,
          },
        },
        update: {},
        create: {
          favoritingUserId,
          favoritedUserId,
        },
      });

      logger.info('User successfully favorited another user', { favoritingUserId, favoritedUserId, favoriteId: favorite.favoritingUserId + '_' + favorite.favoritedUserId });
      res.status(201).json({ success: true, message: 'User added to favorites.' });

    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        // This should be caught by upsert now, but as a fallback
        logger.warn('Favorite attempt failed: Already favorited (P2002)', { favoritingUserId, favoritedUserId });
        return res.status(409).json({ error: 'User is already in your favorites.' });
      }
      logger.error('Error favoriting user:', { favoritingUserId, favoritedUserId, error: error.message, stack: error.stack });
      res.status(500).json({ error: 'Internal server error while favoriting user.' });
    }
  }
);

// DELETE /api/users/:userId/favorite - Unmark a user as favorite
router.delete('/:userId/favorite', 
  authenticateJWT, 
  [ 
    param('userId').matches(/^c[a-zA-Z0-9]{24}$/).withMessage('Invalid target user ID format.')
  ],
  handleValidationErrors,
  async (req, res) => {
    const unfavoritingUserId = req.user?.id;
    const unfavoritedUserId = req.params.userId;

    if (!unfavoritingUserId) {
      return res.status(401).json({ error: 'Authentication required.' });
    }

    try {
      await prisma.favoriteDancer.delete({
        where: {
          favoritingUserId_favoritedUserId: {
            favoritingUserId: unfavoritingUserId,
            favoritedUserId: unfavoritedUserId,
          },
        },
      });

      logger.info('User successfully unfavorited another user', { unfavoritingUserId, unfavoritedUserId });
      res.status(200).json({ success: true, message: 'User removed from favorites.' });

    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        // Record to delete was not found
        logger.warn('Unfavorite attempt failed: User not in favorites (P2025)', { unfavoritingUserId, unfavoritedUserId });
        return res.status(404).json({ error: 'User is not in your favorites.' });
      }
      logger.error('Error unfavoriting user:', { unfavoritingUserId, unfavoritedUserId, error: error.message, stack: error.stack });
      res.status(500).json({ error: 'Internal server error while unfavoriting user.' });
    }
  }
);

// GET /api/users/me/favorites - Get current user's list of favorite dancers
router.get('/me/favorites', authenticateJWT, async (req, res) => {
  const userId = req.user?.id;
  if (!userId) {
    return res.status(401).json({ 
      success: false, 
      error: 'Authentication required.' 
    });
  }

  try {
    const favorites = await prisma.favoriteDancer.findMany({
      where: { favoritingUserId: userId },
      select: {
        favoritedUser: {
          select: {
            id: true,
            username: true,
            profile: { // Access UserProfile through the User model
              select: { avatarUrl: true, displayName: true }
            }
          }
        }
      }
    });

    const formattedFavorites = favorites.map(fav => ({
      id: fav.favoritedUser.id,
      username: fav.favoritedUser.username,
      displayName: fav.favoritedUser.profile?.displayName || fav.favoritedUser.username,
      avatarUrl: fav.favoritedUser.profile?.avatarUrl ? `/uploads/avatars/${fav.favoritedUser.profile.avatarUrl}` : null
    }));

    logger.info(`Fetched ${formattedFavorites.length} favorite dancers for user`, { userId });
    res.json({ 
      success: true, 
      data: formattedFavorites 
    });

  } catch (error) {
    logger.error('Error fetching user favorites:', { userId, error: error.message, stack: error.stack });
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error fetching favorites.' 
    });
  }
});

// GET /api/users/:userId/achievements - Get achievements for a specific user
// Commented out or removed as part of achievements removal
/*
router.get('/:userId/achievements', authenticateJWT, async (req, res) => {
  const { userId } = req.params;
  const requestingUserId = req.user?.id; // For logging, or if only specific users can view others' achievements
  
  logger.debug('Fetching achievements for user', { targetUserId: userId, requestingUserId });
  
  try {
    // Check if user exists
    const userExists = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true }
    });
    
    if (!userExists) {
      logger.warn('Attempt to fetch achievements for non-existent user', { targetUserId: userId, requestingUserId });
      return res.status(404).json({ 
        success: false,
        message: 'User not found.' 
      });
    }
    
    const userAchievements = await prisma.userAchievement.findMany({
      where: { userId },
      include: {
        achievement: true,
      },
      orderBy: {
        earnedAt: 'desc',
      },
    });
    
    // Format the achievements
    const formattedAchievements = userAchievements.map(ua => ({
      id: ua.achievement.id,
      name: ua.achievement.name,
      description: ua.achievement.description,
      iconUrl: ua.achievement.iconUrl,
      earnedAt: ua.earnedAt,
    }));
    
    logger.info(`Returning ${formattedAchievements.length} achievements for user`, { targetUserId: userId });
    res.json({ success: true, data: formattedAchievements });
    
  } catch (error) {
    logger.error('Error fetching user achievements:', { targetUserId: userId, error: error.message, stack: error.stack });
    res.status(500).json({ 
      success: false,
      message: 'Internal server error fetching achievements.' 
    });
  }
});
*/

// --- Achievement Routes ---

// GET /api/achievements/my - Get achievements for the logged in user
// Commented out or removed as part of achievements removal
/*
router.get('/achievements/my', authenticateJWT, async (req, res) => {
  const userId = req.user?.id;

  if (!userId) {
    logger.warn('Attempt to fetch achievements without authentication');
    return res.status(401).json({ 
      success: false,
      message: 'Authentication required.' 
    });
  }

  try {
    logger.debug('Fetching achievements for user', { userId });

    const userAchievements = await prisma.userAchievement.findMany({
      where: { userId },
      include: {
        achievement: true,
      },
      orderBy: {
        earnedAt: 'desc',
      },
    });

    const achievements = userAchievements.map(ua => ({
      id: ua.achievement.id,
      name: ua.achievement.name,
      description: ua.achievement.description,
      iconUrl: ua.achievement.iconUrl,
      earnedAt: ua.earnedAt,
    }));

    logger.info(`Returning ${achievements.length} achievements for user`, { userId });

    return res.json({
      success: true,
      data: achievements
    });
  } catch (error) {
    logger.error('Error fetching user achievements:', { userId: userId, error: error.message });
    return res.status(500).json({ 
      success: false,
      message: 'Internal server error fetching achievements'
    });
  }
});
*/

module.exports = router; 