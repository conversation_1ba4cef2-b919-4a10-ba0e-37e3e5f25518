const express = require('express');
const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const { requireAdmin, authenticateJWT, tryAuthenticateJWT, authenticateOptionalJWT } = require('../middleware/auth');
const { getIo, emitUserCreditsUpdate } = require('../websocket');
const { addVideoToPlaylist } = require('../utils/youtubeApi');
const { getAuthorizedYoutubeClient } = require('../utils/youtubeClient');
const { parseISO8601Duration } = require('../utils/durationUtils');
const suggestionSyncService = require('../services/suggestionSyncService');
const commentRoutes = require('./comments'); // Import comment routes
const { body, param, query, validationResult } = require('express-validator'); // Import validation functions
const { getPlaylistVideoDetails } = require('../services/youtubeService'); // Added for new endpoint
const jwt = require('jsonwebtoken');

const router = express.Router();
const prisma = new PrismaClient();

// --- Constants for Chart Logic ---
const DEFAULT_PAGE_SIZE = 25; // Default items per page for full chart views
const MAX_PAGE_SIZE = 50; // Max items per page

// --- Simple In-Memory Cache for Approved Suggestions --- 
let approvedSuggestionsCache = {
  data: null,
  timestamp: 0,
};
const CACHE_DURATION_MS = 5 * 60 * 1000; // 5 minutes

// --- Achievement Helper Function --- (commenting out)
/* 
const awardAchievement = async (tx, userId, achievementName) => {
  try {
    const achievement = await tx.achievement.findUnique({ where: { name: achievementName } });
    if (!achievement) {
      logger.warn(`Achievement definition not found: ${achievementName}. Skipping award.`, { userId });
      return false; // Achievement definition missing
    }

    // Check if user already has this achievement
    const existingAward = await tx.userAchievement.findUnique({
      where: { userId_achievementId: { userId, achievementId: achievement.id } }
    });

    if (!existingAward) {
      await tx.userAchievement.create({
        data: {
          userId: userId,
          achievementId: achievement.id
        }
      });
      logger.info(`Achievement awarded: ${achievementName} to user ${userId}`);
      // Optionally emit a WebSocket event here
      getIo().to(`user:${userId}`).emit('achievement:unlocked', achievement);
      return true; // Achievement awarded
    } else {
      logger.debug(`User ${userId} already has achievement: ${achievementName}. Skipping award.`);
      return false; // Already awarded
    }
  } catch (achieveError) {
    // Log error but don't fail the main transaction because of achievement awarding
    logger.error(`Error awarding achievement ${achievementName} to user ${userId}`, { error: achieveError.message });
    return false; // Error occurred
  }
};
*/

// Shared validation error handler
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.warn('API Validation failed', { 
      route: req.path,
      errors: errors.array(), 
      ip: req.ip 
    });
    // Return only the first error message for simplicity
    return res.status(400).json({ error: errors.array()[0].msg });
  }
  next();
};

// Validation rules for suggestion submission
const validateSuggestion = [
  body('danceStyle')
    .trim()
    .notEmpty().withMessage('Dance style is required')
    .isLength({ min: 1, max: 50 }).withMessage('Dance style seems invalid'), // Basic length check
    // TODO: Ideally validate against allowed dance styles from config/db later
  body('youtubeVideoId')
    .trim()
    .notEmpty().withMessage('YouTube Video ID or URL is required')
    // Basic check: Allow URLs or typical video IDs (alphanumeric + _-)
    .matches(/^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)?([a-zA-Z0-9_-]{11})$/)
    .withMessage('Invalid YouTube Video ID or URL format')
    .customSanitizer(value => {
      // Extract the 11-character video ID from common URL formats
      const match = value.match(/[a-zA-Z0-9_-]{11}$/);
      return match ? match[0] : value; // Return only the ID if found, otherwise original value for further validation
    }),
  handleValidationErrors
];

// Validation rules for admin suggestions query params
const validateAdminSuggestionsQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 }).withMessage('Page must be a positive integer')
    .toInt(),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
    .toInt(),
  handleValidationErrors
];

// Validation rules for public suggestions query params
const validatePublicSuggestionsQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 }).withMessage('Page must be a positive integer')
    .toInt(), // Convert to integer
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100') // Set a max limit
    .toInt(), // Convert to integer
  query('filterBy')
    .optional()
    .isIn(['all', 'mySuggestions', 'othersSuggestions']).withMessage('Invalid filterBy value'),
  query('sortBy')
    .optional()
    .isIn(['newest', 'oldest', 'votes']).withMessage('Invalid sortBy value'),
  query('danceStyle')
    .optional(),
  handleValidationErrors
];

// Validation rules for voting
const validateVote = [
  param('id') // Changed from suggestionId to id to match route param
    // .isUUID().withMessage('Invalid Suggestion ID format.') // Incorrect: Prisma uses CUIDs
    .matches(/^c[a-zA-Z0-9]{24}$/).withMessage('Invalid Suggestion ID format.'), // Correct: Validate CUID format
  handleValidationErrors
];

// Validation rules for user suggestion history query params
const validateMyHistoryQuery = [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('danceStyle').optional().isString().trim().escape(),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.warn('Invalid pagination parameters for suggestion history', { errors: errors.array() });
      return res.status(400).json({ success: false, errors: errors.array() });
    }
    next();
  }
];

// Validation rules for the new chart endpoint
const validateChartViewQuery = [
  param('chartType')
    .isIn(['top-suggestions', 'style-top', 'monthly-vibes', 'trending', 'weekly', 'monthly', 'top'])
    .withMessage('Invalid chart type specified.'),
  param('style')
    .optional()
    .isString()
    .notEmpty()
    .withMessage('Style must be a non-empty string if provided.'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer.')
    .toInt(),
  query('limit')
    .optional()
    .isInt({ min: 1, max: MAX_PAGE_SIZE })
    .withMessage(`Limit must be between 1 and ${MAX_PAGE_SIZE}.`)
    .toInt(),
  handleValidationErrors
];

// Validation rules for playlist submission
const validatePlaylistSuggestion = [
  body('youtubePlaylistUrl')
    .trim()
    .notEmpty().withMessage('YouTube Playlist URL is required.')
    .isURL().withMessage('Invalid YouTube Playlist URL format.'),
  body('danceStyle')
    .trim()
    .notEmpty().withMessage('Dance style is required.')
    .isLength({ min: 1, max: 50 }).withMessage('Dance style seems invalid.'),
  handleValidationErrors
];

// Validation middleware for approved suggestions endpoint
const validateApprovedQuery = [
  query('danceStyle').optional().isString().trim().escape(),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('chartType').optional().isIn(['standard', 'discovery']).withMessage('Chart type must be either standard or discovery'),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.warn('Invalid parameters for approved suggestions', { errors: errors.array() });
      return res.status(400).json({ success: false, errors: errors.array() });
    }
    next();
  }
];



// --- Admin Suggestion Route --- 
// GET /api/suggestions - List all suggestions (admin only)
router.get('/', requireAdmin, validateAdminSuggestionsQuery, async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    status, 
    danceStyle, 
    searchTerm, 
    sortBy = 'oldest' // Default sort order
  } = req.query;
  const adminUserId = req.user?.id;

  // Add detailed diagnostic logging about the user's authentication state
  logger.info('Admin suggestion route accessed', { 
    userId: adminUserId,
    role: req.user?.role, 
    username: req.user?.username,
    isAdminUser: req.user?.role === 'ADMIN',
    authHeader: req.headers.authorization ? 'Present' : 'Missing',
    hasCookies: !!req.cookies,
    cookieNames: req.cookies ? Object.keys(req.cookies) : []
  });

  try {
    logger.debug('Fetching all suggestions for admin', { adminUserId, page, limit, status, danceStyle, searchTerm, sortBy });

    const skip = (page - 1) * limit;
    const take = parseInt(limit, 10);

    const where = {};
    if (status && status !== 'All') {
      where.status = status;
    }
    if (danceStyle && danceStyle !== 'All') {
      where.danceStyle = danceStyle;
    }
    if (searchTerm) {
      where.OR = [
        { title: { contains: searchTerm, mode: 'insensitive' } },
        { user: { username: { contains: searchTerm, mode: 'insensitive' } } },
        { channelTitle: { contains: searchTerm, mode: 'insensitive' } }
      ];
    }

    let orderBy = { createdAt: 'asc' }; // Default to oldest
    if (sortBy === 'newest') {
      orderBy = { createdAt: 'desc' };
    }
    // Add other sortBy options here if needed in the future

    // Get total count and paginated data
    const [totalCount, suggestions] = await prisma.$transaction([
      prisma.suggestion.count({ where }), // Apply where clause to count
      prisma.suggestion.findMany({
        where, // Apply where clause to findMany
        skip: skip,
        take: take,
        orderBy: orderBy,
        include: {
          user: { select: { id: true, username: true } },
          voters: { select: { userId: true } }, // Corrected: 'voters' is the relation
        },
      }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logger.info(`Admin fetched ${suggestions.length} suggestions (Page ${page}/${totalPages}, Total: ${totalCount})`, { adminUserId });
    
    res.json({
      data: suggestions,
      currentPage: page,
      totalPages: totalPages,
      totalCount: totalCount,
      limit: limit
    });

  } catch (err) {
    logger.error('Error fetching admin suggestions:', { error: err.message, stack: err.stack, adminUserId });
    res.status(500).json({ error: 'Internal server error fetching suggestions' });
  }
});

// GET /api/suggestions/public - List votable suggestions for DANCERS (includes hasVoted)
router.get('/public', authenticateJWT, validatePublicSuggestionsQuery, async (req, res) => {
  const userId = req.user?.id;
  // Extract validated and sanitized query params
  const { filterBy, sortBy, danceStyle, page = 1, limit = 10 } = req.query; 

  if (!userId) {
    logger.warn('Public suggestions fetch attempt without authenticated user.');
    return res.status(401).json({ error: 'Authentication required.' });
  }

  try {
    logger.debug('Fetching public suggestions for user', { userId, filterBy, sortBy, danceStyle });

    // --- Build Prisma Query Conditions --- 
    const whereConditions = {
      status: { in: ['PENDING', 'APPROVED'] }
    };

    if (filterBy === 'mySuggestions') {
      whereConditions.userId = userId;
      logger.debug('Applying filter: mySuggestions', { userId });
    } else if (filterBy === 'othersSuggestions') {
      // Add condition to exclude the current user's suggestions
      whereConditions.NOT = { userId: userId };
      logger.debug('Applying filter: othersSuggestions (excluding own)', { userId });
    } 
    // If filterBy is undefined or 'all' (or anything else), no user filter is applied
    
    // Apply dance style filter if provided
    if (danceStyle) {
      whereConditions.danceStyle = danceStyle;
      logger.debug('Applying dance style filter', { danceStyle });
    }

    const orderByConditions = {};
    switch (sortBy) {
      case 'newest':
        orderByConditions.createdAt = 'desc';
        break;
      case 'oldest':
        orderByConditions.createdAt = 'asc';
        break;
      case 'votes': // Default
      default:
        orderByConditions.votes = 'desc';
        break;
    }
    logger.debug('Applying sort', { orderByConditions });
    // --- End Query Conditions --- 

    // Calculate pagination
    const skip = (page - 1) * limit;
    const take = limit;

    // Use transaction to get both count and data efficiently
    const [totalCount, suggestions] = await prisma.$transaction([
      prisma.suggestion.count({ where: whereConditions }),
      prisma.suggestion.findMany({
        where: whereConditions,
        orderBy: orderByConditions,
        skip: skip,
        take: take,
        select: {
          id: true, title: true, channelTitle: true, thumbnailUrl: true, danceStyle: true,
          status: true, votes: true, isLocked: true, createdAt: true, updatedAt: true,
          userId: true, youtubeVideoId: true,
          durationSeconds: true, // Include duration
          user: { select: { username: true, id: true } },
          voters: {
            select: {
              user: {
                select: {
                  id: true,
                  username: true,
                  profile: { select: { displayName: true, avatarUrl: true } }
                }
              }
            }
          }
        }
      })
    ]);
    
    logger.debug(`Found ${suggestions.length} suggestions for page ${page} (total: ${totalCount})`, { userId });

    // Fetch vote IDs for the current user for the *current page* of suggestions
    const suggestionIdsOnPage = suggestions.map(s => s.id);
    const userVotes = await prisma.vote.findMany({
      where: { 
        userId: userId,
        suggestionId: { in: suggestionIdsOnPage } // Only check votes for suggestions on this page
      },
      select: { suggestionId: true }
    });
    const votedSuggestionIds = new Set(userVotes.map(vote => vote.suggestionId));
    logger.debug(`User has voted on ${votedSuggestionIds.size} suggestions on this page`, { userId, page });

    // Combine data: add hasVoted field
    const suggestionsWithVoteStatus = suggestions.map(suggestion => ({
      ...suggestion,
      hasVoted: votedSuggestionIds.has(suggestion.id)
    }));

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / limit);

    logger.info(`Returning ${suggestionsWithVoteStatus.length} public suggestions for user`, { userId, filterBy, sortBy, page, limit, totalCount, totalPages });
    
    // Return paginated response structure
    res.json({
      data: suggestionsWithVoteStatus,
      currentPage: page,
      totalPages: totalPages,
      totalCount: totalCount,
      limit: limit
    });

  } catch (err) {
    logger.error('Error fetching public suggestions:', { error: err.message, stack: err.stack, userId });
    res.status(500).json({ error: 'Internal server error fetching public suggestions' });
  }
});

// GET /api/suggestions/my-history - Fetch suggestion history for the logged-in user
router.get('/my-history', authenticateJWT, validateMyHistoryQuery, async (req, res) => {
  const userId = req.user?.id;
  const { page = 1, limit = 15, danceStyle } = req.query;
  const pageNumber = parseInt(page, 10) || 1;
  const limitNumber = parseInt(limit, 10) || 15;

  if (!userId) {
    logger.warn('Suggestion history fetch attempt without authenticated user.');
    return res.status(401).json({ error: 'Authentication required.' });
  }

  try {
    logger.debug('Fetching suggestion history for user', { userId, page: pageNumber, limit: limitNumber, danceStyle });

    const skip = (pageNumber - 1) * limitNumber;
    const take = limitNumber;
    
    // Build where conditions
    const where = { userId: userId };
    if (danceStyle && danceStyle !== 'all') {
      where.danceStyle = danceStyle;
    }

    // Get total count for pagination
    const totalCount = await prisma.suggestion.count({
      where: where
    });

    const suggestions = await prisma.suggestion.findMany({
      where: where,
      orderBy: {
        createdAt: 'desc',
      },
      skip: skip,
      take: take,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            profile: {
              select: {
                displayName: true,
                avatarUrl: true
              }
            }
          },
        },
        voters: {
          select: {
            user: {
              select: {
                id: true,
                username: true,
                profile: {
                  select: {
                    displayName: true,
                    avatarUrl: true
                  }
                }
              }
            }
          }
        },
      },
    });

    logger.info(`Fetched ${suggestions.length} suggestions for user's history`, { userId, totalItems: totalCount });

    // Format response with pagination metadata
    const formattedSuggestions = suggestions.map(suggestion => ({
      ...suggestion,
      votes: suggestion.voters.length,
      voters: suggestion.voters.map(voter => voter.user)
    }));

    res.json({
      success: true,
      data: formattedSuggestions,
      pagination: {
        page: pageNumber,
        limit: limitNumber,
        totalCount: totalCount,
        totalPages: Math.ceil(totalCount / limitNumber)
      }
    });
  } catch (error) {
    logger.error('Error fetching suggestion history', { error: error.message, userId });
    res.status(500).json({ error: 'Failed to fetch suggestion history.' });
  }
});

// GET /api/suggestions/approved - Fetch all approved suggestions for the charts
router.get('/approved', validateApprovedQuery, async (req, res) => {
  // Try to get user ID from cookies/auth header if present, but don't require it
  let userId = null;
  
  // Try to extract token from request
  try {
    const authHeader = req.headers.authorization;
    let token = null;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
    } else if (req.cookies && req.cookies.authToken) {
      token = req.cookies.authToken;
    }
    
    // If we have a token, try to verify it
    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        if (decoded && decoded.userId) {
          // Set userId but don't fail if we can't find the user
          userId = decoded.userId;
        }
      } catch (tokenError) {
        // Just log and continue as anonymous
        logger.debug('Invalid token in approved suggestions endpoint', { error: tokenError.message });
      }
    }
  } catch (authError) {
    // Just log and continue as anonymous
    logger.debug('Error checking auth in approved suggestions endpoint', { error: authError.message });
  }
  
  logger.debug('Accessing approved suggestions endpoint', { 
    userId: userId || 'anonymous', 
    authenticated: !!userId,
    query: req.query
  });
  
  const { danceStyle, page = 1, limit = 100, chartType } = req.query;
  const pageNumber = parseInt(page, 10) || 1;
  const limitNumber = parseInt(limit, 10) || 100;

  try {
    logger.debug('Fetching approved suggestions', { danceStyle, page: pageNumber, limit: limitNumber, chartType });
    
    // Build where conditions
    const where = { status: 'APPROVED' };
    if (danceStyle && danceStyle !== 'all') {
      where.danceStyle = danceStyle;
    }
    
    // For discovery algorithms, exclude the current user's own suggestions
    if (chartType === 'discovery' && userId) {
      logger.info('Discovery mode: Excluding current user suggestions', { userId });
      where.NOT = {
        userId: userId
      };
    }

    // Get total count for pagination
    const totalCount = await prisma.suggestion.count({
      where: where
    });

    // Calculate pagination
    const skip = (pageNumber - 1) * limitNumber;
    const take = limitNumber;

    const suggestions = await prisma.suggestion.findMany({
      where: where,
      orderBy: [
        {
          createdAt: 'desc',
        },
      ],
      skip: skip,
      take: take,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            profile: {
              select: {
                displayName: true,
                avatarUrl: true,
              },
            },
          },
        },
        voters: {
          select: {
            userId: true,
            user: {
              select: {
                id: true,
                username: true,
                profile: {
                  select: {
                    displayName: true,
                    avatarUrl: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Process results to include hasVoted flag and sort by votes count
    const processedSuggestions = suggestions
      .map(suggestion => {
        const hasVoted = userId ? suggestion.voters.some(vote => vote.userId === userId) : false;
        
        return {
          ...suggestion,
          votes: suggestion.voters.length,
          hasVoted,
          voters: suggestion.voters.map(vote => ({
            id: vote.userId,
            username: vote.user.username,
            profile: vote.user.profile,
          })),
        };
      })
      // Sort by votes (most first) and then by creation date (newest first)
      .sort((a, b) => {
        if (b.votes !== a.votes) {
          return b.votes - a.votes;
        }
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

    logger.info(`Fetched ${processedSuggestions.length} approved suggestions`, { 
      danceStyle, 
      userId: userId || 'anonymous',
      totalItems: totalCount,
      chartType: chartType || 'standard'
    });

    // Return with pagination metadata
    res.json({
      success: true,
      data: processedSuggestions,
      pagination: {
        page: pageNumber,
        limit: limitNumber,
        totalCount: totalCount,
        totalPages: Math.ceil(totalCount / limitNumber)
      }
    });
  } catch (error) {
    logger.error('Error fetching approved suggestions', { error: error.message });
    res.status(500).json({ error: 'Failed to fetch approved suggestions.' });
  }
});

// GET /api/suggestions/voted - Get suggestions the current user has voted for
router.get('/voted', authenticateJWT, async (req, res) => {
  const userId = req.user?.id;

  if (!userId) {
    logger.warn('Voted suggestions fetch attempt without authenticated user.');
    return res.status(401).json({ error: 'Authentication required.' });
  }

  try {
    logger.debug('Fetching voted suggestions for user', { userId });

    // Get all votes by this user
    const userVotes = await prisma.vote.findMany({
      where: { userId },
      select: { suggestionId: true }
    });

    // Get the actual suggestions
    const votedSuggestions = await prisma.suggestion.findMany({
      where: {
        id: { in: userVotes.map(vote => vote.suggestionId) },
        status: 'APPROVED' // Only include approved suggestions
      },
      include: {
        user: {
          select: { id: true, username: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Add hasVoted field (all should be true since these are the user's voted suggestions)
    const suggestionsWithVoteStatus = votedSuggestions.map(suggestion => ({
      ...suggestion,
      hasVoted: true
    }));

    logger.info(`Returning ${suggestionsWithVoteStatus.length} voted suggestions for user`, { userId });
    
    return res.json({
      success: true,
      data: suggestionsWithVoteStatus
    });

  } catch (err) {
    logger.error('Error fetching voted suggestions:', { error: err.message, stack: err.stack, userId });
    res.status(500).json({ 
      success: false,
      message: 'Internal server error fetching voted suggestions' 
    });
  }
});

// POST /api/suggestions - Submit a new suggestion (dancer)
router.post('/', authenticateJWT, validateSuggestion, async (req, res) => {
  let { youtubeVideoId, title, channelTitle, thumbnailUrl, danceStyle } = req.body;
  let durationSeconds = null; // Initialize duration
  const userId = req.user?.id;
  
  const isUrlSubmission = !title; // Determine if it's likely a URL submission based on missing title

  logger.debug('Attempting suggestion submission', { 
    userId, 
    suggestion: req.body, 
    isUrlSubmission, 
    youtubeVideoId 
  });

  if (!userId) {
    logger.error('Suggestion submission failed: User ID missing after auth', { body: req.body });
    return res.status(401).json({ error: 'Unauthorized - User not identified' });
  }

  // Removed credits check - all users can now suggest unlimited songs

  try {
    let newSuggestion = null;

    // If title is missing, try to fetch it from YouTube API
    if (!title || !channelTitle || !thumbnailUrl) {
      logger.debug('Missing metadata, attempting to fetch from YouTube API', { youtubeVideoId });
      try {
        const videoDetails = await getVideoDetails(youtubeVideoId);
        if (videoDetails) {
          title = title || videoDetails.title;
          channelTitle = channelTitle || videoDetails.channelTitle;
          thumbnailUrl = thumbnailUrl || videoDetails.thumbnailUrl;
          durationSeconds = videoDetails.durationSeconds;
          logger.debug('Successfully fetched video details from YouTube API', { 
            youtubeVideoId, 
            title, 
            channelTitle, 
            durationSeconds 
          });
        } else {
          logger.warn('YouTube API returned no details for video', { youtubeVideoId });
        }
      } catch (apiError) {
        logger.warn('Failed to fetch video details from YouTube API', { 
          youtubeVideoId, 
          error: apiError.message 
        });
        // Continue with submission even if API call fails
      }
    }

    // Fallback title if still missing
    if (!title) {
      title = `YouTube Video ${youtubeVideoId}`;
      logger.debug('Using fallback title for suggestion', { youtubeVideoId, title });
    }

    // Create suggestion without credits transaction
    await prisma.$transaction(async (tx) => {
      // Create Suggestion
      const suggestion = await tx.suggestion.create({
        data: {
          userId,
          youtubeVideoId,
          title,
          channelTitle,
          thumbnailUrl,
          danceStyle,
          durationSeconds,
          status: 'PENDING',
          votes: 0
        }
      });
      newSuggestion = suggestion;
      logger.debug('Suggestion created within transaction', { suggestionId: suggestion.id, userId });

      // Removed credit deduction and transaction logging
    });
    
    logger.info('Suggestion submitted successfully', { 
      suggestionId: newSuggestion.id, 
      userId,
      title,
      danceStyle
    });
    
    // Emit WebSocket events
    getIo().to('admin').emit('suggestion:created', newSuggestion);
    getIo().to('suggestions').emit('suggestion:created', newSuggestion);
    getIo().to(`user:${userId}`).emit('suggestion:created', newSuggestion);
    getIo().to(`user:${userId}`).emit('notification', { type: 'suggestion_submitted', suggestion: newSuggestion });
    
    res.status(201).json(newSuggestion);

  } catch (err) {
    logger.error('Error during suggestion submission transaction', { 
      userId, 
      error: err.message, 
      stack: err.stack
    });
    
    if (err.code === 'P2002' && err.meta?.target?.includes('Suggestion_youtubeVideoId')) {
      // This is a unique constraint violation - the song was already suggested
      return res.status(409).json({ error: 'This song has already been suggested. Please try a different song.' });
    }
    
    res.status(500).json({ error: 'Internal server error during suggestion submission' });
  }
});

// --- Suggestion Approval Route ---
// POST /api/suggestions/:id/approve - Approve a suggestion (admin)
router.post('/:id/approve', requireAdmin, async (req, res) => {
  const suggestionId = req.params.id;
  const adminUserId = req.user?.id;
  logger.debug('Admin attempting suggestion approval', { adminUserId, suggestionId });

  try {
    const suggestion = await prisma.suggestion.findUnique({
      where: { id: suggestionId }
    });

    if (!suggestion) {
      logger.warn('Approve failed: Suggestion not found', { adminUserId, suggestionId });
      return res.status(404).json({ error: 'Suggestion not found' });
    }

    if (suggestion.status !== 'PENDING') {
      logger.warn('Approve failed: Suggestion not PENDING', { adminUserId, suggestionId, status: suggestion.status });
      return res.status(400).json({ error: 'Suggestion is not pending approval' });
    }

    // Fetch style-specific playlist ID
    const adminSettings = await prisma.adminSettings.findUnique({
      where: { singletonLock: true },
      select: { styleSourcePlaylists: true }
    });

    let playlistId = null;
    if (adminSettings && adminSettings.styleSourcePlaylists && adminSettings.styleSourcePlaylists[suggestion.danceStyle]) {
        playlistId = adminSettings.styleSourcePlaylists[suggestion.danceStyle];
        logger.debug(`Found target playlist ID for style ${suggestion.danceStyle}: ${playlistId}`, { suggestionId });
    } else {
        logger.warn(`No specific YouTube playlist configured for style: ${suggestion.danceStyle}. Suggestion approved but not added to a playlist.`, { suggestionId });
        // Optionally: Could fall back to a default playlist if desired
    }

    const updatedSuggestion = await prisma.$transaction(async (tx) => {
        const updated = await tx.suggestion.update({
            where: { id: suggestionId },
            data: { status: 'APPROVED', isLocked: false }, // Approve and unlock
            include: { user: { select: { id: true, username: true } } }
        });

        // --- Achievement Logic --- 
        // 1. Award 'First Suggestion Approved' to the suggester
        // await awardAchievement(tx, updated.userId, 'FIRST_SUGGESTION_APPROVED');

        // 2. Check for 'Discoverer Award' (e.g., 5 approved suggestions)
        const approvedCount = await tx.suggestion.count({
          where: {
            userId: updated.userId,
            status: 'APPROVED'
          }
        });
        logger.debug('Checking Discoverer Award', { userId: updated.userId, approvedCount });
        if (approvedCount >= 5) { // Threshold for Discoverer
          // await awardAchievement(tx, updated.userId, 'DISCOVERER_AWARD');
        }
        // --- End Achievement Logic --- 
        
        // Add to playlist if ID found
        if (playlistId) {
          try {
             await addVideoToPlaylist(suggestion.youtubeVideoId, playlistId);
             logger.info(`Added approved suggestion to YouTube playlist`, { suggestionId, youtubeVideoId: suggestion.youtubeVideoId, playlistId });
          } catch (youtubeError) {
             logger.error('Failed to add approved suggestion to YouTube playlist', { suggestionId, playlistId, error: youtubeError.message });
             // Decide if this should fail the transaction or just log the error
             // Currently logging and continuing
             toast.error('Suggestion approved, but failed to add to YouTube playlist automatically.'); // Inform admin via toast?
          }
        }

        return updated;
    });

    if (updatedSuggestion) {
        invalidateApprovedSuggestionsCache(); // Invalidate cache after successful approval
        logger.info('Suggestion approved successfully', { adminUserId, suggestionId, suggesterUserId: updatedSuggestion.userId });
        getIo().emit('suggestion:updated', updatedSuggestion);
        res.json(updatedSuggestion);
    } else {
        // Handle case where transaction might not return updatedSuggestion (should be rare if logic is correct)
        logger.error('Approval transaction completed but no updated suggestion data returned', { suggestionId });
        res.status(500).json({ error: 'Internal server error during approval post-processing.' });
    }

  } catch (err) {
    logger.error('Error approving suggestion:', { error: err.message, stack: err.stack, adminUserId, suggestionId });
    res.status(500).json({ error: 'Internal server error approving suggestion' });
  }
});

// POST /api/suggestions/:id/reject - Reject a suggestion (admin)
router.post('/:id/reject', requireAdmin, async (req, res) => {
  const suggestionId = req.params.id;
  const adminUserId = req.user?.id;
  logger.debug('Admin attempting suggestion rejection', { adminUserId, suggestionId });
  try {
    const suggestion = await prisma.suggestion.findUnique({
      where: { id: suggestionId }
    });

    if (!suggestion) {
      logger.warn('Reject failed: Suggestion not found', { adminUserId, suggestionId });
      return res.status(404).json({ error: 'Suggestion not found' });
    }

    // Allow rejection of PENDING or even APPROVED (if undoing approval first isn't desired)
    if (suggestion.status !== 'PENDING' && suggestion.status !== 'APPROVED') {
      logger.warn('Reject failed: Suggestion not PENDING or APPROVED', { adminUserId, suggestionId, status: suggestion.status });
      return res.status(400).json({ error: 'Suggestion cannot be rejected in its current state' });
    }

    const updatedSuggestion = await prisma.suggestion.update({
      where: { id: suggestionId },
      data: { status: 'REJECTED', isLocked: false }, // Reject and ensure unlocked
      include: { user: { select: { id: true, username: true } } }
    });

    logger.info('Suggestion rejected successfully', { adminUserId, suggestionId });
    getIo().emit('suggestion:updated', updatedSuggestion); // Emit update
    getIo().to(`user:${updatedSuggestion.userId}`).emit('notification', { 
        type: 'suggestion_rejected', 
        message: `Your suggestion "${updatedSuggestion.title.substring(0, 30)}..." was rejected.`,
        suggestion: { id: updatedSuggestion.id, title: updatedSuggestion.title } // Send minimal info
    });

    res.json(updatedSuggestion);
  } catch (err) {
    logger.error('Error rejecting suggestion:', { error: err.message, stack: err.stack, adminUserId, suggestionId });
    res.status(500).json({ error: 'Internal server error rejecting suggestion' });
  }
});

// POST /api/suggestions/:id/undo-approve - Revert an approved suggestion to pending (admin)
router.post('/:id/undo-approve', requireAdmin, async (req, res) => {
  const suggestionId = req.params.id;
  const adminUserId = req.user?.id;
  logger.debug('Admin attempting suggestion approval undo', { adminUserId, suggestionId });

  try {
    const suggestion = await prisma.suggestion.findUnique({
      where: { id: suggestionId }
    });

    if (!suggestion) {
      logger.warn('Undo approve failed: Suggestion not found', { adminUserId, suggestionId });
      return res.status(404).json({ error: 'Suggestion not found' });
    }

    if (suggestion.status !== 'APPROVED') {
      logger.warn('Undo approve failed: Suggestion not APPROVED', { adminUserId, suggestionId, status: suggestion.status });
      return res.status(400).json({ error: 'Suggestion is not currently approved' });
    }

    // TODO: Consider implications if this song was added to a YouTube playlist.
    // Should it be removed from YouTube? Current logic only changes DB status.
    // For now, just revert the status.

    const updatedSuggestion = await prisma.suggestion.update({
      where: { id: suggestionId },
      data: { status: 'PENDING', isLocked: false }, // Revert to PENDING and unlock
      include: { user: { select: { id: true, username: true } } } // Include necessary data for frontend update
    });

    logger.info('Suggestion approval undone successfully', { adminUserId, suggestionId });
    getIo().emit('suggestion:updated', updatedSuggestion); // Emit update
    res.json(updatedSuggestion);

  } catch (err) {
    logger.error('Error undoing suggestion approval:', { error: err.message, stack: err.stack, adminUserId, suggestionId });
    res.status(500).json({ error: 'Internal server error undoing approval' });
  }
});

// POST /api/suggestions/:id/undo-reject - Revert a rejected suggestion to pending (admin)
router.post('/:id/undo-reject', requireAdmin, async (req, res) => {
  const suggestionId = req.params.id;
  const adminUserId = req.user?.id;
  const io = getIo();

  logger.debug('Attempting to undo rejection for suggestion', { suggestionId, adminUserId });

  try {
    const suggestionToUpdate = await prisma.suggestion.findUnique({
      where: { id: suggestionId },
      select: { status: true, userId: true } // Select necessary fields
    });

    if (!suggestionToUpdate) {
      logger.warn('Undo reject failed: Suggestion not found', { suggestionId, adminUserId });
      return res.status(404).json({ error: 'Suggestion not found' });
    }

    // Restore check for REJECTED status
    if (suggestionToUpdate.status !== 'REJECTED') {
      logger.warn('Undo reject failed: Suggestion is not currently rejected', { 
        suggestionId, 
        currentStatus: suggestionToUpdate.status, 
        adminUserId 
      });
      return res.status(400).json({ error: 'Suggestion is not currently rejected.' });
    }

    const updatedSuggestion = await prisma.suggestion.update({
      where: { id: suggestionId },
      data: {
        status: 'PENDING' // Set status back to PENDING
      },
      include: { // Include necessary fields for WebSocket update
        user: { select: { id: true, username: true } }
      }
    });

    logger.info('Suggestion rejection successfully undone', { 
      suggestionId: updatedSuggestion.id, 
      adminUserId,
      previousStatus: suggestionToUpdate.status,
      newStatus: 'PENDING'
    });

    // Emit WebSocket updates
    io.to('admin').emit('suggestion:updated', updatedSuggestion); // Update admin view
    io.to('suggestions').emit('suggestion:updated', updatedSuggestion); // Add back to public/dancer view
    // Optionally notify the original suggester
    io.to(`user:${suggestionToUpdate.userId}`).emit('notification', {
      type: 'suggestion_status_changed',
      message: `Your suggestion '${updatedSuggestion.title.substring(0, 30)}...' is now pending again.`, // Shorten title
      suggestionId: updatedSuggestion.id,
      newStatus: 'PENDING'
    });

    res.json(updatedSuggestion);

  } catch (err) {
    logger.error('Error undoing suggestion rejection:', { error: err.message, stack: err.stack, suggestionId, adminUserId });
    res.status(500).json({ error: 'Internal server error undoing rejection' });
  }
});

// POST /api/suggestions/:id/vote - Vote for a suggestion
router.post('/:id/vote', authenticateJWT, validateVote, async (req, res) => {
  const suggestionId = req.params.id;
  const userId = req.user?.id;
  
  if (!userId) {
    logger.warn('Vote attempt without authenticated user', { suggestionId });
    return res.status(401).json({ error: 'Authentication required to vote.' });
  }

  logger.debug('Attempting to vote on suggestion', { suggestionId, userId });

  try {
    let vote = null;
    let wasCreated = false;
    let finalSuggestionData = null;
    
    // Check if user has already voted
    const existingVote = await prisma.vote.findUnique({
          where: {
        userId_suggestionId: {
          userId: userId,
          suggestionId: suggestionId
        }
      }
    });

    if (existingVote) {
      logger.debug('User has already voted for this suggestion', { userId, suggestionId, voteId: existingVote.id });
      return res.status(409).json({ error: 'You have already voted for this suggestion.' });
    }

    // Check if suggestion exists, is not locked, and is in an appropriate status
    const suggestion = await prisma.suggestion.findUnique({
      where: { id: suggestionId },
      select: { id: true, status: true, isLocked: true, userId: true }
    });

    if (!suggestion) {
      logger.warn('Vote failed: Suggestion not found', { suggestionId, userId });
      return res.status(404).json({ error: 'Suggestion not found.' });
    }

    if (suggestion.isLocked) {
      logger.warn('Vote failed: Suggestion is locked', { suggestionId, userId });
      return res.status(403).json({ error: 'This suggestion is locked and cannot receive votes.' });
    }

    if (suggestion.status !== 'PENDING' && suggestion.status !== 'APPROVED') {
      logger.warn('Vote failed: Invalid suggestion status', { suggestionId, userId, status: suggestion.status });
      return res.status(403).json({ error: `Cannot vote on suggestions with status: ${suggestion.status}` });
    }

    // Check if user is voting for their own suggestion
    if (suggestion.userId === userId) {
      logger.warn('Vote failed: User attempted to vote for their own suggestion', { suggestionId, userId });
      return res.status(403).json({ error: 'You cannot vote for your own suggestion.' });
    }

    // Begin transaction to create vote and update suggestion
    await prisma.$transaction(async (tx) => {
      // Create vote
      vote = await tx.vote.create({
        data: {
          userId: userId,
          suggestionId: suggestionId
        }
      });
      wasCreated = true;
      logger.debug('Vote created', { voteId: vote.id, userId, suggestionId });

      // Update suggestion vote count
      const updatedSuggestionResult = await tx.suggestion.update({
        where: { id: suggestionId },
        data: { votes: { increment: 1 } },
        select: { id: true, votes: true, userId: true }
      });
      finalSuggestionData = updatedSuggestionResult;
      logger.debug('Suggestion vote count updated', { suggestionId, newVoteCount: finalSuggestionData.votes });

      // --- Achievement Logic --- 
      // 1. Award 'First Vote' to the voter
      // await awardAchievement(tx, userId, 'FIRST_VOTE');

      // 2. Check for 'Five Votes' achievement for the suggester
      if (finalSuggestionData.votes === 5) {
        // await awardAchievement(tx, finalSuggestionData.userId, 'FIVE_VOTES');
      }
      // 3. Check for 'Ten Votes' achievement for the suggester
      if (finalSuggestionData.votes === 10) {
        // await awardAchievement(tx, finalSuggestionData.userId, 'TEN_VOTES');
      }
    });

    // Check if transaction was successful and we have data
    if (!vote || !finalSuggestionData) {
        logger.error('Vote transaction failed or did not return expected data', { suggestionId, userId, voteExists: !!vote, finalSuggestionDataExists: !!finalSuggestionData });
        return res.status(500).json({ error: 'Internal server error processing vote transaction.' });
    }
    
    invalidateApprovedSuggestionsCache(); // Invalidate cache after successful vote
    logger.info('Vote successful', { voteId: vote.id, suggestionId, userId, wasCreated });
    
    // Emit WebSocket events using data retrieved AFTER transaction
    getIo().to('admin').emit('suggestion:vote', { suggestionId: finalSuggestionData.id, votes: finalSuggestionData.votes });
    getIo().to('suggestions').emit('suggestion:vote', { suggestionId: finalSuggestionData.id, votes: finalSuggestionData.votes });
    // Notify the suggester (suggestion.userId fetched before transaction is still valid)
    getIo().to(`user:${suggestion.userId}`).emit('notification', { 
      type: 'suggestion_vote',
      message: 'Your suggestion received a vote!',
      suggestionId: finalSuggestionData.id // Use ID from final data
    });
    
    res.status(wasCreated ? 201 : 200).json({ success: true, vote });
    
  } catch (err) {
    logger.error('Error processing vote:', { error: err.message, stack: err.stack, suggestionId, userId });
    if (err.code === 'P2002') {
      return res.status(409).json({ error: 'You have already voted for this suggestion (conflict).' });
    }
    res.status(500).json({ error: 'Internal server error processing vote' });
  }
});

// DELETE /api/suggestions/:id/vote - Unvote a suggestion
router.delete('/:id/vote', authenticateJWT, async (req, res) => {
  const suggestionId = req.params.id;
  const userId = req.user?.id;

  if (!userId) {
    logger.warn('Unvote attempt without authenticated user', { suggestionId });
    return res.status(401).json({ error: 'Authentication required to unvote.' });
  }

  logger.debug('Attempting to unvote suggestion', { suggestionId, userId });

  try {
    let deletedVoteId = null;
    let finalSuggestionData = null;

    // Check if the vote actually exists
    const existingVote = await prisma.vote.findUnique({
      where: {
        userId_suggestionId: {
          userId: userId,
          suggestionId: suggestionId
        }
      }
    });

    if (!existingVote) {
      logger.warn('Unvote failed: User has not voted for this suggestion', { userId, suggestionId });
      // It's not really an *error* state if they try to unvote something they haven't voted for,
      // maybe just return success but indicate no change?
      // For simplicity, returning 404 might be clearer for the client.
      return res.status(404).json({ error: 'You have not voted for this suggestion.' });
    }

    // Check suggestion status (optional, but good practice)
    const suggestion = await prisma.suggestion.findUnique({
      where: { id: suggestionId },
      select: { id: true, votes: true, userId: true } // Select suggester ID for notification
    });

    if (!suggestion) {
      // Should not happen if a vote exists, but good defense
      logger.error('Unvote inconsistency: Vote exists but suggestion not found', { suggestionId, userId, voteId: existingVote.id });
      return res.status(404).json({ error: 'Suggestion not found.' });
    }

    // Use transaction to ensure atomicity
    await prisma.$transaction(async (tx) => {
      // Delete the vote
      await tx.vote.delete({
        where: {
          userId_suggestionId: {
            userId: userId,
            suggestionId: suggestionId
          }
        }
      });
      deletedVoteId = existingVote.id; // Store the ID for logging
      logger.debug('Vote deleted', { voteId: deletedVoteId, userId, suggestionId });

      // Decrement suggestion vote count, ensuring it doesn't go below 0
      const updatedSuggestionResult = await tx.suggestion.update({
        where: { id: suggestionId },
        data: { votes: { decrement: 1 } },
        select: { id: true, votes: true }
      });
      finalSuggestionData = updatedSuggestionResult;
      logger.debug('Suggestion vote count decremented', { suggestionId, newVoteCount: finalSuggestionData.votes });
    });
    
    // Check if transaction was successful and we have data
    if (!deletedVoteId || !finalSuggestionData) {
        logger.error('Unvote transaction failed or did not return expected data', { suggestionId, userId, voteId: deletedVoteId, finalSuggestionDataExists: !!finalSuggestionData });
        return res.status(500).json({ error: 'Internal server error processing unvote transaction.' });
    }

    invalidateApprovedSuggestionsCache(); // Invalidate cache after successful unvote
    logger.info('Unvote successful', { voteId: deletedVoteId, suggestionId, userId });

    // Emit WebSocket events
    getIo().to('admin').emit('suggestion:vote', { suggestionId: finalSuggestionData.id, votes: finalSuggestionData.votes });
    getIo().to('suggestions').emit('suggestion:vote', { suggestionId: finalSuggestionData.id, votes: finalSuggestionData.votes });
    // Notify the suggester (optional, might be noisy)
    // getIo().to(`user:${suggestion.userId}`).emit('notification', {
    //   type: 'suggestion_unvote',
    //   message: 'Someone removed their vote from your suggestion.',
    //   suggestionId: finalSuggestionData.id
    // });

    res.status(200).json({ success: true, message: 'Vote removed.' });

  } catch (err) {
    logger.error('Error processing unvote:', { error: err.message, stack: err.stack, suggestionId, userId });
    res.status(500).json({ error: 'Internal server error processing unvote' });
  }
});

// PUT /api/suggestions/:id - Update suggestion lock status (admin only)
router.put('/:id', requireAdmin, async (req, res) => {
  const { id } = req.params;
  const { isLocked } = req.body;
  const adminUserId = req.user?.id;

  logger.debug('Attempting suggestion lock status update', { suggestionId: id, isLocked, adminUserId });

  if (typeof isLocked !== 'boolean') {
    logger.warn('Suggestion lock update failed: isLocked field must be a boolean', { suggestionId: id, providedValue: isLocked, adminUserId });
    return res.status(400).json({ error: 'The isLocked field must be a boolean.' });
  }

  try {
    const updatedSuggestion = await prisma.suggestion.update({
      where: { id },
      data: { isLocked },
      include: {
        user: { select: { id: true, username: true } }
      }
    });

    logger.info(`Suggestion lock status updated successfully`, { suggestionId: updatedSuggestion.id, isLocked: updatedSuggestion.isLocked, adminUserId });

    const suggestionWithVoteStatus = {
      ...updatedSuggestion,
      hasVoted: false
    };

    getIo().to('admin').emit('suggestion:updated', suggestionWithVoteStatus);
    getIo().to('suggestions').emit('suggestion:updated', suggestionWithVoteStatus);

    res.json(updatedSuggestion);

  } catch (err) {
    if (err.code === 'P2025') {
      logger.warn('Suggestion lock update failed: Suggestion not found', { suggestionId: id, adminUserId });
      return res.status(404).json({ error: 'Suggestion not found.' });
    }
    logger.error('Error updating suggestion lock status', { suggestionId: id, error: err.message, stack: err.stack, adminUserId });
    res.status(500).json({ error: 'Internal server error updating suggestion lock status' });
  }
});

// DELETE /api/suggestions/:id - Delete a suggestion (admin only)
router.delete('/:id', requireAdmin, async (req, res) => {
  const { id } = req.params;
  try {
    const deletedSuggestion = await prisma.suggestion.delete({
      where: { id }
    });
    res.json(deletedSuggestion);
  } catch (err) {
    logger.error('Error deleting suggestion:', err);
    res.status(500).json({ error: 'Internal server error deleting suggestion' });
  }
});

// PUT /api/suggestions/:id/lock - Update the lock status of a suggestion (admin)
router.put('/:id/lock', requireAdmin, 
  [
    param('id').matches(/^c[a-z0-9]{24}$/).withMessage('Invalid Suggestion ID format.'),
    body('isLocked').isBoolean().withMessage('isLocked must be a boolean value.'),
    handleValidationErrors
  ],
  async (req, res) => {
    const suggestionId = req.params.id;
    const { isLocked } = req.body; // Get the desired lock state from the request body
    const adminUserId = req.user?.id;
    const action = isLocked ? 'Locking' : 'Unlocking';
    logger.debug(`Admin attempting suggestion ${action.toLowerCase()}`, { adminUserId, suggestionId, isLocked });

    try {
      const suggestion = await prisma.suggestion.findUnique({
        where: { id: suggestionId }
      });

      if (!suggestion) {
        logger.warn(`${action} failed: Suggestion not found`, { adminUserId, suggestionId });
        return res.status(404).json({ error: 'Suggestion not found' });
      }

      // Optional: You might want to restrict locking/unlocking based on status
      // For example, only allow locking APPROVED suggestions
      /* 
      if (suggestion.status !== 'APPROVED') {
        logger.warn(`${action} failed: Suggestion not APPROVED`, { adminUserId, suggestionId, status: suggestion.status });
        return res.status(400).json({ error: 'Only approved suggestions can be locked/unlocked' });
      }
      */

      const updatedSuggestion = await prisma.suggestion.update({
        where: { id: suggestionId },
        data: { isLocked: isLocked },
        include: { 
          user: { select: { id: true, username: true } } // Include user for WebSocket update
        }
      });

      logger.info(`Suggestion ${action.toLowerCase()} successful`, { adminUserId, suggestionId, newLockStatus: updatedSuggestion.isLocked });

      // Emit WebSocket event
      getIo().to('admin').to('public').emit('suggestion:updated', updatedSuggestion);

      res.json({ success: true, data: updatedSuggestion });

    } catch (err) {
      logger.error(`Error ${action.toLowerCase()} suggestion:`, { error: err.message, stack: err.stack, adminUserId, suggestionId });
      res.status(500).json({ success: false, error: `Internal server error ${action.toLowerCase()} suggestion` });
    }
  });

// --- MOUNT COMMENT ROUTES --- Must be after routes using :suggestionId directly
router.use('/:suggestionId/comments', commentRoutes);

// NEW ROUTE: GET /api/suggestions/charts/:chartType/:style? - Get full paginated chart data
router.get('/charts/:chartType/:style?', tryAuthenticateJWT, validateChartViewQuery, async (req, res) => {
  try {
    const userId = req.user?.id; // Optional, used only for checking if user has voted
    const { chartType, style } = req.params;
    const { page = 1, limit = DEFAULT_PAGE_SIZE } = req.query;
    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);

    logger.debug('Fetching full chart view data', { userId, chartType, style, page: pageNumber, limit: limitNumber });

    const skip = (pageNumber - 1) * limitNumber;
    const take = limitNumber;

    let whereConditions = { status: 'APPROVED' }; // Base condition for most charts
    let orderByConditions = {};

    // If style is specified and not 'all', add it to the where conditions
    if (style && style.toLowerCase() !== 'all') {
      whereConditions.danceStyle = { equals: style, mode: 'insensitive' }; // Case-insensitive comparison
    }

    // --- Map frontend chart types to backend types ---
    let effectiveChartType = chartType.toLowerCase();
    if (effectiveChartType === 'trending') effectiveChartType = 'top-suggestions';
    if (effectiveChartType === 'top') effectiveChartType = 'style-top';
    if (effectiveChartType === 'monthly') effectiveChartType = 'monthly-vibes';
    
    // --- Determine query based on effectiveChartType and style ---
    switch (effectiveChartType) {
      case 'top-suggestions':
        // Sort by votes (most voted first)
        orderByConditions = { votes: 'desc' };
        break;
      
      case 'style-top':
        // Top songs for a style, sort by votes
        orderByConditions = { votes: 'desc' };
        break;
      
      case 'monthly-vibes':
        // Monthly chart - last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        whereConditions.createdAt = { gte: thirtyDaysAgo };
        orderByConditions = { votes: 'desc' }; // First by votes
        break;
      
      case 'weekly':
        // Weekly chart - last 7 days
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        whereConditions.createdAt = { gte: sevenDaysAgo };
        orderByConditions = { votes: 'desc' }; // First by votes
        break;
      
      default:
        // Default to trending
        orderByConditions = { votes: 'desc' };
        break;
    }

    logger.debug('Constructed chart query conditions', { whereConditions, orderByConditions, effectiveChartType });

    // --- Fetch data using Prisma ---
    const [totalCount, suggestions] = await prisma.$transaction([
      prisma.suggestion.count({ where: whereConditions }),
      prisma.suggestion.findMany({
        where: whereConditions,
        orderBy: orderByConditions,
        skip: skip,
        take: take,
        select: { // Select fields needed for the chart view
          id: true, title: true, channelTitle: true, thumbnailUrl: true, danceStyle: true,
          status: true, votes: true, isLocked: true, createdAt: true, updatedAt: true,
          userId: true, youtubeVideoId: true, durationSeconds: true,
          user: { select: { username: true, id: true } },
          voters: {
            select: {
              user: {
                select: {
                  id: true,
                  username: true,
                  profile: { select: { displayName: true, avatarUrl: true } }
                }
              }
            }
          }
        }
      })
    ]);

    logger.debug(`Found ${suggestions.length} chart suggestions for page ${pageNumber} (total: ${totalCount})`, { userId, chartType, style, page: pageNumber });

    // Process voters array for each suggestion to the expected format
    const processedSuggestions = suggestions.map(suggestion => {
      // Transform voters array from { user: {...} } to just the user object
      const voters = suggestion.voters.map(v => v.user);
      
      // Determine if current user has voted
      const hasVoted = userId ? voters.some(voter => voter.id === userId) : false;
      
      return { ...suggestion, voters, hasVoted };
    });

    const totalPages = Math.ceil(totalCount / limitNumber);

    logger.info(`Returning ${processedSuggestions.length} chart suggestions for full view`, { 
      userId, 
      chartType, 
      style, 
      page: pageNumber, 
      limit: limitNumber, 
      totalCount, 
      totalPages 
    });

    // Return structured response with pagination
    res.json({
      data: processedSuggestions,
      pagination: {
        currentPage: pageNumber,
        totalPages: totalPages,
        totalCount: totalCount,
        limit: limitNumber
      },
      chartInfo: { // Add some context about the chart being viewed
        type: chartType,
        style: style || 'all'
      },
      success: true
    });

  } catch (err) {
    logger.error('Error fetching full chart view data:', { error: err.message, stack: err.stack, chartType: req.params.chartType, style: req.params.style });
    res.status(500).json({ success: false, error: 'Internal server error fetching chart data.' });
  }
});

// GET /api/suggestions/by-users - Fetch suggestions made by a specific list of users
router.get('/by-users', authenticateJWT, 
  [
    query('userIds')
      .notEmpty().withMessage('userIds query parameter is required.')
      .custom((value) => {
        // Check if it's a comma-separated string of valid CUIDs
        const ids = value.split(',');
        if (!ids.every(id => /^c[a-zA-Z0-9]{24}$/.test(id.trim()))) {
          throw new Error('userIds must be a comma-separated list of valid User IDs.');
        }
        return true;
      })
      .customSanitizer(value => value.split(',').map(id => id.trim())), // Sanitize into an array
    query('limit')
      .optional()
      .isInt({ min: 1, max: 20 }).withMessage('Limit must be between 1 and 20') // Smaller limit for this specific query?
      .toInt()
  ],
  handleValidationErrors,
  async (req, res) => {
    const requestingUserId = req.user?.id;
    const targetUserIds = req.query.userIds; // Already sanitized into an array
    const limit = req.query.limit || 5; // Default limit

    if (!requestingUserId) {
      return res.status(401).json({ error: 'Authentication required.' });
    }

    if (!targetUserIds || targetUserIds.length === 0) {
      // Should be caught by validation, but good defense
      return res.status(400).json({ error: 'No user IDs provided.' });
    }

    logger.debug('Fetching suggestions by favorite users', { requestingUserId, targetUserIds, limit });

    try {
      const suggestions = await prisma.suggestion.findMany({
        where: {
          userId: { in: targetUserIds },
          status: 'APPROVED' // Only show approved suggestions from favorites
        },
        orderBy: [
          { createdAt: 'desc' } // Primarily sort by newest
          //{ votes: 'desc' } // Could also sort by votes
        ],
        take: limit,
        select: {
          id: true, title: true, channelTitle: true, thumbnailUrl: true, danceStyle: true,
          status: true, votes: true, isLocked: true, createdAt: true, updatedAt: true,
          userId: true, youtubeVideoId: true, durationSeconds: true,
          user: { select: { username: true, id: true } },
          voters: {
            select: {
              user: {
                select: {
                  id: true,
                  username: true,
                  profile: { select: { displayName: true, avatarUrl: true } }
                }
              }
            }
          }
        }
      });

      // Fetch vote status for the *requesting* user
      const suggestionIdsOnPage = suggestions.map(s => s.id);
      const userVotes = suggestionIdsOnPage.length > 0 ? await prisma.vote.findMany({
        where: {
          userId: requestingUserId,
          suggestionId: { in: suggestionIdsOnPage }
        },
        select: { suggestionId: true }
      }) : [];
      const votedSuggestionIds = new Set(userVotes.map(vote => vote.suggestionId));

      const suggestionsWithVoteStatus = suggestions.map(suggestion => {
        const voters = suggestion.voters.map(v => v.user);
        const hasVoted = voters.some(u => u.id === requestingUserId);
        return { ...suggestion, voters, hasVoted };
      });

      logger.info(`Returning ${suggestionsWithVoteStatus.length} suggestions by favorite users`, { requestingUserId, count: suggestionsWithVoteStatus.length });
      res.json({ success: true, data: suggestionsWithVoteStatus });

    } catch (error) {
      logger.error('Error fetching suggestions by users:', { requestingUserId, targetUserIds, error: error.message, stack: error.stack });
      res.status(500).json({ error: 'Internal server error fetching suggestions.' });
    }
  }
);

// GET /api/suggestions/search - Search approved songs by title or artist
router.get('/search', authenticateJWT, async (req, res) => {
  const { query } = req.query;
  const userId = req.user?.id;

  if (!query || typeof query !== 'string' || query.trim().length < 1) {
    return res.status(400).json({ message: 'Search query parameter is required.' });
  }

  const searchQuery = query.trim();
  logger.info('Searching approved songs', { query: searchQuery, userId });

  try {
    const songs = await prisma.suggestion.findMany({
      where: {
        status: 'APPROVED',
        OR: [
          { title: { contains: searchQuery, mode: 'insensitive' } },
          { channelTitle: { contains: searchQuery, mode: 'insensitive' } }
        ]
      },
      select: {
        id: true,
        youtubeVideoId: true,
        title: true,
        channelTitle: true,
        thumbnailUrl: true,
        danceStyle: true,
        votes: true,
        durationSeconds: true,
        createdAt: true,
        user: {
          select: {
            id: true,
            username: true,
            profile: { select: { displayName: true, avatarUrl: true } }
          }
        },
        voters: {
          select: {
            user: {
              select: {
                id: true,
                username: true,
                profile: { select: { displayName: true, avatarUrl: true } }
              }
            }
          }
        }
      },
      orderBy: [
        { votes: 'desc' },
        { createdAt: 'desc' }
      ],
      take: 20
    });

    // Transform voters data to match the expected format
    const transformedSongs = songs.map(song => ({
      ...song,
      voters: song.voters.map(v => v.user),
      hasVoted: song.voters.some(v => v.user.id === userId)
    }));

    logger.info(`Found ${transformedSongs.length} songs matching query "${searchQuery}"`);
    res.json(transformedSongs);
  } catch (err) {
    logger.error('Error searching songs:', { error: err.message, stack: err.stack, userId });
    res.status(500).json({ error: 'Internal server error searching songs' });
  }
});

// POST /api/suggestions/playlist - Submit an entire YouTube playlist for suggestions
router.post('/playlist', authenticateJWT, validatePlaylistSuggestion, async (req, res) => {
  const { youtubePlaylistUrl, danceStyle } = req.body;
  const userId = req.user?.id;
  const username = req.user?.username;

  // Permission Check: Allow all authenticated users to submit playlists
  // No special permission required - all dancers can now submit playlists

  logger.info('Playlist submission attempt', { userId, username, youtubePlaylistUrl, danceStyle });

  try {
    const videoDetailsList = await getPlaylistVideoDetails(youtubePlaylistUrl);

    if (!videoDetailsList || videoDetailsList.length === 0) {
      logger.warn('No videos found in the provided playlist URL or playlist is empty.', { youtubePlaylistUrl, userId });
      return res.status(404).json({ error: 'No videos found in the provided playlist URL or the playlist is empty.' });
    }

    const createdSuggestions = [];
    let failedCount = 0;

    // Use a transaction if you want all-or-nothing, though for batch it might be better to process what we can.
    // For now, processing one by one.
    for (const video of videoDetailsList) {
      try {
        // Check if this video has already been suggested by *anyone* and is PENDING or APPROVED
        const existingSuggestion = await prisma.suggestion.findFirst({
          where: {
            youtubeVideoId: video.videoId,
            status: { in: ['PENDING', 'APPROVED'] }
          }
        });

        if (existingSuggestion) {
          logger.info('Skipping already suggested video from playlist submission.', {
            videoId: video.videoId,
            title: video.title,
            existingStatus: existingSuggestion.status,
            userId
          });
          failedCount++;
          continue;
        }

        const newSuggestion = await prisma.suggestion.create({
          data: {
            userId: userId,
            youtubeVideoId: video.videoId,
            title: video.title || 'Untitled Video',
            channelTitle: video.channelTitle || 'Unknown Channel',
            thumbnailUrl: video.thumbnailUrl || null,
            danceStyle: danceStyle,
            status: 'PENDING', // All playlist songs go to PENDING
            votes: 0, // Start with 0 votes
            isLocked: false,
            durationSeconds: video.durationSeconds,
          },
          include: {
            user: { select: { username: true } }
          }
        });
        createdSuggestions.push(newSuggestion);
        logger.info('Created suggestion from playlist.', { suggestionId: newSuggestion.id, videoId: video.videoId, userId });

      } catch (singleError) {
        failedCount++;
        logger.error('Failed to create a suggestion for a video from playlist.', {
          videoId: video.videoId,
          title: video.title,
          error: singleError.message,
          userId
        });
      }
    }

    if (createdSuggestions.length > 0) {
      // Notify admin via WebSocket
      const io = getIo();
      if (io) {
        io.to('admin_room').emit('admin:playlist_submitted', {
          submitterUsername: username,
          playlistUrl: youtubePlaylistUrl,
          danceStyle,
          count: createdSuggestions.length,
          submittedAt: new Date().toISOString(),
        });
        logger.info('Admin notification sent for playlist submission.', { count: createdSuggestions.length, submitter: username });
      }

      const message = `Successfully submitted ${createdSuggestions.length} new song(s) for approval. ${failedCount > 0 ? `${failedCount} song(s) were skipped (e.g., already suggested or errors).` : ''}`;
      return res.status(201).json({ 
        message,
        count: createdSuggestions.length,
        failedCount,
        suggestions: createdSuggestions 
      });
    } else {
      return res.status(400).json({ error: `No new songs could be submitted. ${failedCount > 0 ? `${failedCount} song(s) were skipped.` : 'The playlist might be empty, all songs already suggested, or an error occurred.'}` });
    }

  } catch (error) {
    logger.error('Error processing playlist submission:', {
      error: error.message,
      stack: error.stack,
      youtubePlaylistUrl,
      userId
    });
    if (error.message.includes('Invalid YouTube playlist URL')) {
        return res.status(400).json({ error: 'Invalid YouTube playlist URL provided.' });
    }
    if (error.message.includes('Failed to fetch video details')) {
        return res.status(502).json({ error: 'Could not fetch details from the YouTube playlist. Please check the URL and try again.' });
    }
    res.status(500).json({ error: 'Internal server error processing playlist submission.' });
  }
});

// Invalidate cache on relevant updates (e.g., new approval, new vote)
const invalidateApprovedSuggestionsCache = () => {
  logger.info('Invalidating approved suggestions cache.');
  approvedSuggestionsCache.data = null;
  approvedSuggestionsCache.timestamp = 0;
};

// PUT /api/suggestions/:id/update-style - Update a suggestion's dance style (admin only)
router.put('/:id/update-style', requireAdmin, [
  body('danceStyle')
    .trim()
    .notEmpty().withMessage('Dance style is required')
    .isLength({ min: 1, max: 50 }).withMessage('Dance style seems invalid'),
  handleValidationErrors
], async (req, res) => {
  const suggestionId = req.params.id;
  const { danceStyle } = req.body;
  const adminUserId = req.user?.id;
  
  logger.debug('Admin attempting to update dance style', { adminUserId, suggestionId, newStyle: danceStyle });

  try {
    const suggestion = await prisma.suggestion.findUnique({
      where: { id: suggestionId },
      select: { 
        id: true, 
        userId: true, 
        status: true, 
        danceStyle: true
      }
    });

    if (!suggestion) {
      logger.warn('Update style failed: Suggestion not found', { adminUserId, suggestionId });
      return res.status(404).json({ error: 'Suggestion not found' });
    }

    // Update the dance style
    const updatedSuggestion = await prisma.suggestion.update({
      where: { id: suggestionId },
      data: { danceStyle: danceStyle },
      include: { 
        user: { 
          select: { 
            id: true, 
            username: true 
          } 
        } 
      }
    });

    logger.info('Suggestion dance style updated successfully', { 
      adminUserId, 
      suggestionId, 
      oldStyle: suggestion.danceStyle,
      newStyle: danceStyle
    });
    
    // Emit WebSocket update
    getIo().emit('suggestion:updated', updatedSuggestion);
    
    res.json(updatedSuggestion);
  } catch (err) {
    logger.error('Error updating suggestion dance style:', { error: err.message, stack: err.stack, adminUserId, suggestionId });
    res.status(500).json({ error: 'Internal server error updating dance style' });
  }
});

module.exports = router;