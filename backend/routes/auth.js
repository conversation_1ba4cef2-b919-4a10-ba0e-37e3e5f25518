const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const passport = require('passport');
const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const { requireAdmin, authenticateJWT } = require('../middleware/auth');
const { google } = require('googleapis');
const crypto = require('crypto');
const { body, validationResult } = require('express-validator');

const router = express.Router();
const prisma = new PrismaClient();

// Determine if we should force cookies to be secure, even in non-production
// This helps when using Cloudflare Flexible SSL with tunneling services like Pinggy
const forceSecureCookies = process.env.USE_CLOUDFLARE_FLEXIBLE_SSL === 'true';

// Google OAuth2 setup
const oauth2Client = new google.auth.OAuth2(
  process.env.YOUTUBE_CLIENT_ID,
  process.env.YOUTUBE_CLIENT_SECRET,
  process.env.YOUTUBE_REDIRECT_URI
);
const SCOPES = [
  'https://www.googleapis.com/auth/youtube',
  'https://www.googleapis.com/auth/youtube.force-ssl',
  'https://www.googleapis.com/auth/userinfo.profile',
  'https://www.googleapis.com/auth/youtube.readonly'
];

// Validation middleware function (can be reused or adapted)
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.warn('API Validation failed', { 
      route: req.path,
      errors: errors.array(), 
      ip: req.ip 
    });
    return res.status(400).json({ error: errors.array()[0].msg });
  }
  next();
};

// Validation rules for login
const validateLogin = [
  body('username')
    .trim()
    .notEmpty().withMessage('Username is required'),
    // No need for length/alphanumeric checks here usually, just presence
  body('password')
    .notEmpty().withMessage('Password is required'),
  handleValidationErrors // Use the shared error handler
];

// POST /api/auth/login
router.post('/login', validateLogin, async (req, res) => {
  const { username, password } = req.body;
  try {
    const user = await prisma.user.findUnique({ where: { username } });
    if (!user || !user.passwordHash) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    const valid = await bcrypt.compare(password, user.passwordHash);
    if (!valid) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    const token = jwt.sign(
      { userId: user.id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRY || '24h' }
    );
    
    // Set token in HTTP-only cookie
    res.cookie('authToken', token, { 
        httpOnly: true, 
        secure: process.env.NODE_ENV === 'production' || forceSecureCookies, // Use secure cookies in production or with Cloudflare Flexible SSL
        maxAge: (parseInt(process.env.JWT_EXPIRY_SECONDS) || 24 * 60 * 60) * 1000, // Cookie expiry matches JWT
        sameSite: 'lax' // Or 'strict' depending on needs
    });
    
    // Return token, role, userId, and username in response body (removed credits)
    res.json({ 
        token, 
        role: user.role, 
        userId: user.id, 
        username: user.username
    });
  } catch (err) {
    logger.error('Login error:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Validation middleware for signup
const validateSignup = [
  body('username')
    .trim()
    .notEmpty().withMessage('Username is required')
    .isLength({ min: 3, max: 30 }).withMessage('Username must be between 3 and 30 characters')
    .isAlphanumeric().withMessage('Username must contain only letters and numbers'),
  body('password')
    .isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
  // Middleware to check validation results
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      // Log the validation errors for debugging
      logger.warn('Signup validation failed', { errors: errors.array(), ip: req.ip });
      // Return a 400 Bad Request with the first error message for simplicity
      // Or you could return all errors: errors.array()
      return res.status(400).json({ error: errors.array()[0].msg }); 
    }
    next(); // Proceed to the route handler if validation passes
  }
];

// POST /api/auth/signup (Public user registration)
router.post('/signup', validateSignup, async (req, res) => {
  const { username, password } = req.body;

  try {
    // Check if username already exists
    const existingUser = await prisma.user.findUnique({ where: { username } });
    if (existingUser) {
      return res.status(409).json({ error: 'Username already taken' }); // 409 Conflict
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 10);

    // Create new user (default role is DANCER, no credits needed)
    const user = await prisma.user.create({
      data: {
        username,
        passwordHash,
        role: 'DANCER' // Default role for public signup
      },
      select: { // Only return non-sensitive info
          id: true,
          username: true,
          role: true
      }
    });

    logger.info('New user registered successfully', { userId: user.id, username: user.username });

    // Optionally: Log the user in immediately after signup
    const token = jwt.sign(
      { userId: user.id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRY || '24h' }
    );

    res.cookie('authToken', token, { 
        httpOnly: true, 
        secure: process.env.NODE_ENV === 'production' || forceSecureCookies,
        maxAge: (parseInt(process.env.JWT_EXPIRY_SECONDS) || 24 * 60 * 60) * 1000,
        sameSite: 'lax' 
    });

    // Return user info and token (removed credits)
    res.status(201).json({ 
        token, 
        user: { id: user.id, username: user.username, role: user.role } 
    });

  } catch (err) {
    logger.error('Signup error:', err);
    // Avoid leaking detailed error info in production
    res.status(500).json({ error: 'Internal server error during signup' });
  }
});

// GET /api/auth/me - Get current authenticated user
router.get('/me', authenticateJWT, async (req, res) => {
  if (!req.user || !req.user.id) {
    // authenticateJWT middleware should already handle this case by returning a 401
    logger.warn('AuthMe: No user or user.id on req.user after authenticateJWT', { userObject: req.user });
    return res.status(401).json({ message: 'Not authenticated or user data missing' });
  }

  try {
    // Fetch the most up-to-date user data directly from the database
    const freshUserData = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        username: true,
        role: true
        // Removed credits from selection
      }
    });
    
    if (!freshUserData) {
      logger.warn('AuthMe: User not found in database', { userId: req.user.id });
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Use the freshly fetched user data
    logger.info('AuthMe: User session verified via /me endpoint', { 
      userId: freshUserData.id,
      role: freshUserData.role
    });
    
    res.json({ 
      user: freshUserData
    });

  } catch (error) {
    logger.error('AuthMe: Error processing /me endpoint', { error: error.message, userId: req.user?.id, stack: error.stack });
    res.status(500).json({ message: 'Error fetching user details' });
  }
});

// POST /api/auth/logout
router.post('/logout', authenticateJWT, (req, res) => {
  // The primary logout mechanism is deleting the token client-side.
  // This endpoint serves to clear the HttpOnly cookie as a best practice.
  logger.info('User logging out', { userId: req.user?.id });
  
  res.cookie('authToken', '', { 
    httpOnly: true, 
    secure: process.env.NODE_ENV === 'production' || forceSecureCookies,
    expires: new Date(0), // Expire immediately
    sameSite: 'lax'
  });
  
  res.status(200).json({ message: 'Logout successful' });
});

// POST /api/auth/register (admin only, for creating dancers)
router.post('/register', authenticateJWT, requireAdmin, async (req, res) => {
  const { username, password, role } = req.body;
  if (!username) {
    return res.status(400).json({ error: 'Username is required' });
  }
  try {
    let passwordHash = undefined;
    if (password) {
      passwordHash = await bcrypt.hash(password, 10);
    }
    const user = await prisma.user.create({
      data: {
        username,
        passwordHash,
        role: role === 'ADMIN' ? 'ADMIN' : 'DANCER',
      }
    });
    res.status(201).json({ id: user.id, username: user.username, role: user.role });
  } catch (err) {
    logger.error('Register error:', err);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/auth/youtube/initiate (admin only)
router.get('/youtube/initiate', authenticateJWT, requireAdmin, (req, res) => {
  logger.debug('YouTube OAuth initiate called', {
    user: req.user,
    headers: req.headers,
    query: req.query,
    url: req.originalUrl
  });
  try {
    const url = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: SCOPES,
      prompt: 'consent',
    });
    logger.debug('Generated YouTube OAuth URL', { url });
    res.redirect(url);
  } catch (err) {
    logger.error('YouTube OAuth initiate error', { error: err, stack: err.stack });
    res.status(500).send('OAuth initiation error');
  }
});

// GET /api/auth/youtube/callback (admin only)
router.get('/youtube/callback', authenticateJWT, requireAdmin, async (req, res) => {
  logger.debug('YouTube OAuth callback called', {
    user: req.user,
    headers: req.headers,
    query: req.query,
    url: req.originalUrl
  });
  const code = req.query.code;
  if (!code) {
    logger.error('YouTube OAuth callback missing code', { query: req.query });
    return res.status(400).send('Missing code');
  }
  try {
    logger.debug('Exchanging code for tokens', { code });
    const { tokens } = await oauth2Client.getToken(code);
    logger.info('YouTube OAuth tokens received');
    
    // Set credentials on the client to fetch profile info
    oauth2Client.setCredentials(tokens);
    
    // Check if we have the scope we need for watch history
    const hasHistoryScope = tokens.scope && (
      tokens.scope.includes('https://www.googleapis.com/auth/history') ||
      tokens.scope.includes('https://www.googleapis.com/auth/history.readonly')
    );
    
    if (!hasHistoryScope) {
      logger.warn('YouTube OAuth: Missing history.readonly scope', { 
        grantedScopes: tokens.scope, 
        needsScopes: ['https://www.googleapis.com/auth/history', 'https://www.googleapis.com/auth/history.readonly'] 
      });
    }
    
    // Fetch user profile info from Google
    const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
    let profileInfo = null;
    try {
        const profileRes = await oauth2.userinfo.get();
        profileInfo = profileRes.data;
        logger.info('Fetched Google User Info', { profileId: profileInfo.id, email: profileInfo.email });
    } catch (profileError) {
        logger.error('Failed to fetch Google User Info after getting tokens', { error: profileError });
        // Decide if this should be fatal or just log and continue without profile info
    }

    // Encryption function (keep as is for now)
    const encrypt = (text) => {
      if (!text) return null;
      const key = Buffer.from(process.env.DB_ENCRYPTION_KEY || '0123456789abcdef0123456789abcdef', 'hex');
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return iv.toString('hex') + ':' + encrypted;
    };

    // Save tokens and profile info to the database
    const expiryDate = tokens.expiry_date ? new Date(tokens.expiry_date) : null;
    const encryptedAccessToken = encrypt(tokens.access_token);
    const encryptedRefreshToken = encrypt(tokens.refresh_token);

    await prisma.adminSettings.upsert({
      where: { singletonLock: true },
      update: {
        accessToken: encryptedAccessToken,
        refreshToken: encryptedRefreshToken, // Store the refresh token!
        tokenExpiry: expiryDate,
        youtubeProfileId: profileInfo?.id || null,
        youtubeEmail: profileInfo?.email || null,
        isYouTubeConnected: true // Set connected flag
      },
      create: {
        singletonLock: true,
        accessToken: encryptedAccessToken,
        refreshToken: encryptedRefreshToken, // Store the refresh token!
        tokenExpiry: expiryDate,
        youtubeProfileId: profileInfo?.id || null,
        youtubeEmail: profileInfo?.email || null,
        isYouTubeConnected: true // Set connected flag
      }
    });
    logger.info('Successfully saved YouTube OAuth tokens and profile info.');
    
    // Clear credentials from shared client instance after use
    oauth2Client.setCredentials(null);

    // Redirect to frontend settings page with success flag
    const frontendSettingsUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/admin/settings?youtube=connected`;
    logger.debug('Redirecting to frontend', { url: frontendSettingsUrl });
    res.redirect(frontendSettingsUrl);
  } catch (err) {
    logger.error('YouTube OAuth callback error', { error: err, stack: err.stack });
    res.status(500).send('OAuth error');
  }
});

// GET /api/auth/youtube/status - Check connection status (admin only)
router.get('/youtube/status', authenticateJWT, requireAdmin, async (req, res) => {
    logger.debug('Checking YouTube connection status', { userId: req.user.id });
    try {
        const settings = await prisma.adminSettings.findUnique({
            where: { singletonLock: true },
            select: {
                isYouTubeConnected: true,
                youtubeEmail: true,
                youtubeProfileId: true
            }
        });

        if (!settings) {
            logger.warn('YouTube status check: AdminSettings record not found');
            return res.json({ isConnected: false, email: null, profileId: null });
        }

        logger.debug('YouTube connection status retrieved', { settings });
        res.json({
            isConnected: settings.isYouTubeConnected || false,
            email: settings.youtubeEmail,
            profileId: settings.youtubeProfileId
        });

    } catch (err) {
        logger.error('Error fetching YouTube connection status', { error: err.message, stack: err.stack });
        res.status(500).json({ error: 'Internal server error' });
    }
});

// --- LOGGING ENDPOINT FOR FRONTEND LOGS ---
router.post('/logs', (req, res) => {
  try {
    // Handle batched logs
    if (req.body.batch && Array.isArray(req.body.logs)) {
      // Process a batch of logs at once
      const logs = req.body.logs;
      
      // Limit the number of logs processed per batch to prevent abuse
      const maxLogsPerBatch = 10;
      const logsToProcess = logs.slice(0, maxLogsPerBatch);
      
      // Process each log in the batch
      logsToProcess.forEach(log => {
        const { level = 'info', message, error, info, data, timestamp } = log || {};
        
        // Skip empty logs
        if (!message) return;
        
        const logDetails = { message, error, info, data, timestamp, source: 'frontend' };
        
        if (level === 'error') {
          logger.error('[FRONTEND] ' + message, logDetails);
        } else if (level === 'warn') {
          logger.warn('[FRONTEND] ' + message, logDetails);
        } else if (level === 'debug') {
          logger.debug('[FRONTEND] ' + message, logDetails);
        } else {
          logger.info('[FRONTEND] ' + message, logDetails);
        }
      });
      
      res.status(200).json({ processed: logsToProcess.length });
    } else {
      // Handle single log (legacy mode)
      const { level = 'info', message, error, info, data, timestamp } = req.body || {};
      const logDetails = { message, error, info, data, timestamp, source: 'frontend' };
      
      if (level === 'error') {
        logger.error('[FRONTEND] ' + message, logDetails);
      } else if (level === 'warn') {
        logger.warn('[FRONTEND] ' + message, logDetails);
      } else if (level === 'debug') {
        logger.debug('[FRONTEND] ' + message, logDetails);
      } else {
        logger.info('[FRONTEND] ' + message, logDetails);
      }
      
      res.status(204).end();
    }
  } catch (err) {
    logger.error('Error processing frontend log', { error: err.message, body: req.body });
    res.status(204).end(); // Still return 204 to avoid client errors
  }
});

// --- Google Sign-In Routes ---

// Route to initiate Google OAuth flow
// Frontend will link to this route (e.g., <a href="/api/auth/google">Sign in with Google</a>)
router.get('/google', 
  passport.authenticate('google', { scope: ['profile', 'email'] })
);

// Callback route for Google to redirect to after authentication
router.get('/google/callback',
  passport.authenticate('google', {
    failureRedirect: process.env.FRONTEND_URL ? `${process.env.FRONTEND_URL}/login?error=google_auth_failed` : '/login?error=google_auth_failed', // Redirect to frontend login page on failure
    session: false // We are using JWTs, not sessions managed by Passport after this
  }),
  (req, res) => {
    // Successful authentication, req.user is populated by Passport strategy
    const user = req.user;

    if (!user) {
      logger.error('Google OAuth callback: No user object found after passport.authenticate.');
      // Redirect to an error page or login page on the frontend
      return res.redirect(process.env.FRONTEND_URL ? `${process.env.FRONTEND_URL}/login?error=authentication_failed` : '/login?error=authentication_failed');
    }

    try {
      const token = jwt.sign(
        { userId: user.id, role: user.role, username: user.username || user.name }, // Include username or name for frontend use
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRY || '24h' }
      );

      res.cookie('authToken', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production' || forceSecureCookies, // Fixed: Added forceSecureCookies
        maxAge: (parseInt(process.env.JWT_EXPIRY_SECONDS) || 24 * 60 * 60) * 1000,
        sameSite: 'lax'
      });

      // Redirect to the frontend dashboard or homepage after successful login
      // The frontend can then fetch user details if needed using the token or from local storage if set.
      logger.info('User successfully authenticated via Google', { userId: user.id, email: user.email });
      res.redirect(process.env.FRONTEND_URL || '/'); 

    } catch (error) {
      logger.error('Error generating JWT or setting cookie after Google OAuth:', { error: error.message, userId: user.id });
      // Redirect to an error page or login page on the frontend
      res.redirect(process.env.FRONTEND_URL ? `${process.env.FRONTEND_URL}/login?error=server_error` : '/login?error=server_error');
    }
  }
);

// --- End Google Sign-In Routes ---

// --- Facebook Sign-In Routes ---

// Route to initiate Facebook OAuth flow
// Frontend will link to this route (e.g., <a href="/api/auth/facebook">Sign in with Facebook</a>)
router.get('/facebook', 
  passport.authenticate('facebook', { scope: ['email', 'public_profile'] })
);

// Callback route for Facebook to redirect to after authentication
router.get('/facebook/callback',
  (req, res, next) => {
    passport.authenticate('facebook', {
      failureRedirect: process.env.FRONTEND_URL ? `${process.env.FRONTEND_URL}/login?error=facebook_auth_failed` : '/login?error=facebook_auth_failed',
      session: false // We are using JWTs, not sessions managed by Passport for the main app state
    }, (err, user, info) => {
      if (err) {
        logger.error('Facebook auth callback error:', { error: err.message, stack: err.stack, info });
        return res.redirect(process.env.FRONTEND_URL ? `${process.env.FRONTEND_URL}/login?error=facebook_auth_failed&message=${encodeURIComponent(err.message || 'Unknown error')}` : '/login?error=facebook_auth_failed');
      }
      if (!user) {
        logger.warn('Facebook auth callback - no user returned. Redirecting to login.', { info });
        const infoMessage = info && info.message ? info.message : 'Authentication failed or user not found.';
        return res.redirect(process.env.FRONTEND_URL ? `${process.env.FRONTEND_URL}/login?error=facebook_auth_no_user&message=${encodeURIComponent(infoMessage)}` : '/login?error=facebook_auth_no_user');
      }

      // User is authenticated, generate JWT and set cookie
      const token = jwt.sign(
        { userId: user.id, role: user.role },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRY || '24h' }
      );

      res.cookie('authToken', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production' || forceSecureCookies, // Fixed: Added forceSecureCookies
        maxAge: (parseInt(process.env.JWT_EXPIRY_SECONDS) || 24 * 60 * 60) * 1000,
        sameSite: 'lax'
      });

      // Redirect to a frontend page that will handle the post-login state (e.g., dashboard or previous page)
      // The frontend AuthProvider will pick up the session via /api/auth/me using the cookie.
      res.redirect(process.env.FRONTEND_URL || '/'); 

    })(req, res, next);
  }
);

// TODO: Add logout endpoint as needed

module.exports = router; 