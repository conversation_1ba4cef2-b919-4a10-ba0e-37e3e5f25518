const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const gameService = require('./gameService');

const prisma = new PrismaClient();

// Constants for battle calculations
const BASE_DAMAGE = 20;
const BASE_ACCURACY = 80;
const MAX_TURNS = 6;

// Calculate damage for a move
const calculateDamage = (attacker, defender, move, timingScore = 0) => {
  // Base damage from the move
  let damage = move.power;
  
  // Level bonus (5% per level)
  const levelBonus = 1 + (attacker.level * 0.05);
  damage *= levelBonus;
  
  // Timing bonus (up to 20% extra damage for perfect timing)
  const timingBonus = 1 + (timingScore / 500); // 0-100 score gives 0-20% bonus
  damage *= timingBonus;
  
  // Trait modifiers (placeholder for now)
  // TODO: Implement trait modifiers
  
  // Randomize slightly (±10%)
  const randomFactor = 0.9 + (Math.random() * 0.2);
  damage *= randomFactor;
  
  // Round to integer
  return Math.round(damage);
};

// Calculate hit chance for a move
const calculateHitChance = (attacker, defender, move, timingScore = 0) => {
  // Base accuracy from the move
  let accuracy = move.accuracy;
  
  // Level difference bonus/penalty (2% per level difference)
  const levelDiff = attacker.level - defender.level;
  accuracy += (levelDiff * 2);
  
  // Timing bonus (up to 10% extra accuracy for perfect timing)
  const timingBonus = timingScore / 10;
  accuracy += timingBonus;
  
  // Trait modifiers (placeholder for now)
  // TODO: Implement trait modifiers
  
  // Cap between 5% and 95%
  return Math.min(95, Math.max(5, accuracy));
};

// Check if the attack hits
const doesAttackHit = (hitChance) => {
  return (Math.random() * 100) < hitChance;
};

// Process a single turn of battle
const processBattleTurn = async (battleId, attackerHeroId, defenderHeroId, moveId, timingScore = 0) => {
  try {
    // Get attacker, defender, and move details
    const [attacker, defender, move] = await Promise.all([
      prisma.hero.findUnique({ where: { id: attackerHeroId } }),
      prisma.hero.findUnique({ where: { id: defenderHeroId } }),
      prisma.battleMove.findUnique({ where: { id: moveId } })
    ]);
    
    if (!attacker || !defender || !move) {
      return { 
        success: false, 
        error: !attacker ? 'Attacker not found' : !defender ? 'Defender not found' : 'Move not found' 
      };
    }
    
    // Calculate hit chance
    const hitChance = calculateHitChance(attacker, defender, move, timingScore);
    
    // Determine if the attack hits
    const hits = doesAttackHit(hitChance);
    
    let damage = 0;
    if (hits) {
      // Calculate damage
      damage = calculateDamage(attacker, defender, move, timingScore);
    }
    
    // Log the attack
    logger.info(`Battle turn: ${attacker.name} uses ${move.name} against ${defender.name}. Hit: ${hits}. Damage: ${damage}. Timing: ${timingScore}`);
    
    // Track the turn (detailed battle turn tracking would be implemented here)
    // For now, we'll just return the result
    return {
      success: true,
      attacker: {
        id: attacker.id,
        name: attacker.name,
        level: attacker.level
      },
      defender: {
        id: defender.id,
        name: defender.name,
        level: defender.level
      },
      move: {
        id: move.id,
        name: move.name,
        type: move.type
      },
      hitChance,
      hits,
      damage,
      timingScore
    };
  } catch (error) {
    logger.error('Error processing battle turn', { 
      error: error.message, 
      stack: error.stack,
      battleId,
      attackerHeroId,
      defenderHeroId,
      moveId 
    });
    return { success: false, error: error.message };
  }
};

// Start a new battle
const startBattle = async (hero1Id, hero2Id) => {
  try {
    // Check if both heroes exist
    const [hero1, hero2] = await Promise.all([
      prisma.hero.findUnique({ where: { id: hero1Id } }),
      prisma.hero.findUnique({ where: { id: hero2Id } })
    ]);
    
    if (!hero1 || !hero2) {
      return { 
        success: false, 
        error: !hero1 ? 'Hero 1 not found' : 'Hero 2 not found' 
      };
    }
    
    // Use energy for both heroes
    const [energyResult1, energyResult2] = await Promise.all([
      gameService.useEnergy(hero1Id),
      gameService.useEnergy(hero2Id)
    ]);
    
    if (!energyResult1.success || !energyResult2.success) {
      return { 
        success: false, 
        error: !energyResult1.success ? energyResult1.error : energyResult2.error 
      };
    }
    
    // Calculate max health for both heroes
    const hero1MaxHealth = gameService.getMaxHealth(hero1.level);
    const hero2MaxHealth = gameService.getMaxHealth(hero2.level);
    
    // Create a battle record (we would implement detailed battle tracking here)
    // For now, we'll just return initial battle state
    logger.info(`Starting battle between ${hero1.name} (Lv ${hero1.level}) and ${hero2.name} (Lv ${hero2.level})`);
    
    return {
      success: true,
      battleId: 'battle_' + Date.now(), // Placeholder for a real battle ID
      heroes: [
        {
          id: hero1.id,
          name: hero1.name,
          level: hero1.level,
          maxHealth: hero1MaxHealth,
          currentHealth: hero1MaxHealth
        },
        {
          id: hero2.id,
          name: hero2.name,
          level: hero2.level,
          maxHealth: hero2MaxHealth,
          currentHealth: hero2MaxHealth
        }
      ],
      currentTurn: 1,
      maxTurns: MAX_TURNS,
      status: 'ACTIVE'
    };
  } catch (error) {
    logger.error('Error starting battle', { 
      error: error.message, 
      stack: error.stack,
      hero1Id,
      hero2Id
    });
    return { success: false, error: error.message };
  }
};

// End a battle and update stats
const endBattle = async (battleId, winnerId, loserId, isDraw = false) => {
  try {
    // Update hero stats
    if (!isDraw) {
      await Promise.all([
        // Update winner
        prisma.hero.update({
          where: { id: winnerId },
          data: { wins: { increment: 1 } }
        }),
        // Update loser
        prisma.hero.update({
          where: { id: loserId },
          data: { losses: { increment: 1 } }
        })
      ]);
      
      // Award XP to winner
      await gameService.awardXP(winnerId, 50);
      
      // Award smaller consolation XP to loser
      await gameService.awardXP(loserId, 15);
      
      // Update quest progress for winner
      await gameService.updateQuestProgress(winnerId, 'WIN');
      
      // Update participation quest progress for both
      await Promise.all([
        gameService.updateQuestProgress(winnerId, 'BATTLE'),
        gameService.updateQuestProgress(loserId, 'BATTLE')
      ]);
      
      logger.info(`Battle ${battleId} ended. Winner: ${winnerId}. Loser: ${loserId}.`);
    } else {
      // Update draw stats for both heroes
      await Promise.all([
        prisma.hero.update({
          where: { id: winnerId },
          data: { draws: { increment: 1 } }
        }),
        prisma.hero.update({
          where: { id: loserId },
          data: { draws: { increment: 1 } }
        })
      ]);
      
      // Award small XP to both for the draw
      await Promise.all([
        gameService.awardXP(winnerId, 25),
        gameService.awardXP(loserId, 25)
      ]);
      
      // Update participation quest progress for both
      await Promise.all([
        gameService.updateQuestProgress(winnerId, 'BATTLE'),
        gameService.updateQuestProgress(loserId, 'BATTLE')
      ]);
      
      logger.info(`Battle ${battleId} ended in a draw between ${winnerId} and ${loserId}.`);
    }
    
    return { success: true, isDraw };
  } catch (error) {
    logger.error('Error ending battle', { 
      error: error.message, 
      stack: error.stack,
      battleId,
      winnerId,
      loserId,
      isDraw
    });
    return { success: false, error: error.message };
  }
};

// Get available moves for a hero
const getAvailableMoves = async (heroId) => {
  try {
    // Get hero level
    const hero = await prisma.hero.findUnique({ where: { id: heroId } });
    
    if (!hero) {
      return { success: false, error: 'Hero not found' };
    }
    
    // Get all moves available at this level
    const moves = await prisma.battleMove.findMany({
      where: {
        unlockLevel: { lte: hero.level }
      },
      orderBy: {
        unlockLevel: 'asc'
      }
    });
    
    return { success: true, moves };
  } catch (error) {
    logger.error('Error getting available moves', { 
      error: error.message, 
      stack: error.stack,
      heroId
    });
    return { success: false, error: error.message };
  }
};

class BattleService {
  // ===== VENUE BATTLE SYSTEM =====
  
  async startVenueBattle(heroId, venueId, danceStyle) {
    try {
      const hero = await prisma.hero.findUnique({ where: { id: heroId } });
      const venue = await prisma.danceVenue.findUnique({ where: { id: venueId } });

      if (!hero || !venue) {
        throw new Error('Hero or venue not found');
      }

      // Check venue requirements
      if (hero.level < venue.unlockLevel) {
        throw new Error(`Requires level ${venue.unlockLevel} to enter this venue`);
      }

      if (hero.energy < 2) {
        throw new Error('Not enough energy for venue battle (requires 2 energy)');
      }

      // Check if venue is discovered
      const discovery = await prisma.heroLocationDiscovery.findUnique({
        where: {
          heroId_venueId: {
            heroId,
            venueId
          }
        }
      });

      if (venue.isHidden && !discovery) {
        throw new Error('This venue has not been discovered yet');
      }

      // Create venue battle
      const battle = await prisma.battle.create({
        data: {
          hero1Id: heroId,
          venueId,
          danceStyle,
          battleType: 'SINGLE_PLAYER',
          status: 'ACTIVE',
          hero1Health: hero.health,
          hero1Mana: hero.mana,
          hero2Health: 100 + (venue.difficulty * 10), // AI opponent health based on venue difficulty
          hero2Mana: 50 + (venue.difficulty * 5),
          maxTurns: 10 + venue.difficulty
        }
      });

      // Deduct energy
      await prisma.hero.update({
        where: { id: heroId },
        data: { 
          energy: hero.energy - 2,
          totalBattles: hero.totalBattles + 1
        }
      });

      // Record venue discovery if first time
      if (!discovery) {
        await prisma.heroLocationDiscovery.create({
          data: {
            heroId,
            venueId,
            firstVisit: true,
            visitCount: 1
          }
        });
      } else {
        await prisma.heroLocationDiscovery.update({
          where: { id: discovery.id },
          data: {
            visitCount: discovery.visitCount + 1,
            firstVisit: false
          }
        });
      }

      logger.info(`Started venue battle: Hero ${heroId} at venue ${venue.name}`);
      return { battle, venue };
    } catch (error) {
      logger.error('Error starting venue battle:', error);
      throw error;
    }
  }

  // ===== COMBO BATTLE SYSTEM =====
  
  async executeCombo(battleId, heroId, comboId, bodySequence, timingAccuracy) {
    try {
      const battle = await prisma.battle.findUnique({
        where: { id: battleId },
        include: { venue: true }
      });

      const combo = await prisma.battleCombo.findUnique({
        where: { id: comboId }
      });

      if (!battle || !combo) {
        throw new Error('Battle or combo not found');
      }

      if (battle.status !== 'ACTIVE') {
        throw new Error('Battle is not active');
      }

      const hero = await prisma.hero.findUnique({ where: { id: heroId } });
      if (!hero) {
        throw new Error('Hero not found');
      }

      // Check if hero has enough energy and mana
      if (hero.energy < combo.energyCost) {
        throw new Error('Not enough energy for this combo');
      }

      if (battle.hero1Mana < combo.manaCost) {
        throw new Error('Not enough mana for this combo');
      }

      // Validate body sequence
      const expectedSequence = combo.bodySequence;
      const actualSequence = bodySequence.join('+');
      const sequenceAccuracy = this.calculateSequenceAccuracy(expectedSequence, actualSequence);

      // Calculate combo effectiveness
      const effectiveness = this.calculateComboEffectiveness(
        combo, 
        hero, 
        battle.venue, 
        timingAccuracy, 
        sequenceAccuracy
      );

      // Calculate damage
      const baseDamage = combo.damage;
      const finalDamage = Math.floor(baseDamage * effectiveness.damageMultiplier);

      // Apply damage and costs
      const newOpponentHealth = Math.max(0, battle.hero2Health - finalDamage);
      const newHeroMana = Math.max(0, battle.hero1Mana - combo.manaCost);

      // Record move usage
      await prisma.battleMoveUsage.create({
        data: {
          battleId,
          heroId,
          comboId,
          timingScore: timingAccuracy,
          damage: finalDamage,
          turn: battle.currentTurn,
          bodySequence: actualSequence,
          beatAccuracy: sequenceAccuracy
        }
      });

      // Update combo mastery
      await this.updateComboMastery(heroId, comboId, timingAccuracy, sequenceAccuracy);

      // Check for battle end
      let battleStatus = 'ACTIVE';
      let winnerId = null;
      let rewards = null;

      if (newOpponentHealth <= 0) {
        battleStatus = 'COMPLETED';
        winnerId = heroId;
        rewards = await this.calculateVenueBattleRewards(hero, battle.venue, effectiveness);
        
        // Apply rewards
        await this.applyBattleRewards(heroId, rewards);
      } else if (battle.currentTurn >= battle.maxTurns) {
        battleStatus = 'COMPLETED';
        // Determine winner by remaining health percentage
        const heroHealthPercent = battle.hero1Health / hero.health;
        const opponentHealthPercent = newOpponentHealth / battle.hero2Health;
        winnerId = heroHealthPercent > opponentHealthPercent ? heroId : null;
        
        if (winnerId === heroId) {
          rewards = await this.calculateVenueBattleRewards(hero, battle.venue, effectiveness);
          await this.applyBattleRewards(heroId, rewards);
        }
      }

      // Update battle
      const updatedBattle = await prisma.battle.update({
        where: { id: battleId },
        data: {
          hero1Mana: newHeroMana,
          hero2Health: newOpponentHealth,
          currentTurn: battle.currentTurn + 1,
          status: battleStatus,
          winnerId,
          endedAt: battleStatus === 'COMPLETED' ? new Date() : undefined
        }
      });

      // Deduct energy from hero
      await prisma.hero.update({
        where: { id: heroId },
        data: { energy: hero.energy - combo.energyCost }
      });

      logger.info(`Combo executed: ${combo.name} by hero ${heroId}, damage: ${finalDamage}`);
      
      return {
        battle: updatedBattle,
        comboResult: {
          damage: finalDamage,
          effectiveness: effectiveness.rating,
          timingAccuracy,
          sequenceAccuracy,
          battleEnded: battleStatus === 'COMPLETED',
          winner: winnerId,
          rewards
        }
      };
    } catch (error) {
      logger.error('Error executing combo:', error);
      throw error;
    }
  }

  // ===== CALCULATION METHODS =====
  
  calculateSequenceAccuracy(expected, actual) {
    if (expected === actual) return 100;
    
    const expectedParts = expected.split('+');
    const actualParts = actual.split('+');
    
    if (expectedParts.length !== actualParts.length) {
      return Math.max(0, 100 - (Math.abs(expectedParts.length - actualParts.length) * 20));
    }
    
    let correctParts = 0;
    for (let i = 0; i < expectedParts.length; i++) {
      if (expectedParts[i] === actualParts[i]) {
        correctParts++;
      }
    }
    
    return (correctParts / expectedParts.length) * 100;
  }

  calculateComboEffectiveness(combo, hero, venue, timingAccuracy, sequenceAccuracy) {
    let damageMultiplier = 1.0;
    let rating = 'NORMAL';

    // Timing accuracy impact (0-100)
    const timingBonus = timingAccuracy / 100;
    damageMultiplier += timingBonus * 0.5; // Up to 50% bonus

    // Sequence accuracy impact
    const sequenceBonus = sequenceAccuracy / 100;
    damageMultiplier += sequenceBonus * 0.3; // Up to 30% bonus

    // Dance style compatibility
    if (venue && venue.specialties.includes(combo.danceStyle)) {
      damageMultiplier += 0.25; // 25% bonus for venue specialty
    }

    // Hero style compatibility
    if (combo.danceStyle === hero.primaryStyle) {
      damageMultiplier += 0.2; // 20% bonus for primary style
    }

    // Venue difficulty bonus (higher difficulty = higher rewards)
    if (venue) {
      damageMultiplier += (venue.difficulty / 10) * 0.1; // Up to 10% bonus
    }

    // Perfect execution bonuses
    if (timingAccuracy >= 95 && sequenceAccuracy >= 95) {
      damageMultiplier += 0.5; // 50% bonus for perfect execution
      rating = 'PERFECT';
    } else if (timingAccuracy >= 90 && sequenceAccuracy >= 90) {
      damageMultiplier += 0.25; // 25% bonus for excellent execution
      rating = 'EXCELLENT';
    } else if (timingAccuracy >= 80 && sequenceAccuracy >= 80) {
      rating = 'GOOD';
    }

    // Critical hit chance based on hero luck
    const criticalChance = hero.luck / 100;
    if (Math.random() < criticalChance) {
      damageMultiplier += 0.5; // 50% critical hit bonus
      rating = rating === 'PERFECT' ? 'PERFECT_CRITICAL' : 'CRITICAL';
    }

    return { damageMultiplier, rating };
  }

  async calculateVenueBattleRewards(hero, venue, effectiveness) {
    const baseXp = venue.rewards?.bonusXp || 50;
    const baseCoins = venue.rewards?.coins || 25;
    
    // Apply effectiveness multiplier
    const effectivenessMultiplier = effectiveness.damageMultiplier;
    
    const rewards = {
      xp: Math.floor(baseXp * effectivenessMultiplier),
      coins: Math.floor(baseCoins * effectivenessMultiplier),
      skillPoints: venue.difficulty >= 7 ? 1 : 0,
      traitPoints: venue.difficulty >= 9 ? 1 : 0,
      specialRewards: []
    };

    // Venue-specific rewards
    if (venue.rewards?.specialMoves) {
      rewards.specialRewards.push(...venue.rewards.specialMoves);
    }

    // First-time venue completion bonus
    const previousCompletion = await prisma.battle.findFirst({
      where: {
        hero1Id: hero.id,
        venueId: venue.id,
        status: 'COMPLETED',
        winnerId: hero.id
      }
    });

    if (!previousCompletion) {
      rewards.xp *= 2; // Double XP for first completion
      rewards.coins *= 2; // Double coins for first completion
      rewards.specialRewards.push('First Victory at ' + venue.name);
    }

    return rewards;
  }

  async applyBattleRewards(heroId, rewards) {
    const hero = await prisma.hero.findUnique({ where: { id: heroId } });
    
    const updates = {
      experience: hero.experience + rewards.xp,
      wins: hero.wins + 1,
      winStreak: hero.winStreak + 1,
      bestWinStreak: Math.max(hero.bestWinStreak, hero.winStreak + 1)
    };

    if (rewards.skillPoints > 0) {
      updates.skillPoints = hero.skillPoints + rewards.skillPoints;
    }

    if (rewards.traitPoints > 0) {
      updates.traitPoints = hero.traitPoints + rewards.traitPoints;
    }

    await prisma.hero.update({
      where: { id: heroId },
      data: updates
    });

    // Add coins to user credits
    if (rewards.coins > 0) {
      await prisma.user.update({
        where: { id: hero.userId },
        data: { credits: { increment: rewards.coins } }
      });
    }

    logger.info(`Applied battle rewards to hero ${heroId}: XP:${rewards.xp}, Coins:${rewards.coins}`);
  }

  async updateComboMastery(heroId, comboId, timingAccuracy, sequenceAccuracy) {
    const mastery = await prisma.heroComboMastery.findUnique({
      where: {
        heroId_comboId: {
          heroId,
          comboId
        }
      }
    });

    const isPerfectHit = timingAccuracy >= 95 && sequenceAccuracy >= 95;

    if (!mastery) {
      await prisma.heroComboMastery.create({
        data: {
          heroId,
          comboId,
          timesUsed: 1,
          perfectHits: isPerfectHit ? 1 : 0,
          masteryLevel: 1,
          lastUsedAt: new Date()
        }
      });
    } else {
      const newTimesUsed = mastery.timesUsed + 1;
      const newPerfectHits = mastery.perfectHits + (isPerfectHit ? 1 : 0);
      let newMasteryLevel = mastery.masteryLevel;

      // Increase mastery level based on usage and perfect hits
      const perfectHitRatio = newPerfectHits / newTimesUsed;
      if (perfectHitRatio >= 0.8 && newTimesUsed >= newMasteryLevel * 20) {
        newMasteryLevel = Math.min(5, newMasteryLevel + 1);
      }

      await prisma.heroComboMastery.update({
        where: { id: mastery.id },
        data: {
          timesUsed: newTimesUsed,
          perfectHits: newPerfectHits,
          masteryLevel: newMasteryLevel,
          lastUsedAt: new Date()
        }
      });
    }
  }

  // ===== AVAILABLE MOVES =====
  
  async getAvailableMoves(heroId) {
    try {
      const hero = await prisma.hero.findUnique({ where: { id: heroId } });
      
      if (!hero) {
        throw new Error('Hero not found');
      }

      // Get basic battle moves
      const battleMoves = await prisma.battleMove.findMany({
        where: {
          unlockLevel: { lte: hero.level }
        },
        orderBy: [
          { unlockLevel: 'asc' },
          { power: 'desc' }
        ]
      });

      // Get available combos
      const combos = await prisma.battleCombo.findMany({
        where: {
          unlockLevel: { lte: hero.level }
        },
        include: {
          heroComboMastery: {
            where: { heroId },
            take: 1
          }
        },
        orderBy: [
          { unlockLevel: 'asc' },
          { damage: 'desc' }
        ]
      });

      const formattedCombos = combos.map(combo => ({
        ...combo,
        sequence: combo.bodySequence.split('+'),
        power: combo.damage,
        masteryLevel: combo.heroComboMastery[0]?.masteryLevel || 0,
        timesUsed: combo.heroComboMastery[0]?.timesUsed || 0
      }));

      return {
        success: true,
        battleMoves,
        combos: formattedCombos
      };
    } catch (error) {
      logger.error('Error getting available moves:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // ===== VENUE DISCOVERY =====
  
  async discoverVenue(heroId, venueId) {
    try {
      const hero = await prisma.hero.findUnique({ where: { id: heroId } });
      const venue = await prisma.danceVenue.findUnique({ where: { id: venueId } });

      if (!hero || !venue) {
        throw new Error('Hero or venue not found');
      }

      if (hero.level < venue.unlockLevel) {
        throw new Error(`Requires level ${venue.unlockLevel} to discover this venue`);
      }

      // Check if already discovered
      const existing = await prisma.heroLocationDiscovery.findUnique({
        where: {
          heroId_venueId: {
            heroId,
            venueId
          }
        }
      });

      if (existing) {
        return { success: false, message: 'Venue already discovered' };
      }

      // Create discovery record
      await prisma.heroLocationDiscovery.create({
        data: {
          heroId,
          venueId,
          firstVisit: true,
          visitCount: 0
        }
      });

      // Award discovery XP
      const discoveryXp = venue.difficulty * 10;
      await prisma.hero.update({
        where: { id: heroId },
        data: {
          experience: hero.experience + discoveryXp
        }
      });

      logger.info(`Hero ${heroId} discovered venue ${venue.name}`);
      
      return {
        success: true,
        venue,
        xpReward: discoveryXp
      };
    } catch (error) {
      logger.error('Error discovering venue:', error);
      throw error;
    }
  }
}

module.exports = new BattleService(); 