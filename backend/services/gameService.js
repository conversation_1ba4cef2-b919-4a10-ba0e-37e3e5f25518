const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');

const prisma = new PrismaClient();

class GameService {
  // ===== HERO MANAGEMENT =====
  
  async getOrCreateHero(userId) {
    try {
      let hero = await prisma.hero.findUnique({
        where: { userId },
        include: {
          user: true,
          equipment: true,
          questProgress: {
            include: { quest: true }
          }
        }
      });

      if (!hero) {
        // Create new hero with default stats
        hero = await prisma.hero.create({
          data: {
            userId,
            name: `Hero_${Date.now()}`,
            level: 1,
            experience: 0,
            energy: 10,
            health: 100,
            mana: 50,
            attack: 10,
            defense: 10,
            speed: 10,
            luck: 10,
            primaryStyle: 'SALSA',
            stylePoints: { SALSA: 10 },
            skills: { TIMING: 5, FOOTWORK: 5 },
            equippedItems: {},
            inventory: [],
            traits: ['ON2_TIMING'],
            traitPoints: 3,
            skillPoints: 5
          },
          include: {
            user: true,
            equipment: true,
            questProgress: {
              include: { quest: true }
            }
          }
        });

        logger.info(`Created new hero for user ${userId}: ${hero.name}`);
      }

      return hero;
    } catch (error) {
      logger.error('Error getting/creating hero:', error);
      throw error;
    }
  }

  async updateHeroStats(heroId, updates) {
    try {
      const hero = await prisma.hero.update({
        where: { id: heroId },
        data: {
          ...updates,
          updatedAt: new Date(),
          lastActive: new Date()
        },
        include: {
          user: true,
          equipment: true
        }
      });

      logger.info(`Updated hero ${heroId} stats:`, updates);
      return hero;
    } catch (error) {
      logger.error('Error updating hero stats:', error);
      throw error;
    }
  }

  // ===== BATTLE SYSTEM =====

  async startAIBattle(heroId, aiOpponentId, danceStyle) {
    try {
      const hero = await prisma.hero.findUnique({ where: { id: heroId } });
      const aiOpponent = await prisma.hero.findUnique({ where: { id: aiOpponentId } });

      if (!hero || !aiOpponent) {
        throw new Error('Hero or AI opponent not found');
      }

      if (hero.energy < 1) {
        throw new Error('Not enough energy to battle');
      }

      // Create battle record
      const battle = await prisma.playerBattle.create({
        data: {
          battleType: 'AI_BATTLE',
          status: 'IN_PROGRESS',
          player1Id: heroId,
          player2Id: aiOpponentId,
          danceStyle,
          maxRounds: 5,
          currentRound: 1,
          battleData: {
            player1Health: hero.health,
            player2Health: aiOpponent.health,
            player1Mana: hero.mana,
            player2Mana: aiOpponent.mana,
            currentTurn: 'player1'
          },
          moves: []
        }
      });

      // Deduct energy
      await this.updateHeroStats(heroId, { 
        energy: hero.energy - 1,
        totalBattles: hero.totalBattles + 1
      });

      logger.info(`Started AI battle: ${heroId} vs ${aiOpponentId} in ${danceStyle}`);
      return battle;
    } catch (error) {
      logger.error('Error starting AI battle:', error);
      throw error;
    }
  }

  async executeBattleMove(battleId, heroId, moveId, timingScore = 0) {
    try {
      const battle = await prisma.playerBattle.findUnique({
        where: { id: battleId },
        include: {
          player1: true,
          player2: true
        }
      });

      if (!battle || battle.status !== 'IN_PROGRESS') {
        throw new Error('Battle not found or not in progress');
      }

      const move = await prisma.battleMove.findUnique({
        where: { id: moveId }
      });

      if (!move) {
        throw new Error('Battle move not found');
      }

      const isPlayer1 = battle.player1Id === heroId;
      const currentPlayer = isPlayer1 ? battle.player1 : battle.player2;
      const opponent = isPlayer1 ? battle.player2 : battle.player1;

      // Calculate move effectiveness
      const effectiveness = this.calculateMoveEffectiveness(move, currentPlayer, opponent, timingScore);
      
      // Apply move effects
      const battleData = battle.battleData;
      const damage = Math.floor(move.power * effectiveness.damageMultiplier);
      
      if (isPlayer1) {
        battleData.player2Health = Math.max(0, battleData.player2Health - damage);
        battleData.player1Mana = Math.max(0, battleData.player1Mana - move.manaCost);
      } else {
        battleData.player1Health = Math.max(0, battleData.player1Health - damage);
        battleData.player2Mana = Math.max(0, battleData.player2Mana - move.manaCost);
      }

      // Record move
      const moveRecord = {
        round: battle.currentRound,
        playerId: heroId,
        moveId,
        moveName: move.name,
        damage,
        timingScore,
        effectiveness: effectiveness.rating,
        timestamp: new Date().toISOString()
      };

      const updatedMoves = [...battle.moves, moveRecord];

      // Check for battle end
      let battleStatus = 'IN_PROGRESS';
      let winnerId = null;
      let xpReward = 0;
      let coinReward = 0;

      if (battleData.player1Health <= 0 || battleData.player2Health <= 0) {
        battleStatus = 'COMPLETED';
        winnerId = battleData.player1Health > 0 ? battle.player1Id : battle.player2Id;
        
        // Calculate rewards
        if (winnerId === heroId) {
          xpReward = 50 + (opponent.level * 10);
          coinReward = 25 + (opponent.level * 5);
          
          // Update hero stats
          await this.updateHeroStats(heroId, {
            wins: currentPlayer.wins + 1,
            winStreak: currentPlayer.winStreak + 1,
            bestWinStreak: Math.max(currentPlayer.bestWinStreak, currentPlayer.winStreak + 1),
            experience: currentPlayer.experience + xpReward
          });

          // Update user credits
          await prisma.user.update({
            where: { id: currentPlayer.userId },
            data: { credits: { increment: coinReward } }
          });
        } else {
          await this.updateHeroStats(heroId, {
            losses: currentPlayer.losses + 1,
            winStreak: 0
          });
        }

        // Create battle history record
        await prisma.battleHistory.create({
          data: {
            heroId,
            opponentId: opponent.id,
            result: winnerId === heroId ? 'WIN' : 'LOSS',
            turnCount: updatedMoves.length,
            finalScore: damage
          }
        });
      }

      // Update battle
      const updatedBattle = await prisma.playerBattle.update({
        where: { id: battleId },
        data: {
          battleData,
          moves: updatedMoves,
          status: battleStatus,
          winnerId,
          xpReward,
          coinReward,
          lastMoveAt: new Date(),
          ...(battleStatus === 'COMPLETED' && { endedAt: new Date() })
        }
      });

      logger.info(`Battle move executed: ${move.name} by ${heroId}, damage: ${damage}`);
      
      return {
        battle: updatedBattle,
        moveResult: {
          damage,
          effectiveness: effectiveness.rating,
          timingScore,
          battleEnded: battleStatus === 'COMPLETED',
          winner: winnerId,
          rewards: { xp: xpReward, coins: coinReward }
        }
      };
    } catch (error) {
      logger.error('Error executing battle move:', error);
      throw error;
    }
  }

  calculateMoveEffectiveness(move, attacker, defender, timingScore) {
    let damageMultiplier = 1.0;
    let rating = 'NORMAL';

    // Timing score impact (0-100)
    const timingBonus = timingScore / 100;
    damageMultiplier += timingBonus * 0.5; // Up to 50% bonus for perfect timing

    // Style compatibility
    if (move.danceStyle === attacker.primaryStyle) {
      damageMultiplier += 0.2; // 20% bonus for primary style
    }

    // Level difference
    const levelDiff = attacker.level - defender.level;
    damageMultiplier += levelDiff * 0.05; // 5% per level difference

    // Luck factor
    const luckRoll = Math.random() * 100;
    if (luckRoll < attacker.luck) {
      damageMultiplier += 0.3; // Critical hit
      rating = 'CRITICAL';
    }

    // Accuracy check
    const accuracyRoll = Math.random() * 100;
    if (accuracyRoll > move.accuracy) {
      damageMultiplier = 0.1; // Miss
      rating = 'MISS';
    }

    // Perfect timing bonus
    if (timingScore >= 95) {
      damageMultiplier += 0.25;
      rating = 'PERFECT';
    }

    return { damageMultiplier, rating };
  }

  // ===== EQUIPMENT SYSTEM =====

  async getAvailableEquipment(heroLevel = 1) {
    try {
      const equipment = await prisma.equipment.findMany({
        where: {
          isForSale: true,
          heroId: null // Not owned by anyone
        },
        orderBy: [
          { rarity: 'asc' },
          { price: 'asc' }
        ]
      });

      return equipment;
    } catch (error) {
      logger.error('Error getting available equipment:', error);
      throw error;
    }
  }

  async purchaseEquipment(heroId, equipmentId) {
    try {
      const hero = await prisma.hero.findUnique({
        where: { id: heroId },
        include: { user: true }
      });

      const equipment = await prisma.equipment.findUnique({
        where: { id: equipmentId }
      });

      if (!hero || !equipment) {
        throw new Error('Hero or equipment not found');
      }

      if (equipment.heroId) {
        throw new Error('Equipment already owned');
      }

      if ((hero.coins || 0) < equipment.price) {
        throw new Error('Insufficient coins');
      }

      // Transfer equipment and deduct coins
      await prisma.$transaction([
        prisma.equipment.update({
          where: { id: equipmentId },
          data: { 
            heroId,
            isForSale: false
          }
        }),
        prisma.hero.update({
          where: { id: heroId },
          data: { coins: { decrement: equipment.price } }
        })
      ]);

      // Add to hero inventory
      const currentInventory = Array.isArray(hero.inventory) ? hero.inventory : [];
      await this.updateHeroStats(heroId, {
        inventory: [...currentInventory, equipmentId]
      });

      logger.info(`Hero ${heroId} purchased equipment ${equipment.name} for ${equipment.price} coins`);
      return { success: true, equipment };
    } catch (error) {
      logger.error('Error purchasing equipment:', error);
      throw error;
    }
  }

  async equipItem(heroId, equipmentId) {
    try {
      const hero = await prisma.hero.findUnique({
        where: { id: heroId },
        include: { equipment: true }
      });

      const equipment = await prisma.equipment.findUnique({
        where: { id: equipmentId }
      });

      if (!hero || !equipment || equipment.heroId !== heroId) {
        throw new Error('Hero or equipment not found, or equipment not owned');
      }

      const currentEquipped = hero.equippedItems || {};
      const equipmentType = equipment.type;

      // Unequip current item of same type if exists
      if (currentEquipped[equipmentType]) {
        // Move current item back to inventory
        const currentInventory = Array.isArray(hero.inventory) ? hero.inventory : [];
        currentInventory.push(currentEquipped[equipmentType]);
        currentEquipped[equipmentType] = equipmentId;
        
        // Remove from inventory
        const newInventory = currentInventory.filter(id => id !== equipmentId);
        
        await this.updateHeroStats(heroId, {
          equippedItems: currentEquipped,
          inventory: newInventory
        });
      } else {
        // Just equip the item
        currentEquipped[equipmentType] = equipmentId;
        const currentInventory = Array.isArray(hero.inventory) ? hero.inventory : [];
        const newInventory = currentInventory.filter(id => id !== equipmentId);
        
        await this.updateHeroStats(heroId, {
          equippedItems: currentEquipped,
          inventory: newInventory
        });
      }

      logger.info(`Hero ${heroId} equipped ${equipment.name}`);
      return { success: true, equippedItem: equipment };
    } catch (error) {
      logger.error('Error equipping item:', error);
      throw error;
    }
  }

  // ===== QUEST SYSTEM =====

  async getActiveQuests(heroId) {
    try {
      const hero = await prisma.hero.findUnique({
        where: { id: heroId }
      });

      if (!hero) {
        throw new Error('Hero not found');
      }

      // Get all active quests that hero can do
      const quests = await prisma.quest.findMany({
        where: {
          isActive: true,
          minLevel: { lte: hero.level },
          OR: [
            { maxLevel: null },
            { maxLevel: { gte: hero.level } }
          ]
        },
        include: {
          heroQuests: {
            where: { heroId },
            take: 1
          }
        }
      });

      // Initialize quest progress for new quests
      for (const quest of quests) {
        if (quest.heroQuests.length === 0) {
          await prisma.questProgress.create({
            data: {
              heroId,
              questId: quest.id,
              progress: 0,
              completed: false
            }
          });
        }
      }

      // Fetch updated quest data with progress
      const questsWithProgress = await prisma.quest.findMany({
        where: {
          isActive: true,
          minLevel: { lte: hero.level },
          OR: [
            { maxLevel: null },
            { maxLevel: { gte: hero.level } }
          ]
        },
        include: {
          heroQuests: {
            where: { heroId }
          }
        }
      });

      return questsWithProgress;
    } catch (error) {
      logger.error('Error getting active quests:', error);
      throw error;
    }
  }

  async updateQuestProgress(heroId, questType, action, amount = 1) {
    try {
      const quests = await prisma.quest.findMany({
        where: {
          isActive: true,
          category: questType
        },
        include: {
          heroQuests: {
            where: { heroId }
          }
        }
      });

      const completedQuests = [];

      for (const quest of quests) {
        if (quest.heroQuests.length === 0) continue;
        
        const progress = quest.heroQuests[0];
        if (progress.completed) continue;

        // Check if action matches quest requirements
        const requirements = quest.requirements;
        let shouldUpdate = false;
        let newProgress = progress.progress;

        // Match action to quest requirements
        if (requirements[action]) {
          newProgress = Math.min(progress.progress + amount, quest.targetValue);
          shouldUpdate = true;
        }

        if (shouldUpdate) {
          const isCompleted = newProgress >= quest.targetValue;
          
          await prisma.questProgress.update({
            where: {
              heroId_questId: {
                heroId,
                questId: quest.id
              }
            },
            data: {
              progress: newProgress,
              completed: isCompleted,
              ...(isCompleted && { claimedAt: new Date() })
            }
          });

          if (isCompleted) {
            // Award quest rewards
            await this.awardQuestRewards(heroId, quest);
            completedQuests.push(quest);
            logger.info(`Quest completed: ${quest.title} by hero ${heroId}`);
          }
        }
      }

      return completedQuests;
    } catch (error) {
      logger.error('Error updating quest progress:', error);
      throw error;
    }
  }

  async awardQuestRewards(heroId, quest) {
    try {
      const hero = await prisma.hero.findUnique({
        where: { id: heroId },
        include: { user: true }
      });

      if (!hero) return;

      const updates = {};
      
      // XP reward
      if (quest.rewardXp > 0) {
        updates.experience = hero.experience + quest.rewardXp;
      }

      // Skill points
      if (quest.rewardSkillPoints > 0) {
        updates.skillPoints = hero.skillPoints + quest.rewardSkillPoints;
      }

      // Trait points
      if (quest.rewardTraitPoints > 0) {
        updates.traitPoints = hero.traitPoints + quest.rewardTraitPoints;
      }

      // Update hero
      if (Object.keys(updates).length > 0) {
        await this.updateHeroStats(heroId, updates);
      }

      // Coin reward
      if (quest.rewardCoins > 0) {
        await prisma.user.update({
          where: { id: hero.userId },
          data: { credits: { increment: quest.rewardCoins } }
        });
      }

      // Item rewards (create equipment)
      if (quest.specialRewards && quest.specialRewards.length > 0) {
        for (const itemName of quest.specialRewards) {
          // Create quest reward equipment
          await prisma.equipment.create({
            data: {
              name: itemName,
              description: `Reward from quest: ${quest.title}`,
              type: 'CHARM',
              rarity: 'RARE',
              heroId,
              statBonuses: { luck: 5 },
              skillBonuses: {},
              specialEffects: ['quest_reward']
            }
          });
        }
      }

      logger.info(`Awarded quest rewards for ${quest.title}: XP:${quest.rewardXp}, Coins:${quest.rewardCoins}`);
    } catch (error) {
      logger.error('Error awarding quest rewards:', error);
      throw error;
    }
  }

  // ===== ENERGY SYSTEM =====

  async regenerateEnergy() {
    try {
      const heroes = await prisma.hero.findMany({
        where: {
          energy: { lt: 10 } // Max energy is 10
        }
      });

      for (const hero of heroes) {
        const now = new Date();
        const lastRefill = new Date(hero.lastEnergyRefill);
        const hoursSinceRefill = (now - lastRefill) / (1000 * 60 * 60);
        
        if (hoursSinceRefill >= 1) { // 1 energy per hour
          const energyToAdd = Math.floor(hoursSinceRefill);
          const newEnergy = Math.min(10, hero.energy + energyToAdd);
          
          await prisma.hero.update({
            where: { id: hero.id },
            data: {
              energy: newEnergy,
              lastEnergyRefill: now
            }
          });
        }
      }

      logger.info(`Regenerated energy for ${heroes.length} heroes`);
    } catch (error) {
      logger.error('Error regenerating energy:', error);
    }
  }

  // ===== LEVEL SYSTEM =====

  async checkLevelUp(heroId) {
    try {
      const hero = await prisma.hero.findUnique({
        where: { id: heroId }
      });

      if (!hero) return null;

      const requiredXP = this.getRequiredXPForLevel(hero.level + 1);
      
      if (hero.experience >= requiredXP) {
        const newLevel = hero.level + 1;
        const updates = {
          level: newLevel,
          skillPoints: hero.skillPoints + 3, // 3 skill points per level
          traitPoints: hero.traitPoints + 1,  // 1 trait point per level
          health: hero.health + 10,           // +10 health per level
          mana: hero.mana + 5                 // +5 mana per level
        };

        await this.updateHeroStats(heroId, updates);
        
        logger.info(`Hero ${heroId} leveled up to level ${newLevel}!`);
        return { newLevel, rewards: updates };
      }

      return null;
    } catch (error) {
      logger.error('Error checking level up:', error);
      throw error;
    }
  }

  getRequiredXPForLevel(level) {
    // Exponential XP curve: level^2 * 100
    return Math.pow(level, 2) * 100;
  }

  // ===== UTILITY METHODS =====

  async getHeroStats(heroId) {
    try {
      const hero = await prisma.hero.findUnique({
        where: { id: heroId },
        include: {
          user: true,
          equipment: true,
          questProgress: {
            include: { quest: true },
            where: { completed: false }
          },
          battleHistory: {
            orderBy: { createdAt: 'desc' },
            take: 10
          }
        }
      });

      if (!hero) {
        throw new Error('Hero not found');
      }

      // Calculate total stats with equipment bonuses
      const totalStats = this.calculateTotalStats(hero);
      
      return {
        ...hero,
        totalStats,
        nextLevelXP: this.getRequiredXPForLevel(hero.level + 1),
        energyRegenTime: this.getEnergyRegenTime(hero)
      };
    } catch (error) {
      logger.error('Error getting hero stats:', error);
      throw error;
    }
  }

  calculateTotalStats(hero) {
    const baseStats = {
      health: hero.health,
      mana: hero.mana,
      attack: hero.attack,
      defense: hero.defense,
      speed: hero.speed,
      luck: hero.luck
    };

    // Add equipment bonuses
    if (hero.equipment && hero.equipment.length > 0) {
      for (const item of hero.equipment) {
        const bonuses = item.statBonuses || {};
        for (const [stat, bonus] of Object.entries(bonuses)) {
          if (baseStats[stat] !== undefined) {
            baseStats[stat] += bonus;
          }
        }
      }
    }

    return baseStats;
  }

  getEnergyRegenTime(hero) {
    if (hero.energy >= 10) return 0;
    
    const now = new Date();
    const lastRefill = new Date(hero.lastEnergyRefill);
    const nextRefill = new Date(lastRefill.getTime() + (60 * 60 * 1000)); // +1 hour
    
    return Math.max(0, nextRefill - now);
  }
}

module.exports = new GameService();