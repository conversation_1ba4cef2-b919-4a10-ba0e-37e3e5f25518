const cron = require('node-cron');
const gameService = require('./gameService');
const logger = require('../utils/logger');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

class CronService {
  constructor() {
    this.jobs = new Map();
  }

  // Initialize all cron jobs
  init() {
    this.setupEnergyRegeneration();
    this.setupDailyQuestReset();
    this.setupWeeklyQuestReset();
    this.setupMonthlyQuestReset();
    logger.info('Cron service initialized with all scheduled jobs');
  }

  // Energy regeneration every hour
  setupEnergyRegeneration() {
    const job = cron.schedule('0 * * * *', async () => {
      try {
        await gameService.regenerateEnergy();
        logger.info('Energy regeneration completed');
      } catch (error) {
        logger.error('Error in energy regeneration cron job:', error);
      }
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.jobs.set('energyRegen', job);
    job.start();
    logger.info('Energy regeneration cron job scheduled');
  }

  // Daily quest reset at midnight UTC
  setupDailyQuestReset() {
    const job = cron.schedule('0 0 * * *', async () => {
      try {
        await this.resetDailyQuests();
        logger.info('Daily quest reset completed');
      } catch (error) {
        logger.error('Error in daily quest reset cron job:', error);
      }
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.jobs.set('dailyQuestReset', job);
    job.start();
    logger.info('Daily quest reset cron job scheduled');
  }

  // Weekly quest reset on Monday at midnight UTC
  setupWeeklyQuestReset() {
    const job = cron.schedule('0 0 * * 1', async () => {
      try {
        await this.resetWeeklyQuests();
        logger.info('Weekly quest reset completed');
      } catch (error) {
        logger.error('Error in weekly quest reset cron job:', error);
      }
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.jobs.set('weeklyQuestReset', job);
    job.start();
    logger.info('Weekly quest reset cron job scheduled');
  }

  // Monthly quest reset on the 1st at midnight UTC
  setupMonthlyQuestReset() {
    const job = cron.schedule('0 0 1 * *', async () => {
      try {
        await this.resetMonthlyQuests();
        logger.info('Monthly quest reset completed');
      } catch (error) {
        logger.error('Error in monthly quest reset cron job:', error);
      }
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.jobs.set('monthlyQuestReset', job);
    job.start();
    logger.info('Monthly quest reset cron job scheduled');
  }

  // Reset daily quests
  async resetDailyQuests() {
    try {
      // Get all daily quests
      const dailyQuests = await prisma.quest.findMany({
        where: {
          type: 'DAILY',
          isActive: true,
          repeatDaily: true
        }
      });

      if (dailyQuests.length === 0) {
        logger.info('No daily quests found to reset');
        return;
      }

      // Reset progress for all heroes on daily quests
      for (const quest of dailyQuests) {
        await prisma.questProgress.updateMany({
          where: {
            questId: quest.id,
            completed: true
          },
          data: {
            progress: 0,
            completed: false,
            claimedAt: null
          }
        });
      }

      logger.info(`Reset ${dailyQuests.length} daily quests for all heroes`);
    } catch (error) {
      logger.error('Error resetting daily quests:', error);
      throw error;
    }
  }

  // Reset weekly quests
  async resetWeeklyQuests() {
    try {
      // Get all weekly quests
      const weeklyQuests = await prisma.quest.findMany({
        where: {
          type: 'WEEKLY',
          isActive: true,
          repeatWeekly: true
        }
      });

      if (weeklyQuests.length === 0) {
        logger.info('No weekly quests found to reset');
        return;
      }

      // Reset progress for all heroes on weekly quests
      for (const quest of weeklyQuests) {
        await prisma.questProgress.updateMany({
          where: {
            questId: quest.id,
            completed: true
          },
          data: {
            progress: 0,
            completed: false,
            claimedAt: null
          }
        });
      }

      logger.info(`Reset ${weeklyQuests.length} weekly quests for all heroes`);
    } catch (error) {
      logger.error('Error resetting weekly quests:', error);
      throw error;
    }
  }

  // Reset monthly quests
  async resetMonthlyQuests() {
    try {
      // Get all monthly quests
      const monthlyQuests = await prisma.quest.findMany({
        where: {
          type: 'MONTHLY',
          isActive: true
        }
      });

      if (monthlyQuests.length === 0) {
        logger.info('No monthly quests found to reset');
        return;
      }

      // Reset progress for all heroes on monthly quests
      for (const quest of monthlyQuests) {
        await prisma.questProgress.updateMany({
          where: {
            questId: quest.id,
            completed: true
          },
          data: {
            progress: 0,
            completed: false,
            claimedAt: null
          }
        });
      }

      // Update global rankings monthly
      await this.updateGlobalRankings();

      logger.info(`Reset ${monthlyQuests.length} monthly quests for all heroes`);
    } catch (error) {
      logger.error('Error resetting monthly quests:', error);
      throw error;
    }
  }

  // Update global rankings
  async updateGlobalRankings() {
    try {
      // Get all heroes sorted by level and experience
      const heroes = await prisma.hero.findMany({
        where: {
          user: {
            role: 'DANCER' // Exclude AI opponents
          }
        },
        orderBy: [
          { level: 'desc' },
          { experience: 'desc' },
          { wins: 'desc' }
        ]
      });

      // Update global ranks
      for (let i = 0; i < heroes.length; i++) {
        await prisma.hero.update({
          where: { id: heroes[i].id },
          data: { globalRank: i + 1 }
        });
      }

      // Update style-specific rankings
      const danceStyles = ['SALSA', 'BACHATA', 'KIZOMBA', 'ZOUK', 'CHACHA'];
      
      for (const style of danceStyles) {
        const styleHeroes = await prisma.hero.findMany({
          where: {
            user: {
              role: 'DANCER'
            },
            primaryStyle: style
          },
          orderBy: [
            { level: 'desc' },
            { experience: 'desc' },
            { wins: 'desc' }
          ]
        });

        for (let i = 0; i < styleHeroes.length; i++) {
          const hero = styleHeroes[i];
          const currentStyleRanks = hero.styleRanks || {};
          currentStyleRanks[style] = i + 1;

          await prisma.hero.update({
            where: { id: hero.id },
            data: { styleRanks: currentStyleRanks }
          });
        }
      }

      logger.info(`Updated global rankings for ${heroes.length} heroes`);
    } catch (error) {
      logger.error('Error updating global rankings:', error);
      throw error;
    }
  }

  // Stop a specific cron job
  stopJob(jobName) {
    const job = this.jobs.get(jobName);
    if (job) {
      job.stop();
      logger.info(`Stopped cron job: ${jobName}`);
    } else {
      logger.warn(`Cron job not found: ${jobName}`);
    }
  }

  // Stop all cron jobs
  stopAll() {
    for (const [name, job] of this.jobs) {
      job.stop();
      logger.info(`Stopped cron job: ${name}`);
    }
    this.jobs.clear();
    logger.info('All cron jobs stopped');
  }

  // Get status of all jobs
  getStatus() {
    const status = {};
    for (const [name, job] of this.jobs) {
      status[name] = {
        running: job.running || false,
        scheduled: job.scheduled || false
      };
    }
    return status;
  }

  // Manual trigger for testing
  async manualEnergyRegen() {
    try {
      await gameService.regenerateEnergy();
      logger.info('Manual energy regeneration completed');
      return { success: true, message: 'Energy regeneration completed' };
    } catch (error) {
      logger.error('Error in manual energy regeneration:', error);
      return { success: false, error: error.message };
    }
  }

  async manualDailyReset() {
    try {
      await this.resetDailyQuests();
      logger.info('Manual daily quest reset completed');
      return { success: true, message: 'Daily quest reset completed' };
    } catch (error) {
      logger.error('Error in manual daily quest reset:', error);
      return { success: false, error: error.message };
    }
  }

  async manualRankingUpdate() {
    try {
      await this.updateGlobalRankings();
      logger.info('Manual ranking update completed');
      return { success: true, message: 'Global rankings updated' };
    } catch (error) {
      logger.error('Error in manual ranking update:', error);
      return { success: false, error: error.message };
    }
  }
}

module.exports = new CronService(); 