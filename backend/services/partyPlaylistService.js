const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const { getAuthorizedYoutubeClient } = require('../utils/youtubeClient');
const { getPlaylistItemsByUrl, createNewPlaylist, addVideosToPlaylist } = require('./youtubeService');
const { getIo } = require('../websocket');
const crypto = require('crypto');

const prisma = new PrismaClient();

/**
 * Shuffles array in place using <PERSON><PERSON><PERSON> (Knut<PERSON>) algorithm with crypto for strong randomization.
 * This ensures different results on each call, even with the same input array.
 * @param {Array<T>} array Array to shuffle.
 * @returns {Array<T>} The shuffled array.
 */
function shuffleArray(array) {
    // Return early for empty or single-item arrays
    if (!array || array.length <= 1) return array;
    
    // Create a copy to avoid modifying the original array
    const result = [...array];
    
    // <PERSON><PERSON> shuffle with crypto-secure randomness
    for (let i = result.length - 1; i > 0; i--) {
        // Get cryptographically secure random bytes
        const buf = crypto.randomBytes(4);
        const randomInt = buf.readUInt32LE(0);
        const j = randomInt % (i + 1);
        
        // Swap elements
        [result[i], result[j]] = [result[j], result[i]];
    }

    // Add some logging to verify randomization if array is short enough
    if (array.length <= 10) {
        logger.debug(`Shuffled array: original order vs shuffled order`, {
            original: JSON.stringify(array.slice(0, 5)),
            shuffled: JSON.stringify(result.slice(0, 5)),
            entropy: crypto.randomBytes(2).toString('hex')
        });
    }
    
    return result;
}

/**
 * Generates a new YouTube playlist based on the active template, ensuring uniqueness and respecting maxSongs.
 * @param {string[]} selectedStyles - Array of dance style names explicitly selected for inclusion.
 * @param {number | null} maxSongs - Maximum total number of songs, or null for no limit.
 * @param {string} adminUserId - The ID of the admin user.
 * @returns {Promise<{success: boolean, message: string, statusCode?: number, data?: {playlistId: string, playlistUrl: string}}>}
 */
async function generatePartyPlaylist(selectedStyles, maxSongs, adminUserId) {
    logger.info('Starting party playlist generation (v3 - Pre-fetch Unique)', { selectedStyles, maxSongs, adminUserId });
    logger.info('IMPORTANT: Using ONLY YouTube source playlists for generation - all database suggestions will be IGNORED');

    try {
        // 1. Fetch Active Template
        const activeTemplate = await prisma.playlistTemplate.findFirst({
            where: { isActive: true },
            select: { id: true, name: true, styleRotation: true }
        });

        if (!activeTemplate || !Array.isArray(activeTemplate.styleRotation) || activeTemplate.styleRotation.length === 0) {
            logger.warn('Cannot generate party playlist: No active template found or template has invalid/empty rotation.', { adminUserId });
            return { success: false, message: 'No active playlist template found or it has no style rotation defined.', statusCode: 404 };
        }
        logger.debug(`Using active template: "${activeTemplate.name}"`, { templateId: activeTemplate.id, adminUserId });

        // 2. Fetch Admin Settings (for Source Playlists & YouTube check)
        const adminSettings = await prisma.adminSettings.findFirst({
            where: { singletonLock: true },
            select: { 
                isYouTubeConnected: true, 
                styleSourcePlaylists: true,
                styleSequentialExtractionPrefs: true
            }
        });

        if (!adminSettings?.isYouTubeConnected) {
            logger.warn('Cannot generate party playlist: YouTube account not connected.', { adminUserId });
            return { success: false, message: 'YouTube account is not connected.', statusCode: 400 };
        }
        const sourcePlaylistsMap = adminSettings.styleSourcePlaylists || {};
        const sequentialExtractionPrefs = adminSettings.styleSequentialExtractionPrefs || {};
        logger.debug('Fetched admin settings', { adminUserId, sequentialExtractionEnabled: Object.keys(sequentialExtractionPrefs) });

        // --- Step 3: Pre-fetch and Pool Unique Songs per Required Style ---
        const requiredStyles = [...new Set(activeTemplate.styleRotation.map(block => block.style))];
        const styleSongPools = {}; // { style: [uniqueVideoId1, uniqueVideoId2, ...], ... }
        const stylePoolIndices = {}; // { style: 0, ... } Keep track of the next index to pull

        logger.info('Pre-fetching unique songs for required styles:', { styles: requiredStyles });

        for (const style of requiredStyles) {
             if (!selectedStyles.includes(style)) {
                 logger.debug(`Skipping pre-fetch for unselected style: "${style}"`);
                 continue; // Skip fetching if style wasn't selected by admin
             }

            // Initialize empty arrays
            let styleSuggestionIds = []; // This will remain empty now
            let styleAdminSourceIds = [];

            // Fetch Admin Source Playlist Items
            const playlistUrl = sourcePlaylistsMap[style];
            if (playlistUrl) {
                try {
                    styleAdminSourceIds = await getPlaylistItemsByUrl(playlistUrl);
                    logger.debug(`Fetched ${styleAdminSourceIds.length} videos from admin source for style "${style}"`);
                    
                    // Check if sequential extraction is enabled for this style
                    const isSequentialExtraction = !!sequentialExtractionPrefs[style];
                    
                    if (!isSequentialExtraction) {
                        // Original random extraction logic
                        // If the source playlist is very large, randomly sample from it
                        // for additional uniqueness between playlist generations
                        const MAX_SOURCE_SAMPLE = 50; // Maximum number of songs to sample if source is large
                        const THRESHOLD_FOR_SAMPLING = MAX_SOURCE_SAMPLE * 2; // Only sample if we have at least 2x the sample size
                        
                        if (styleAdminSourceIds.length > THRESHOLD_FOR_SAMPLING) {
                            // Shuffle and take a subset - this adds randomness between different generations
                            const shuffledSource = shuffleArray([...styleAdminSourceIds]);
                            styleAdminSourceIds = shuffledSource.slice(0, MAX_SOURCE_SAMPLE);
                            logger.info(`Large source for "${style}" detected (${shuffledSource.length} videos). Randomly sampled ${styleAdminSourceIds.length} videos for variety.`);
                        }
                    } else {
                        // For sequential extraction, we keep the array as is (already in playlist order)
                        logger.info(`Sequential extraction enabled for "${style}". Using songs in original playlist order.`);
                    }
                } catch (err) {
                    logger.error(`Error fetching admin source playlist for style "${style}", skipping source.`, { url: playlistUrl, error: err.message });
                    // Continue without admin source for this style
                }
            }

            // Use ONLY YouTube playlist items (no database suggestions)
            // Previously was: const combinedIds = [...styleSuggestionIds, ...styleAdminSourceIds];
            const uniqueIds = [...new Set(styleAdminSourceIds)];
            
            const isSequentialExtraction = !!sequentialExtractionPrefs[style];
            
            if (isSequentialExtraction) {
                // For sequential extraction with only YouTube sources, we can just use the source playlist order directly
                styleSongPools[style] = [...uniqueIds];
                logger.info(`Created sequential pool for style "${style}" using only YouTube source: ${uniqueIds.length} songs`);
            } else {
                // For random extraction, shuffle the IDs
                styleSongPools[style] = shuffleArray([...uniqueIds]);
                logger.info(`Created shuffled pool for style "${style}" using only YouTube source: ${uniqueIds.length} songs`);
            }
            
            stylePoolIndices[style] = 0; // Initialize index tracker
        }
        logger.info('Finished pre-fetching song pools.');

        // For styles with random extraction, reshuffle the pools right before template cycling
        for (const style in styleSongPools) {
            const isSequentialExtraction = !!sequentialExtractionPrefs[style];
            
            if (!isSequentialExtraction && styleSongPools[style].length > 0) {
                styleSongPools[style] = shuffleArray([...styleSongPools[style]]);
                logger.debug(`Reshuffled ${styleSongPools[style].length} songs for style "${style}" before template cycling`);
            } else if (isSequentialExtraction) {
                // For sequential extraction, log the order to help with debugging
                logger.debug(`Maintaining sequential order for style "${style}" with ${styleSongPools[style].length} songs`);
                if (styleSongPools[style].length > 0) {
                    logger.debug(`First 3 songs in sequential order for "${style}": ${styleSongPools[style].slice(0, 3).join(', ')}`);
                }
            }
        }

        // --- Step 4: Build Final Sequence Iteratively by Cycling Template ---
        const finalSequenceVideoIds = [];
        const globallyUsedVideoIds = new Set(); // Track used songs across all blocks/cycles
        // For sequential styles, we want to track which songs were used in which template block
        const sequentialStyleBlocks = {}; // { style: { blockIndex: [videoIds] } }
        let templateIndex = 0;
        let songsAddedInLastCycle = 1; // Initialize > 0 to start the loop

        // Initialize the sequential style blocks tracking structure
        for (const style in sequentialExtractionPrefs) {
            if (sequentialExtractionPrefs[style]) {
                sequentialStyleBlocks[style] = {};
            }
        }

        logger.info('Building final playlist sequence by cycling template...');

        // Loop until max songs is reached OR a full template cycle adds no new songs
        while ((!maxSongs || finalSequenceVideoIds.length < maxSongs) && songsAddedInLastCycle > 0) {
            
            songsAddedInLastCycle = 0; // Reset counter for this cycle
            logger.debug(`Starting template cycle. Current length: ${finalSequenceVideoIds.length}. Target: ${maxSongs ?? 'unlimited'}`);

            for (let blockIndex = 0; blockIndex < activeTemplate.styleRotation.length; blockIndex++) {
                const block = activeTemplate.styleRotation[blockIndex];
                const style = block?.style;
                const count = block?.count || 1; // Get the count of songs for this style block, default to 1
                
                if (typeof style !== 'string' || typeof count !== 'number' || count <= 0) {
                    logger.warn('Skipping invalid block in styleRotation cycle', { block, templateId: activeTemplate.id });
                    continue;
                }

                // Skip block if its style wasn't selected by admin OR if pool doesn't exist
                if (!selectedStyles.includes(style) || !styleSongPools[style]) {
                    logger.debug(`Skipping block for style "${style}" during cycle (unselected or no pool).`);
                    continue;
                }

                logger.debug(`Processing template block #${blockIndex}: Style="${style}", Count=${count}`);
                
                const isSequentialExtraction = !!sequentialExtractionPrefs[style];
                
                // Try to add exactly 'count' songs for this style block
                let songsAddedForBlock = 0;
                
                // For sequential styles, we need to track which block instance this is
                if (isSequentialExtraction) {
                    // Create a key for this specific block instance if it doesn't exist
                    const blockKey = `${blockIndex}_${templateIndex}`;
                    if (!sequentialStyleBlocks[style][blockKey]) {
                        sequentialStyleBlocks[style][blockKey] = [];
                    }
                }
                
                for (let i = 0; i < count; i++) {
                    // Check if maxSongs limit reached
                    if (maxSongs && finalSequenceVideoIds.length >= maxSongs) {
                        logger.info(`Max songs limit (${maxSongs}) reached during template cycle.`);
                        break; // Stop processing this block
                    }
                    
                    // Find the next *unique* song from this style's pool
                    const stylePool = styleSongPools[style];
                    let foundUniqueSong = false;
                    let currentPoolIndex = stylePoolIndices[style] || 0; // Start from the current index for this style

                    while (currentPoolIndex < stylePool.length) {
                        const candidateVideoId = stylePool[currentPoolIndex];
                        
                        if (!globallyUsedVideoIds.has(candidateVideoId)) {
                            // Found a unique song for this slot!
                            finalSequenceVideoIds.push(candidateVideoId);
                            globallyUsedVideoIds.add(candidateVideoId);
                            stylePoolIndices[style] = currentPoolIndex + 1; // Update pool index for next time
                            
                            // For sequential styles, record which block this song was added to
                            if (isSequentialExtraction) {
                                const blockKey = `${blockIndex}_${templateIndex}`;
                                sequentialStyleBlocks[style][blockKey].push(candidateVideoId);
                            }
                            
                            songsAddedInLastCycle++;
                            songsAddedForBlock++;
                            foundUniqueSong = true;
                            logger.debug(`Added unique video "${candidateVideoId}" for style "${style}" block #${blockIndex} (${songsAddedForBlock}/${count}). Total: ${finalSequenceVideoIds.length}`);
                            break; // Exit the inner while loop (found song for this block slot)
                        }
                        // If already used globally, move to the next candidate in the pool
                        currentPoolIndex++;
                    }
                    
                    // If we couldn't find a unique song for this position in the block
                    if (!foundUniqueSong) {
                        logger.warn(`Could not find a new unique song for style "${style}" (Block #${blockIndex}, Position ${i+1}/${count}). Pool exhausted or all remaining songs already used.`);
                        stylePoolIndices[style] = currentPoolIndex; // Update the index even if we didn't find a song
                        break; // Stop trying to add more songs to this block
                    }
                }

                logger.debug(`Completed adding songs for style "${style}" block: ${songsAddedForBlock}/${count} songs added.`);
                
                // Break the outer loop if maxSongs is reached
                if (maxSongs && finalSequenceVideoIds.length >= maxSongs) {
                    break;
                }
            } // End inner loop (for each block in template)
            
            // Increment the template cycle counter
            templateIndex++;

            // Check if maxSongs limit was reached after processing the cycle
            if (maxSongs && finalSequenceVideoIds.length >= maxSongs) {
                break; // Exit the main while loop
            }

            // If a full cycle added no songs, break the main loop
            if (songsAddedInLastCycle === 0) {
                logger.warn('Completed a full template cycle without adding any new unique songs. Stopping generation. All pools likely exhausted or remaining songs already used.', { finalCount: finalSequenceVideoIds.length });
                break;
            }

        } // End outer loop (while maxSongs not reached and songs are being added)

        // Log sequential block information for debugging
        for (const style in sequentialStyleBlocks) {
            const blocks = sequentialStyleBlocks[style];
            const totalSequentialSongs = Object.values(blocks).reduce((sum, songs) => sum + songs.length, 0);
            if (totalSequentialSongs > 0) {
                logger.info(`Style "${style}" has ${totalSequentialSongs} songs in ${Object.keys(blocks).length} sequential blocks`);
                // Log first few songs of each block to verify sequential order
                Object.keys(blocks).slice(0, 3).forEach(blockKey => {
                    const songs = blocks[blockKey];
                    if (songs.length > 0) {
                        logger.debug(`Block ${blockKey} for style "${style}" has ${songs.length} songs, starting with: ${songs.slice(0, 3).join(', ')}`);
                    }
                });
            }
        }

        logger.info(`Finished building sequence by cycling template. Final count: ${finalSequenceVideoIds.length} unique songs.`);

        // --- Step 5: Create Playlist & Add Songs ---
        if (finalSequenceVideoIds.length === 0) {
            logger.warn('No video IDs collected after processing template and filters. Cannot create empty playlist.', { selectedStyles, templateId: activeTemplate.id, adminUserId });
            return { success: false, message: 'No songs found for the selected styles based on the active template and configured sources.', statusCode: 400 };
        }

        // For sequential styles, check if we need to reorder any songs to maintain true sequential order
        const hasSequentialStyles = Object.values(sequentialExtractionPrefs).some(val => val === true);
        let playlistSequence = [...finalSequenceVideoIds]; // Default sequence (as generated by template cycling)
        
        if (hasSequentialStyles) {
            logger.info('Sequential extraction enabled for some styles. Analyzing if reordering is needed...');
            
            // For sequential styles, check if this is a single-style playlist with sequential extraction
            const sequentialStyles = Object.keys(sequentialExtractionPrefs).filter(style => 
                sequentialExtractionPrefs[style] && selectedStyles.includes(style));
            
            if (sequentialStyles.length === 1 && selectedStyles.length === 1) {
                // Special case: Only one style selected and it's sequential - use pure sequential order from source
                const style = sequentialStyles[0];
                logger.info(`Single-style playlist with sequential extraction for ${style}. Using original source order.`);
                
                playlistSequence = styleSongPools[style].filter(id => globallyUsedVideoIds.has(id));
                logger.info(`Reordered playlist to match source sequential order for ${style}. Total songs: ${playlistSequence.length}`);
            } 
            else if (sequentialStyles.length > 0) {
                // For multi-style playlists, we need to honor the template pattern but ensure sequential order within each style block
                logger.info(`Multi-style playlist with ${sequentialStyles.length} sequential styles. Maintaining style blocks with sequential ordering within each block.`);
                
                // First, group videos by their style
                const videosByStyle = {};
                
                // Initialize the structure
                for (const style of selectedStyles) {
                    videosByStyle[style] = [];
                }
                
                // Identify which video belongs to which style and store its position in the template sequence
                finalSequenceVideoIds.forEach((videoId, index) => {
                    for (const style of selectedStyles) {
                        if (styleSongPools[style] && styleSongPools[style].includes(videoId)) {
                            videosByStyle[style].push({ videoId, position: index });
                            break; // Each video belongs to only one style
                        }
                    }
                });
                
                // For sequential styles, reorder the videos in their original order from the source
                for (const style of sequentialStyles) {
                    if (videosByStyle[style].length > 0) {
                        // Get the original ordered list of video IDs for this style
                        const originalOrder = styleSongPools[style].filter(id => 
                            videosByStyle[style].some(item => item.videoId === id));
                        
                        // Sort the videosByStyle entries based on the original order
                        videosByStyle[style].sort((a, b) => {
                            const indexA = originalOrder.indexOf(a.videoId);
                            const indexB = originalOrder.indexOf(b.videoId);
                            return indexA - indexB;
                        });
                        
                        logger.debug(`Reordered ${videosByStyle[style].length} videos for sequential style "${style}" to match source order`);
                    }
                }
                
                // Now rebuild the sequence while maintaining the positions
                const newSequence = new Array(finalSequenceVideoIds.length);
                
                // Place videos back in their template positions, but with the proper sequential ordering within each style
                for (const style of selectedStyles) {
                    const videos = videosByStyle[style];
                    
                    // For sequential styles, place videos in their ordered sequence
                    if (sequentialStyles.includes(style)) {
                        videos.forEach((videoInfo, index) => {
                            const position = videoInfo.position;
                            newSequence[position] = videoInfo.videoId;
                        });
                    } 
                    // For non-sequential styles, maintain their randomized order
                    else {
                        videos.forEach(videoInfo => {
                            const position = videoInfo.position;
                            newSequence[position] = videoInfo.videoId;
                        });
                    }
                }
                
                // Filter out any undefined values (should not happen, but just in case)
                playlistSequence = newSequence.filter(id => id !== undefined);
                logger.info(`Reordered playlist to maintain sequential extraction within style blocks. Total songs: ${playlistSequence.length}`);
            }
        }

        // Create playlist name and description
        const timestamp = new Date().toLocaleString('en-US', { month: 'short', day: 'numeric', hour: 'numeric', minute: '2-digit', hour12: true });
        const playlistName = `Party Mix (${activeTemplate.name}) - ${timestamp} [YouTube Sources Only]`;
        const playlistDescription = `Generated party playlist using only YouTube source playlists (no suggestions) with template "${activeTemplate.name}" for styles: ${selectedStyles.join(', ')}. Target size: ${maxSongs ?? 'All available'}. Generated on ${new Date().toISOString()}.`;

        // Pre-filter known problematic video IDs
        // This specific video was reported as unavailable in the logs
        const knownBadIds = ['BhitwIgbTYc']; 
        const filteredPlaylistSequence = playlistSequence.filter(id => !knownBadIds.includes(id));
        
        if (filteredPlaylistSequence.length < playlistSequence.length) {
            logger.warn(`Filtered out ${playlistSequence.length - filteredPlaylistSequence.length} known unavailable videos from playlist sequence`, {
                removedIds: knownBadIds,
                originalCount: playlistSequence.length,
                newCount: filteredPlaylistSequence.length
            });
        }
        
        // Fail early if we have no videos left
        if (filteredPlaylistSequence.length === 0) {
            logger.error('All videos were filtered out as unavailable. Cannot create empty playlist.', { adminUserId });
            return { success: false, message: 'All videos were filtered due to availability issues. Cannot create empty playlist.', statusCode: 400 };
        }

        // Create the new playlist
        let newPlaylistData;
        try {
            newPlaylistData = await createNewPlaylist(playlistName, playlistDescription);
            if (!newPlaylistData || !newPlaylistData.id) {
                throw new Error('Failed to create new playlist on YouTube or received invalid response.');
            }
            logger.info('Successfully created new YouTube playlist', { name: playlistName, id: newPlaylistData.id, adminUserId });
        } catch (error) {
             logger.error('Failed to create new YouTube playlist', { error: error.message, name: playlistName, adminUserId });
             return { success: false, message: `Failed to create YouTube playlist: ${error.message}`, statusCode: 500 };
        }

        try {
            // Use our filtered sequence to avoid problematic videos
            const addResult = await addVideosToPlaylist(newPlaylistData.id, filteredPlaylistSequence);
            
            // If there were partial failures but we still added some videos, consider it a success
            // with a warning message
            if (!addResult.success && addResult.successfulInserts > 0) {
                logger.warn(`Partially successful playlist generation. Added ${addResult.successfulInserts}/${filteredPlaylistSequence.length} videos to playlist ${newPlaylistData.id}`, { 
                    errors: addResult.errors,
                    adminUserId 
                });
                
                // Continue with the process as we did add some videos successfully
            } else if (!addResult.success) {
                 logger.error('Failed to add videos to the new playlist', { 
                     playlistId: newPlaylistData.id, 
                     error: addResult.errors, 
                     videoCount: filteredPlaylistSequence.length, 
                     adminUserId 
                 });
                 return { success: false, message: `Playlist created (ID: ${newPlaylistData.id}), but failed to add videos: ${addResult.errors.join('; ')}. Manual cleanup might be needed.`, statusCode: 500 };
            }
            
            logger.info(`Successfully added ${addResult.successfulInserts || filteredPlaylistSequence.length} videos to playlist ${newPlaylistData.id}`, { adminUserId });
        } catch (error) {
            logger.error('Error calling addVideosToPlaylist', { playlistId: newPlaylistData.id, error: error.message, videoCount: filteredPlaylistSequence.length, adminUserId });
            return { success: false, message: `Playlist created (ID: ${newPlaylistData.id}), but encountered an error adding videos: ${error.message}. Manual cleanup might be needed.`, statusCode: 500 };
        }

        const playlistUrl = `https://www.youtube.com/playlist?list=${newPlaylistData.id}`;

        // Save the playlist to history for future reference
        try {
            await prisma.generatedPlaylistHistory.create({
                data: {
                    playlistId: newPlaylistData.id,
                    playlistUrl: playlistUrl,
                    name: playlistName,
                    description: playlistDescription,
                    songCount: finalSequenceVideoIds.length,
                    adminUserId: adminUserId
                }
            });
            logger.info('Saved playlist to history', { playlistId: newPlaylistData.id, adminUserId });
            
            // Emit websocket event to notify clients of the new playlist
            try {
                const io = getIo();
                if (io) {
                    io.emit('playlist:new', { 
                        playlistId: newPlaylistData.id,
                        playlistUrl: playlistUrl,
                        name: playlistName
                    });
                    logger.debug('Emitted playlist:new websocket event', { playlistId: newPlaylistData.id });
                }
            } catch (socketError) {
                logger.warn('Failed to emit websocket event', { 
                    error: socketError.message, 
                    playlistId: newPlaylistData.id 
                });
                // Don't fail the whole operation if socket emission fails
            }
        } catch (historyError) {
            // Don't fail the overall operation if history saving fails
            logger.error('Failed to save playlist to history', { 
                error: historyError.message, 
                playlistId: newPlaylistData.id, 
                adminUserId 
            });
        }

        return {
            success: true,
            message: `Party playlist generated successfully with ${finalSequenceVideoIds.length} unique songs!`,
            data: {
                playlistId: newPlaylistData.id,
                playlistUrl: playlistUrl,
            },
        };

    } catch (error) {
        logger.error('Unexpected error during party playlist generation process (v3): ', error, { adminUserId });
        return { success: false, message: 'An internal server error occurred during playlist generation.', statusCode: 500 };
    }
}

module.exports = {
    generatePartyPlaylist,
}; 