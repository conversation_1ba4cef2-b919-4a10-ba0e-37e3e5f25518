const cronService = require('./cronService');
const logger = require('../utils/logger');

// Initialize all cron jobs using the enhanced cronService
const initCronJobs = () => {
  try {
    cronService.init();
    logger.info('All cron jobs initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize cron jobs:', error);
  }
};

module.exports = {
  initCronJobs,
  cronService // Export for manual testing/admin access
}; 