const { getAuthorizedYoutubeClient } = require('../utils/youtubeClient');
const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const { triggerPlayerStateUpdate } = require('../websocket');

const prisma = new PrismaClient();

/**
 * Fetches the most recently watched video from YouTube history.
 * This requires the correct YouTube API scope (history.readonly).
 * @returns {Promise<{videoId: string, title: string, channelTitle: string, watchedAt: Date} | null>}
 */
async function getLastWatchedVideo() {
    try {
        logger.info('[YouTubeService] Attempting to fetch video for sync');
        // Use only valid scopes
        const youtube = await getAuthorizedYoutubeClient();
        
        // Get the active playlist ID first (we'll need it for fallbacks)
        const settings = await prisma.adminSettings.findUnique({
            where: { singletonLock: true },
            select: { 
                activeYoutubePlaylistId: true,
                operationalMode: true
            }
        });
        
        if (!settings?.activeYoutubePlaylistId) {
            logger.warn('[YouTubeService] No active YouTube playlist configured');
            return null;
        }
        
        // Get playlist items - this is our primary source now that history is unavailable
        const playlistResponse = await youtube.playlistItems.list({
            part: ['snippet,contentDetails'],
            playlistId: settings.activeYoutubePlaylistId,
            maxResults: 50 
        });
        
        const playlistItems = playlistResponse.data.items || [];
        if (playlistItems.length === 0) {
            logger.warn('[YouTubeService] Active playlist is empty');
            return null;
        }
        
        // We can't access watch history via API anymore, so we'll try liked videos first
        logger.info('[YouTubeService] Trying liked videos');
        try {
            const videoResponse = await youtube.videos.list({
                part: ['snippet,contentDetails'],
                myRating: 'like',
                maxResults: 10
            });
            
            // Try to find a liked video that's in our playlist
            const playlistVideoIds = new Set(
                playlistItems.map(item => item.snippet.resourceId.videoId)
            );
            
            const likedVideos = videoResponse.data.items || [];
            for (const video of likedVideos) {
                if (playlistVideoIds.has(video.id)) {
                    logger.info('[YouTubeService] Found recently liked video that matches playlist');
                    return {
                        videoId: video.id,
                        title: video.snippet.title,
                        channelTitle: video.snippet.channelTitle,
                        thumbnailUrl: video.snippet.thumbnails?.default?.url,
                        watchedAt: new Date(),
                        source: 'liked_videos'
                    };
                }
            }
        } catch (likedError) {
            logger.warn('[YouTubeService] Error fetching liked videos', { error: likedError.message });
            // Continue to playlist fallback
        }
        
        // Fall back to just picking the first video in the playlist
        logger.info('[YouTubeService] Falling back to first video in active playlist');
        const firstVideo = playlistItems[0];
        return {
            videoId: firstVideo.snippet.resourceId.videoId,
            title: firstVideo.snippet.title,
            channelTitle: firstVideo.snippet.channelTitle,
            thumbnailUrl: firstVideo.snippet.thumbnails?.default?.url,
            watchedAt: new Date(),
            source: 'playlist_fallback'
        };
    } catch (error) {
        logger.error('[YouTubeService] Failed to fetch video for sync', { 
            error: error.message, 
            responseData: error.response?.data,
            stack: error.stack 
        });
        
        throw error;
    }
}

/**
 * Checks if the last watched video matches an item in the current playlist.
 * If it does, updates the current song index to reflect the external playback position.
 * Uses multiple fallback methods to determine the current video: watch history,
 * liked videos, and active playlist items.
 */
/* // <--- COMMENT OUT START
async function syncCurrentSongFromWatchHistory() {
    try {
        // 1. Get current admin settings to check mode and sync interval
        const settings = await prisma.adminSettings.findUnique({
            where: { singletonLock: true },
            select: { 
                operationalMode: true, 
                activeYoutubePlaylistId: true, 
                lastWatchedVideoId: true,
                watchHistorySyncIntervalSeconds: true // This field will be removed elsewhere
            }
        });
        
        // Only proceed if in EXTERNAL_MIRROR mode
        if (settings?.operationalMode !== 'EXTERNAL_MIRROR') {
            logger.debug('[YouTubeService] Watch history sync skipped: Not in EXTERNAL_MIRROR mode');
            return null;
        }
        
        // Only proceed if there's an active playlist
        if (!settings.activeYoutubePlaylistId) {
            logger.debug('[YouTubeService] Watch history sync skipped: No active YouTube playlist');
            return null;
        }
        
        // 2. Fetch the latest video from YouTube (with fallbacks)
        const watchedVideo = await getLastWatchedVideo();
        if (!watchedVideo) {
            logger.warn('[YouTubeService] No videos found via any fallback method (watch history, likes, or playlist)');
            return {
                success: false,
                error: 'No video found to sync from. Try interacting with a video from the playlist.'
            };
        }
        
        logger.info('[YouTubeService] Found video to sync from:', { 
            videoId: watchedVideo.videoId, 
            title: watchedVideo.title, 
            method: watchedVideo.source || 'watch_history' 
        });
        
        // 3. Check if this is different from the last known watched video
        if (settings.lastWatchedVideoId === watchedVideo.videoId) {
            logger.debug('[YouTubeService] Last watched video unchanged', { videoId: watchedVideo.videoId });
            return {
                success: false,
                info: 'Last watched video unchanged'
            };
        }
        
        // 4. Get the active playlist to look for this video
        const activePlaylist = await prisma.activePlaylist.findFirst({
            where: { youtubePlaylistId: settings.activeYoutubePlaylistId },
            orderBy: { createdAt: 'desc' },
            include: { 
                playlistItems: {
                    orderBy: { order: 'asc' },
                    select: { id: true, youtubeVideoId: true, order: true, title: true }
                }
            }
        });
        
        if (!activePlaylist || !activePlaylist.playlistItems.length) {
            logger.warn('[YouTubeService] Could not find active playlist or it contains no items');
            return {
                success: false,
                error: 'Active playlist not found or empty'
            };
        }
        
        // 5. Find the matching video in the playlist
        const matchingItem = activePlaylist.playlistItems.find(
            item => item.youtubeVideoId === watchedVideo.videoId
        );
        
        if (!matchingItem) {
            logger.warn('[YouTubeService] Video not found in active playlist', { 
                videoId: watchedVideo.videoId,
                videoTitle: watchedVideo.title,
                playlistId: settings.activeYoutubePlaylistId
            });
            return {
                success: false,
                error: 'Video not found in active playlist',
                videoId: watchedVideo.videoId,
                title: watchedVideo.title
            };
        }
        
        // 6. Update the currentSongIndex in the ActivePlaylist
        const updatedPlaylist = await prisma.activePlaylist.update({
            where: { id: activePlaylist.id },
            data: { 
                currentSongIndex: matchingItem.order,
                // We don't set isPlaying to true because in EXTERNAL_MIRROR mode
                // we don't control playback, just reflect the external state
            }
        });
        
        // 7. Update the lastWatchedVideoId in AdminSettings
        await prisma.adminSettings.update({
            where: { singletonLock: true },
            data: { lastWatchedVideoId: watchedVideo.videoId }
        });
        
        logger.info('[YouTubeService] Updated current song index based on video activity', {
            videoId: watchedVideo.videoId,
            newIndex: matchingItem.order,
            title: watchedVideo.title
        });
        
        // 8. Notify clients of the state change
        try {
            if (typeof triggerPlayerStateUpdate === 'function') {
                await triggerPlayerStateUpdate('watch_history_sync');
            } else {
                logger.info('[YouTubeService] WebSocket emit function not available (normal when running in standalone mode)');
            }
        } catch (error) {
            logger.warn('[YouTubeService] Could not emit player state update', { error: error.message });
            // Continue execution - this shouldn't stop the sync from succeeding
        }
        
        return {
            success: true,
            videoId: watchedVideo.videoId,
            title: watchedVideo.title,
            newIndex: matchingItem.order,
            method: watchedVideo.source
        };
    } catch (error) {
        logger.error('[YouTubeService] Error in watch history sync:', { error: error.message, stack: error.stack });
        return {
            success: false,
            error: `Sync failed: ${error.message}`
        };
    }
}
*/ // <--- COMMENT OUT END

module.exports = {
    getLastWatchedVideo,
    // syncCurrentSongFromWatchHistory // REMOVE this export
    extractPlaylistIdFromUrl,
    getPlaylistItemsByUrl,
    createNewPlaylist,
    addVideosToPlaylist,
    deleteYoutubePlaylist,
    getPlaylistVideoDetails,
    // --- Added exports ---
    // --- End Added exports ---
};

// --- Added Functions for Party Playlist Generation ---

/**
 * Extracts playlist ID from a YouTube playlist URL.
 * @param {string} url - The YouTube playlist URL.
 * @returns {string | null} - The playlist ID or null if not found.
 */
function extractPlaylistIdFromUrl(url) {
    if (!url) return null;
    try {
        const parsedUrl = new URL(url);
        if (parsedUrl.hostname === 'www.youtube.com' || parsedUrl.hostname === 'youtube.com') {
            const listId = parsedUrl.searchParams.get('list');
            if (listId && /^[a-zA-Z0-9_-]+$/.test(listId)) { // Basic validation for playlist ID format
                return listId;
            }
        }
    } catch (e) {
        logger.warn('Failed to parse URL for playlist ID extraction', { url, error: e.message });
    }
    return null;
}

/**
 * Fetches all video IDs from a given YouTube playlist URL.
 * Handles pagination automatically.
 * @param {string} playlistUrl - The URL of the YouTube playlist.
 * @returns {Promise<string[]>} - A promise that resolves to an array of video IDs.
 * @throws {Error} If the URL is invalid, playlist not found, or API error occurs.
 */
async function getPlaylistItemsByUrl(playlistUrl) {
    const playlistId = extractPlaylistIdFromUrl(playlistUrl);
    if (!playlistId) {
        logger.error('Invalid or unextractable YouTube Playlist URL', { playlistUrl });
        throw new Error('Invalid YouTube Playlist URL format.');
    }
    logger.info('[YouTubeService] Fetching items for playlist ID:', { playlistId });

    try {
        const youtube = await getAuthorizedYoutubeClient();
        let videoIds = [];
        let nextPageToken = null;

        do {
            const response = await youtube.playlistItems.list({
                part: ['contentDetails'], // Only need contentDetails for videoId
                playlistId: playlistId,
                maxResults: 50, // Max allowed by API
                pageToken: nextPageToken,
            });

            const items = response.data.items || [];
            items.forEach(item => {
                if (item.contentDetails?.videoId) {
                    videoIds.push(item.contentDetails.videoId);
                }
            });

            nextPageToken = response.data.nextPageToken;
            logger.debug(`Fetched page of playlist items. Count: ${items.length}. Next page token: ${nextPageToken ? 'Yes' : 'No'}`, { playlistId });

        } while (nextPageToken);

        logger.info(`Successfully fetched ${videoIds.length} video IDs from playlist`, { playlistId });
        return videoIds;

    } catch (error) {
        logger.error('[YouTubeService] Failed to fetch playlist items', { 
            playlistId, 
            error: error.message, 
            statusCode: error.response?.status,
            responseData: error.response?.data 
        });
        if (error.response?.status === 404) {
             throw new Error(`Playlist not found (ID: ${playlistId}). Check the URL or permissions.`);
        }
        throw new Error(`Failed to fetch playlist items: ${error.message}`);
    }
}

/**
 * Creates a new private YouTube playlist.
 * @param {string} name - The name for the new playlist.
 * @param {string} description - The description for the new playlist.
 * @returns {Promise<{id: string, title: string}>} - A promise resolving to the new playlist's ID and title.
 * @throws {Error} If playlist creation fails.
 */
async function createNewPlaylist(name, description) {
    logger.info('[YouTubeService] Attempting to create new playlist', { name });
    try {
        const youtube = await getAuthorizedYoutubeClient();
        const response = await youtube.playlists.insert({
            part: ['snippet', 'status'],
            requestBody: {
                snippet: {
                    title: name,
                    description: description,
                },
                status: {
                    privacyStatus: 'private', // Or 'public'/'unlisted' if needed
                },
            },
        });

        if (response.data && response.data.id) {
            logger.info('[YouTubeService] Successfully created new playlist', { id: response.data.id, title: response.data.snippet.title });
            return {
                id: response.data.id,
                title: response.data.snippet.title,
            };
        } else {
            throw new Error('YouTube API did not return a valid playlist ID after creation.');
        }
    } catch (error) {
        logger.error('[YouTubeService] Failed to create new playlist', {
            name,
            error: error.message,
            statusCode: error.response?.status,
            responseData: error.response?.data
        });
        throw new Error(`Failed to create playlist: ${error.message}`);
    }
}

/**
 * Adds an array of video IDs to a specified YouTube playlist.
 * Handles batching if the number of videos exceeds API limits per request (typically 50).
 * @param {string} playlistId - The ID of the target YouTube playlist.
 * @param {string[]} videoIds - An array of YouTube video IDs to add.
 * @returns {Promise<{success: boolean, errors: string[]}>} - Resolves with success status and any errors encountered.
 */
async function addVideosToPlaylist(playlistId, videoIds) {
    if (!videoIds || videoIds.length === 0) {
        logger.warn('[YouTubeService] No video IDs provided to add to playlist', { playlistId });
        return { success: true, errors: [], successfulInserts: 0 }; // Nothing to add
    }
    logger.info(`[YouTubeService] Attempting to add ${videoIds.length} videos to playlist`, { playlistId });

    const youtube = await getAuthorizedYoutubeClient();
    if (!youtube) {
        return { success: false, errors: ['YouTube client not authorized'], successfulInserts: 0 };
    }

    const errors = [];
    let successfulInserts = 0;
    const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
    const MAX_RETRIES = 2;
    const RETRY_DELAY_MS = 1000;

    for (let i = 0; i < videoIds.length; i++) {
        const videoId = videoIds[i];
        logger.debug(`Attempting to insert video ${i + 1}/${videoIds.length}: ${videoId}`, { playlistId });

        let retries = 0;
        let inserted = false;
        while (retries <= MAX_RETRIES && !inserted) {
            try {
                await youtube.playlistItems.insert({
                    part: 'snippet',
                    requestBody: {
                        snippet: {
                            playlistId: playlistId,
                            resourceId: {
                                kind: 'youtube#video',
                                videoId: videoId,
                            },
                        },
                    },
                });
                logger.debug(`Successfully inserted video ${videoId}`, { playlistId });
                successfulInserts++;
                inserted = true; // Exit retry loop on success

            } catch (err) {
                const statusCode = err.response?.status || err.code; // Handle different error structures
                const errorMessage = err.message || 'Unknown YouTube API error';
                const isRetryableError = statusCode === 409 || statusCode === 500 || statusCode === 503;

                if (isRetryableError && retries < MAX_RETRIES) {
                    retries++;
                    logger.warn(`[YouTubeService] Retrying insert for video ${videoId} (Attempt ${retries}/${MAX_RETRIES}) after error`, {
                        playlistId,
                        videoId,
                        statusCode,
                        errorMessage,
                    });
                    await delay(RETRY_DELAY_MS * retries); // Simple exponential backoff (1s, 2s)
                } else {
                    // Non-retryable error OR max retries reached
                    const responseData = err.response?.data || {}; // Get more details if available
                    logger.error('[YouTubeService] Single video insert error (failed after retries or non-retryable)', {
                        playlistId,
                        videoId,
                        statusCode: statusCode,
                        errorMessage,
                        responseData,
                        retries
                    });
                    errors.push(`Failed to add video ${videoId}: ${errorMessage} (Status: ${statusCode || 'N/A'})`);
                    break; // Exit retry loop, mark as failed
                }
            }
        } // End retry while loop

        // Optional: Small delay even after success/failure to further avoid rate limits
        await delay(1000);
    } // End main for loop

    if (errors.length > 0) {
        logger.error(`[YouTubeService] Encountered ${errors.length} errors while adding ${videoIds.length} videos to playlist`, {
            playlistId,
            successfulInserts,
        });
        return { success: false, errors, successfulInserts };
    } else {
        logger.info(`[YouTubeService] Successfully added all ${videoIds.length} videos.`, { playlistId });
        return { success: true, errors: [], successfulInserts };
    }
}

/**
 * Deletes a YouTube playlist completely
 * @param {string} playlistId - YouTube playlist ID to delete
 * @returns {Promise<{success: boolean, error?: string}>}
 */
async function deleteYoutubePlaylist(playlistId) {
    if (!playlistId) {
        logger.error('[YouTubeService] Missing playlist ID for deletion');
        return { success: false, error: 'Missing playlist ID' };
    }

    try {
        logger.info('[YouTubeService] Attempting to delete playlist', { playlistId });
        const youtube = await getAuthorizedYoutubeClient();
        
        await youtube.playlists.delete({
            id: playlistId
        });
        
        logger.info('[YouTubeService] Successfully deleted playlist', { playlistId });
        return { success: true };
    } catch (error) {
        logger.error('[YouTubeService] Failed to delete playlist', { 
            error: error.message, 
            playlistId,
            responseData: error.response?.data,
            stack: error.stack 
        });
        
        return { 
            success: false, 
            error: error.message || 'Failed to delete YouTube playlist' 
        };
    }
}

/**
 * Fetches detailed information for all videos in a YouTube playlist.
 * @param {string} playlistUrl The full YouTube playlist URL.
 * @returns {Promise<Array<{videoId: string, title: string, channelTitle: string, thumbnailUrl: string, durationSeconds: number | null}>>}
 * @throws Will throw an error if YouTube API calls fail or playlist is not found.
 */
async function getPlaylistVideoDetails(playlistUrl) {
  logger.info('[YouTubeService] Attempting to fetch video details for playlist', { playlistUrl });
  const youtube = await getAuthorizedYoutubeClient();
  if (!youtube) {
    logger.error('[YouTubeService] Failed to get authorized YouTube client for playlist video details.');
    throw new Error('Failed to authorize YouTube client.');
  }

  const playlistId = extractPlaylistIdFromUrl(playlistUrl);
  if (!playlistId) {
    logger.warn('[YouTubeService] Invalid or unextractable playlist URL provided for video details.', { playlistUrl });
    throw new Error('Invalid YouTube playlist URL.');
  }

  let allItems = [];
  let nextPageToken = null;

  try {
    // Loop to handle pagination for playlistItems.list
    do {
      const response = await youtube.playlistItems.list({
        part: ['snippet', 'contentDetails'], // contentDetails here is for the playlistItem, not the video itself
        playlistId: playlistId,
        maxResults: 50, // Max allowed by API
        pageToken: nextPageToken,
      });

      if (!response.data.items) {
        logger.warn('[YouTubeService] No items found in playlist or API error for video details.', { playlistId });
        break; // Exit loop if no items
      }

      allItems = allItems.concat(response.data.items);
      nextPageToken = response.data.nextPageToken;
    } while (nextPageToken);

    if (allItems.length === 0) {
      logger.info('[YouTubeService] Playlist is empty or no videos found.', { playlistId });
      return [];
    }

    // Extract video IDs to fetch their durations
    const videoIds = allItems.map(item => item.snippet?.resourceId?.videoId).filter(id => !!id);
    if (videoIds.length === 0) {
        logger.info('[YouTubeService] No valid video IDs found in playlist items.', { playlistId });
        return [];
    }

    // Fetch video details (including duration) for all video IDs in batches of 50
    const videoDetailsMap = new Map();
    for (let i = 0; i < videoIds.length; i += 50) {
      const batchVideoIds = videoIds.slice(i, i + 50);
      const videoResponse = await youtube.videos.list({
        part: ['contentDetails', 'snippet'], // Snippet is needed again if we didn't trust playlistItem's snippet fully
        id: batchVideoIds.join(','),
        maxResults: 50,
      });

      videoResponse.data.items?.forEach(video => {
        const duration = video.contentDetails?.duration;
        let durationSeconds = null;
        if (duration) {
          // Convert ISO 8601 duration to seconds
          const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
          if (match) {
            const hours = parseInt(match[1] || '0');
            const minutes = parseInt(match[2] || '0');
            const seconds = parseInt(match[3] || '0');
            durationSeconds = hours * 3600 + minutes * 60 + seconds;
          }
        }
        videoDetailsMap.set(video.id, {
            title: video.snippet?.title,
            channelTitle: video.snippet?.channelTitle,
            thumbnailUrl: video.snippet?.thumbnails?.default?.url,
            durationSeconds
        });
      });
    }

    // Combine playlist item info with video duration info
    const detailedItems = allItems.map(item => {
      const videoId = item.snippet?.resourceId?.videoId;
      if (!videoId) return null; // Should not happen if filtered above
      
      const details = videoDetailsMap.get(videoId);
      return {
        videoId: videoId,
        title: details?.title || item.snippet?.title || 'Unknown Title',
        channelTitle: details?.channelTitle || item.snippet?.channelTitle || 'Unknown Channel',
        thumbnailUrl: details?.thumbnailUrl || item.snippet?.thumbnails?.default?.url || '',
        durationSeconds: details?.durationSeconds || null,
      };
    }).filter(item => item !== null);

    logger.info(`[YouTubeService] Successfully fetched details for ${detailedItems.length} videos from playlist.`, { playlistId });
    return detailedItems;

  } catch (error) {
    logger.error('[YouTubeService] Error fetching playlist video details:', {
      playlistId,
      error: error.message,
      stack: error.stack,
      response: error.response?.data
    });
    throw new Error(`Failed to fetch video details from playlist: ${error.message}`);
  }
}

// --- End Added Functions --- 