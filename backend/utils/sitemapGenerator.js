const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');
const logger = require('./logger');

const prisma = new PrismaClient();

/**
 * Generates a sitemap.xml file for the application
 * @param {string} baseUrl - The base URL of the site (e.g., https://www.social-dance.org)
 * @returns {Promise<string>} - The path to the generated sitemap file
 */
async function generateSitemap(baseUrl) {
  try {
    logger.info('Starting sitemap generation');
    
    // Ensure we use the canonical production URL in production
    let baseUrlNormalized;
    if (process.env.NODE_ENV === 'production') {
      baseUrlNormalized = 'https://www.social-dance.org';
    } else {
      // For development, use the provided baseUrl but normalize it
      baseUrlNormalized = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    }
    
    logger.info(`Generating sitemap with base URL: ${baseUrlNormalized}`);
    
    // Start XML content
    let sitemap = '<?xml version="1.0" encoding="UTF-8"?>\n';
    sitemap += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
    
    // Add static pages
    const staticPages = [
      { url: '/', priority: '1.0', changefreq: 'daily' },
      { url: '/events', priority: '0.8', changefreq: 'weekly' },
      { url: '/clubs', priority: '0.8', changefreq: 'weekly' },
      { url: '/dancers', priority: '0.9', changefreq: 'daily' },
      { url: '/djs', priority: '0.8', changefreq: 'weekly' },
      { url: '/singers', priority: '0.8', changefreq: 'weekly' },
      { url: '/full-top-voted', priority: '0.7', changefreq: 'daily' },
      { url: '/leaderboard/all', priority: '0.7', changefreq: 'daily' },
      // Note: login/signup pages are excluded as they should not be indexed
    ];
    
    for (const page of staticPages) {
      sitemap += `  <url>\n`;
      sitemap += `    <loc>${baseUrlNormalized}${page.url}</loc>\n`;
      sitemap += `    <changefreq>${page.changefreq}</changefreq>\n`;
      sitemap += `    <priority>${page.priority}</priority>\n`;
      sitemap += `  </url>\n`;
    }
    
    // Add article pages (hardcoded for now, but can be made dynamic later)
    const articleSlugs = [
      'salsa-history',
      'bachata-moves', 
      'dance-festival',
      'dance-music'
    ];
    
    for (const slug of articleSlugs) {
      sitemap += `  <url>\n`;
      sitemap += `    <loc>${baseUrlNormalized}/article/${slug}</loc>\n`;
      sitemap += `    <changefreq>weekly</changefreq>\n`;
      sitemap += `    <priority>0.8</priority>\n`;
      sitemap += `  </url>\n`;
    }
    
    // Fetch dynamic content from database and add to sitemap
    try {
      // Add article pages if they exist in the database
      // Example: const articles = await prisma.article.findMany();
      // This depends on your actual database schema/model
      
      // Uncomment and adjust once Article model exists
      // const articles = await prisma.article.findMany({
      //   select: { slug: true, updatedAt: true }
      // });
      
      // for (const article of articles) {
      //   const lastmod = article.updatedAt.toISOString().split('T')[0];
      //   sitemap += `  <url>\n`;
      //   sitemap += `    <loc>${baseUrlNormalized}/article/${article.slug}</loc>\n`;
      //   sitemap += `    <lastmod>${lastmod}</lastmod>\n`;
      //   sitemap += `    <changefreq>weekly</changefreq>\n`;
      //   sitemap += `    <priority>0.8</priority>\n`;
      //   sitemap += `  </url>\n`;
      // }
      
      // Similarly, you can add other dynamic content types as they're implemented
      // e.g., dancer profiles, events, etc.
      
    } catch (dbError) {
      logger.error('Error fetching content for sitemap', { error: dbError });
      // Continue with static pages only
    }
    
    // Close XML
    sitemap += '</urlset>';
    
    // Write sitemap to the public directory
    const publicDir = path.join(__dirname, '../../frontend/build');
    const sitemapPath = path.join(publicDir, 'sitemap.xml');
    
    fs.writeFileSync(sitemapPath, sitemap);
    logger.info(`Sitemap generated successfully at ${sitemapPath}`);
    
    return sitemapPath;
  } catch (error) {
    logger.error('Error generating sitemap', { error });
    throw error;
  }
}

module.exports = {
  generateSitemap
}; 