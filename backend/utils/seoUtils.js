const logger = require('./logger');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * Generates complete SEO metadata for a given path
 * @param {string} path - The current request path
 * @param {object} req - Express request object
 * @returns {object} - Object containing all SEO-related metadata
 */
async function generateSeoMetadata(path, req) {
  logger.info(`SEO Enhancement: Processing path: ${path}`);
  
  const baseUrl = getBaseUrl(req);
  const canonicalUrl = `${baseUrl}${path}`;
  
  // Default metadata (fallback) with enterprise-level enhancements
  let metadata = {
    title: 'Social Dance Moments',
    description: 'Share and discover social dance music with dancers worldwide. Create playlists, suggest songs, and connect with the dancing community.',
    keywords: 'social dance, dance music, dance playlists, bachata, salsa, kizomba, dance community',
    canonicalUrl,
    ogTitle: 'Social Dance Moments',
    ogDescription: 'Share and discover social dance music with dancers worldwide.',
    ogUrl: canonicalUrl,
    ogType: 'website',
    ogImage: `${baseUrl}/img/social-dance-share.jpg`,
    ogLocale: 'en_US',
    ogSiteName: 'Social Dance Moments',
    twitterCard: 'summary_large_image',
    twitterTitle: 'Social Dance Moments',
    twitterDescription: 'Share and discover social dance music with dancers worldwide.',
    twitterImage: `${baseUrl}/img/social-dance-share.jpg`,
    twitterSite: '@SocialDanceMoments',
    structuredData: generateWebsiteStructuredData(baseUrl),
    breadcrumbData: path !== '/' ? generateBreadcrumbStructuredData(path, baseUrl) : null,
    hreflangs: [
      { lang: 'en', url: canonicalUrl },
      { lang: 'x-default', url: canonicalUrl }
    ],
    lastModified: new Date().toISOString(),
    // Enterprise-level SEO metadata
    author: 'Social Dance Moments Team',
    robots: 'index,follow,max-image-preview:large,max-snippet:-1,max-video-preview:-1',
    googleSiteVerification: process.env.GOOGLE_SITE_VERIFICATION || '',
    bingSiteVerification: process.env.BING_SITE_VERIFICATION || '',
    yandexVerification: process.env.YANDEX_VERIFICATION || '',
    // Core Web Vitals and Performance hints
    preloadResources: [
      { href: '/img/social-dance-share.jpg', as: 'image', type: 'image/jpeg' },
      { href: 'https://fonts.googleapis.com', rel: 'preconnect' },
      { href: 'https://fonts.gstatic.com', rel: 'preconnect', crossorigin: true }
    ],
    // Advanced entity-based SEO
    entityData: generateEntityData(path),
    // Professional schema enhancements
    organizationData: generateOrganizationStructuredData(baseUrl),
    // International SEO
    alternateLanguages: generateAlternateLanguages(path, baseUrl),
    // Advanced meta tags for professional SEO
    themeColor: '#1a365d',
    msapplicationTileColor: '#1a365d',
    appleMobileWebAppCapable: 'yes',
    appleMobileWebAppStatusBarStyle: 'black-translucent',
    // Security and privacy
    referrerPolicy: 'strict-origin-when-cross-origin',
    // Professional content categorization
    articleSection: determineArticleSection(path),
    contentRating: 'general',
    // Advanced social media optimization
    facebookAppId: process.env.FACEBOOK_APP_ID || '',
    twitterCreator: '@SocialDanceMoments',
    // Professional indexing directives
    googlebot: 'index,follow,max-image-preview:large,max-snippet:-1,max-video-preview:-1',
    bingbot: 'index,follow',
    // Content freshness signals
    publishedTime: getPublishedTime(path),
    modifiedTime: new Date().toISOString(),
    // Professional categorization
    category: determinePrimaryCategory(path),
    tags: generateContentTags(path)
  };
  
  // Route-specific metadata
  try {
    if (path === '/') {
      metadata = {
        ...metadata,
        title: 'Social Dance Moments - Home',
        description: 'Discover the best music for social dancing, create playlists, and connect with dancers worldwide.',
        keywords: 'social dance, bachata, salsa, kizomba, dance playlists, dance music, social dancer',
        ogTitle: 'Social Dance Moments - Home',
        ogDescription: 'Discover the best music for social dancing, create playlists, and connect with dancers worldwide.',
        twitterTitle: 'Social Dance Moments - Home',
        twitterDescription: 'Discover the best music for social dancing, create playlists, and connect with dancers worldwide.',
      };
    } 
    else if (path === '/login') {
      metadata = {
        ...metadata,
        title: 'Login - Social Dance Moments',
        description: 'Sign in to your Social Dance Moments account to access your playlists, suggestions, and preferences.',
        keywords: 'social dance login, dancer account, dance music account',
        ogTitle: 'Login - Social Dance Moments',
        ogDescription: 'Sign in to your Social Dance Moments account.',
        twitterTitle: 'Login - Social Dance Moments',
        twitterDescription: 'Sign in to your Social Dance Moments account.',
        noindex: true, // Prevent indexing of login page
      };
    }
    else if (path === '/signup') {
      metadata = {
        ...metadata,
        title: 'Sign Up - Social Dance Moments',
        description: 'Create a new account to join the Social Dance Moments community. Suggest songs, create playlists, and connect with dancers.',
        ogTitle: 'Sign Up - Social Dance Moments',
        ogDescription: 'Join the Social Dance Moments community.',
        twitterTitle: 'Sign Up - Social Dance Moments',
        twitterDescription: 'Join the Social Dance Moments community.',
        noindex: true, // Prevent indexing of signup page
      };
    }
    else if (path === '/full-top-voted') {
      metadata = {
        ...metadata,
        title: 'Top Voted Users - Social Dance Moments',
        description: 'Discover the dancers whose song suggestions are most popular in the community. Find new favorite dance songs from trusted users.',
        keywords: 'top dance songs, popular dance music, dance community, voted dance tracks, song suggestions',
        ogTitle: 'Top Voted Users - Social Dance Moments',
        ogDescription: 'Discover dancers with the most popular song suggestions in our community.',
        twitterTitle: 'Top Voted Users - Social Dance Moments',
        twitterDescription: 'Discover dancers with the most popular song suggestions in our community.',
      };
    }
    else if (path === '/leaderboard/all') {
      metadata = {
        ...metadata,
        title: 'Top Suggesters Leaderboard - Social Dance Moments',
        description: 'See our most active community members who contribute the most approved dance songs. Join them in building our dance music collection.',
        keywords: 'dance leaderboard, top suggesters, dance community, dance music contributors',
        ogTitle: 'Top Suggesters Leaderboard - Social Dance Moments',
        ogDescription: 'See our most active community members who contribute approved dance songs.',
        twitterTitle: 'Top Suggesters Leaderboard - Social Dance Moments',
        twitterDescription: 'See our most active community members who contribute approved dance songs.',
      };
    }
    else if (path.startsWith('/article/')) {
      // Extract article slug from path
      const slug = path.replace('/article/', '');
      
      // This is a placeholder. In a real implementation, you would fetch the article data from your database
      // Example: const article = await prisma.article.findUnique({ where: { slug } });
      
      // For now, we'll use hardcoded data based on the slug
      const articleData = getArticleData(slug);
      
      if (articleData) {
        metadata = {
          ...metadata,
          title: `${articleData.title} - Social Dance Moments`,
          description: articleData.description || articleData.excerpt,
          keywords: `${articleData.keywords || 'social dance, dance article'}, ${articleData.title.toLowerCase()}, dance tips, dancing guide`,
          ogTitle: articleData.title,
          ogDescription: articleData.description || articleData.excerpt,
          ogType: 'article',
          ogImage: articleData.image ? `${baseUrl}${articleData.image}` : metadata.ogImage,
          twitterTitle: articleData.title,
          twitterDescription: articleData.description || articleData.excerpt,
          twitterImage: articleData.image ? `${baseUrl}${articleData.image}` : metadata.twitterImage,
          structuredData: generateArticleStructuredData(articleData, baseUrl),
          publishedTime: articleData.publishedAt,
          modifiedTime: articleData.updatedAt,
          author: articleData.author,
          section: articleData.category || 'Dance Articles',
          tags: articleData.tags || ['social dance', 'dance guide', 'dance tips'],
          lastModified: articleData.updatedAt || new Date().toISOString()
        };
      }
    }
    else if (path.startsWith('/events')) {
      metadata = {
        ...metadata,
        title: 'Dance Events - Social Dance Moments',
        description: 'Find upcoming social dance events, workshops, and festivals. Connect with the dance community worldwide.',
        keywords: 'dance events, social dance festival, dance workshops, bachata festival, salsa congress, kizomba party, dance weekender, dance calendar, dance performances',
        ogTitle: 'Dance Events - Social Dance Moments',
        ogDescription: 'Find upcoming social dance events, workshops, and festivals.',
        twitterTitle: 'Dance Events - Social Dance Moments',
        twitterDescription: 'Find upcoming social dance events, workshops, and festivals.',
        structuredData: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "CollectionPage",
          "name": "Dance Events Calendar",
          "description": "Discover upcoming social dance events, workshops, and festivals around the world.",
          "url": canonicalUrl,
          "mainEntity": {
            "@type": "ItemList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Upcoming Dance Events",
                "url": `${baseUrl}/events`
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "Festivals",
                "url": `${baseUrl}/events/festivals`
              },
              {
                "@type": "ListItem",
                "position": 3,
                "name": "Workshops",
                "url": `${baseUrl}/events/workshops`
              }
            ]
          }
        })
      };
      
      // If this is a specific event page, you would fetch that event's data
      // Example: if (path.match(/^\/events\/[\w-]+$/)) { ... }
    }
    else if (path.startsWith('/clubs')) {
      metadata = {
        ...metadata,
        title: 'Dance Clubs - Social Dance Moments',
        description: 'Discover social dance clubs and venues worldwide. Find places to dance and connect with local dance communities.',
        keywords: 'dance clubs, social dance venues, dance studios, salsa clubs, bachata venues, kizomba parties, dance schools, dance academies, where to dance',
        ogTitle: 'Dance Clubs - Social Dance Moments',
        ogDescription: 'Discover social dance clubs and venues worldwide.',
        twitterTitle: 'Dance Clubs - Social Dance Moments',
        twitterDescription: 'Discover social dance clubs and venues worldwide.',
        structuredData: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "CollectionPage",
          "name": "Dance Clubs Directory",
          "description": "Comprehensive directory of social dance clubs, venues, and schools around the world.",
          "url": canonicalUrl,
          "mainEntity": {
            "@type": "ItemList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Dance Clubs",
                "url": `${baseUrl}/clubs`
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "Dance Schools",
                "url": `${baseUrl}/clubs?type=school`
              },
              {
                "@type": "ListItem",
                "position": 3,
                "name": "Dance Venues",
                "url": `${baseUrl}/clubs?type=venue`
              }
            ]
          }
        })
      };
      
      // If this is a specific club page, you would fetch that club's data
      // Example: if (path.match(/^\/clubs\/[\w-]+$/)) { ... }
    }
    else if (path.startsWith('/dancers')) {
      metadata = {
        ...metadata,
        title: 'Artist Dancers in Bulgaria - Social Dance Moments',
        description: 'Meet Bulgaria\'s talented dance instructors and performers who specialize in bachata, salsa, and kizomba. Discover workshops and performances by the country\'s leading dance artists.',
        keywords: 'dance instructors bulgaria, bachata dancers, salsa performers, kizomba teachers, dance workshops sofia, bulgarian dancers, dance artists, professional dancers, dance lessons bulgaria, latin dance bulgaria, dance shows, choreographers bulgaria, dance influencers, european dance artists',
        ogTitle: 'Artist Dancers in Bulgaria - Social Dance Moments',
        ogDescription: 'Meet Bulgaria\'s talented dance instructors and performers for bachata, salsa, and kizomba.',
        ogType: 'website',
        ogImage: `${baseUrl}/img/dancers/featured-dancers.jpg`,
        twitterTitle: 'Artist Dancers in Bulgaria - Social Dance Moments',
        twitterDescription: 'Meet Bulgaria\'s talented dance instructors and performers.',
        twitterImage: `${baseUrl}/img/dancers/featured-dancers.jpg`,
        hreflangs: [
          { lang: 'en', url: canonicalUrl },
          { lang: 'bg', url: `${baseUrl}/bg/dancers` },
          { lang: 'x-default', url: canonicalUrl }
        ],
        structuredData: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'CollectionPage',
          'name': 'Artist Dancers in Bulgaria',
          'description': 'Directory of Bulgaria\'s premier dance instructors and performers for bachata, salsa, and kizomba.',
          'url': canonicalUrl,
          'hasPart': [
            {
              '@type': 'ItemList',
              'itemListElement': [
                {
                  '@type': 'ListItem',
                  'position': 1,
                  'name': 'Bachata Dancers',
                  'url': `${baseUrl}/dancers#bachata-dancers`,
                  'description': 'Professional bachata dancers and instructors based in Bulgaria'
                },
                {
                  '@type': 'ListItem',
                  'position': 2,
                  'name': 'Salsa Dancers',
                  'url': `${baseUrl}/dancers#salsa-dancers`,
                  'description': 'Professional salsa dancers and instructors based in Bulgaria'
                },
                {
                  '@type': 'ListItem',
                  'position': 3,
                  'name': 'Kizomba Dancers',
                  'url': `${baseUrl}/dancers#kizomba-dancers`,
                  'description': 'Professional kizomba dancers and instructors based in Bulgaria'
                }
              ]
            }
          ],
          'mainEntityOfPage': {
            '@type': 'WebPage',
            '@id': canonicalUrl
          },
          'datePublished': '2025-05-01T12:00:00Z',
          'dateModified': new Date().toISOString()
        })
      };
    }
    else if (path === '/dancer') {
      metadata = {
        ...metadata,
        title: 'Dancer Playlist – Social Dance Party',
        description: 'Vote, suggest, and enjoy the live dance party playlist. Explore channels by dance style and join the community!',
        keywords: 'dancer, playlist, vote, suggest, social dance, bachata, salsa, kizomba, party, dance music, song voting, music suggestions, dance party playlist, live playlist',
        ogTitle: 'Dancer Playlist – Social Dance Party',
        ogDescription: 'Vote, suggest, and enjoy the live dance party playlist. Explore channels by dance style and join the community!',
        ogType: 'website',
        ogImage: `${baseUrl}/img/social-dance-share.jpg`,
        twitterTitle: 'Dancer Playlist – Social Dance Party',
        twitterDescription: 'Vote, suggest, and enjoy the live dance party playlist.',
        twitterImage: `${baseUrl}/img/social-dance-share.jpg`,
        structuredData: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebPage",
          "name": "Dancer Playlist – Social Dance Party",
          "description": "Vote, suggest, and enjoy the live dance party playlist. Explore channels by dance style and join the community!",
          "url": canonicalUrl,
          "potentialAction": {
            "@type": "ListenAction",
            "target": `${baseUrl}/public-playlist`
          },
          "mainEntity": {
            "@type": "MusicPlaylist",
            "name": "Social Dance Party Playlist",
            "description": "Live playlist for social dancing parties with various dance styles",
            "numTracks": "Varies",
            "genre": ["Bachata", "Salsa", "Kizomba"]
          }
        }),
      };
    }
    else if (path === '/djs') {
      metadata = {
        ...metadata,
        title: 'Top DJs in Bulgaria - Social Dance Moments',
        description: 'Discover Bulgaria\'s finest salsa, bachata, and kizomba DJs. Learn about the artists behind the music at dance events and festivals across the country.',
        keywords: 'bulgarian djs, salsa dj, bachata dj, kizomba dj, dance music, social dance, dj el vector, dj pzl, dj sofia, latin music djs, dance party djs, dj bookings bulgaria, event djs, club djs bulgaria, music selectors, latin music specialists, afro house djs, dance festival djs',
        ogTitle: 'Top DJs in Bulgaria - Social Dance Moments',
        ogDescription: 'Discover Bulgaria\'s finest salsa, bachata, and kizomba DJs who create the perfect atmosphere for dance events.',
        ogType: 'website',
        ogImage: `${baseUrl}/img/djs/featured-djs.jpg`,
        twitterTitle: 'Top DJs in Bulgaria - Social Dance Moments',
        twitterDescription: 'Discover Bulgaria\'s finest DJs for social dancing.',
        twitterImage: `${baseUrl}/img/djs/featured-djs.jpg`,
        hreflangs: [
          { lang: 'en', url: canonicalUrl },
          { lang: 'bg', url: `${baseUrl}/bg/djs` },
          { lang: 'x-default', url: canonicalUrl }
        ],
        structuredData: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'CollectionPage',
          'name': 'Top DJs in Bulgaria',
          'description': 'Directory of Bulgaria\'s best DJs for bachata, salsa, and kizomba music.',
          'url': canonicalUrl,
          'hasPart': [
            {
              '@type': 'ItemList',
              'itemListElement': [
                {
                  '@type': 'ListItem',
                  'position': 1,
                  'name': 'Bachata DJs',
                  'url': `${baseUrl}/djs#bachata-djs`,
                  'description': 'Talented DJs specializing in bachata music in Bulgaria'
                },
                {
                  '@type': 'ListItem',
                  'position': 2,
                  'name': 'Salsa DJs',
                  'url': `${baseUrl}/djs#salsa-djs`,
                  'description': 'Talented DJs specializing in salsa music in Bulgaria'
                },
                {
                  '@type': 'ListItem',
                  'position': 3,
                  'name': 'Kizomba DJs',
                  'url': `${baseUrl}/djs#kizomba-djs`,
                  'description': 'Talented DJs specializing in kizomba music in Bulgaria'
                }
              ]
            }
          ],
          'mainEntityOfPage': {
            '@type': 'WebPage',
            '@id': canonicalUrl
          },
          'datePublished': '2025-05-01T12:00:00Z',
          'dateModified': new Date().toISOString(),
          'author': {
            '@type': 'Organization',
            'name': 'Social Dance Moments',
            'url': baseUrl
          }
        })
      };
    }
    else if (path === '/singers') {
      metadata = {
        ...metadata,
        title: 'Artist Singers in Bulgaria - Social Dance Moments',
        description: 'Discover Bulgaria\'s talented vocalists who perform bachata, salsa, and kizomba music. Learn about the artists blending Bulgarian musical influences with Latin and African rhythms.',
        keywords: 'bulgarian singers, latin music bulgaria, bachata singers, salsa vocalists, kizomba artists, bulgarian music latin, bulgarian african fusion, bulgarian latin artists, vocal performers, music artists bulgaria, world music bulgaria, fusion music, contemporary bulgarian singers, live music performers, cultural music bulgaria, dance music vocalists',
        ogTitle: 'Artist Singers in Bulgaria - Social Dance Moments',
        ogDescription: 'Discover Bulgaria\'s talented vocalists who perform bachata, salsa, and kizomba music while blending local traditions with global rhythms.',
        ogType: 'website',
        ogImage: `${baseUrl}/img/singers/featured-singers.jpg`,
        twitterTitle: 'Artist Singers in Bulgaria - Social Dance Moments',
        twitterDescription: 'Discover Bulgarian artists who sing Latin and African rhythms.',
        twitterImage: `${baseUrl}/img/singers/featured-singers.jpg`,
        hreflangs: [
          { lang: 'en', url: canonicalUrl },
          { lang: 'bg', url: `${baseUrl}/bg/singers` },
          { lang: 'x-default', url: canonicalUrl }
        ],
        structuredData: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'CollectionPage',
          'name': 'Artist Singers in Bulgaria',
          'description': 'Directory of Bulgarian vocalists who perform bachata, salsa, and kizomba music.',
          'url': canonicalUrl,
          'hasPart': [
            {
              '@type': 'ItemList',
              'itemListElement': [
                {
                  '@type': 'ListItem',
                  'position': 1,
                  'name': 'Bachata Singers',
                  'url': `${baseUrl}/singers#bachata-singers`,
                  'description': 'Bulgarian vocalists specializing in bachata music'
                },
                {
                  '@type': 'ListItem',
                  'position': 2,
                  'name': 'Salsa Singers',
                  'url': `${baseUrl}/singers#salsa-singers`,
                  'description': 'Bulgarian vocalists specializing in salsa music'
                },
                {
                  '@type': 'ListItem',
                  'position': 3,
                  'name': 'Kizomba Singers',
                  'url': `${baseUrl}/singers#kizomba-singers`,
                  'description': 'Bulgarian vocalists specializing in kizomba music'
                }
              ]
            }
          ],
          'mainEntityOfPage': {
            '@type': 'WebPage',
            '@id': canonicalUrl
          },
          'publisher': {
            '@type': 'Organization',
            'name': 'Social Dance Moments',
            'logo': {
              '@type': 'ImageObject',
              'url': `${baseUrl}/img/logo.png`
            }
          },
          'datePublished': '2025-05-01T12:00:00Z',
          'dateModified': new Date().toISOString()
        })
      };
    }
    
    // Add more path-specific metadata here as needed
    
  } catch (error) {
    logger.error(`Error generating SEO metadata for path: ${path}`, { error });
    // Continue with default metadata
  }
  
  return metadata;
}

/**
 * Generates website structured data in JSON-LD format
 * @param {string} baseUrl - The base URL of the website
 * @returns {string} - JSON-LD structured data for the website
 */
function generateWebsiteStructuredData(baseUrl) {
  const data = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "url": baseUrl,
    "name": "Social Dance Moments",
    "description": "Share and discover social dance music with dancers worldwide.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": `${baseUrl}/search?q={search_term_string}`,
      "query-input": "required name=search_term_string"
    },
    "sameAs": [
      "https://www.instagram.com/socialdancemoments/",
      "https://www.facebook.com/groups/363227234024091",
      // Add more social profiles when available
    ],
    "creator": {
      "@type": "Organization",
      "name": "Social Dance Moments",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/img/logo.png`,
        "width": "180",
        "height": "60"
      },
      "foundingDate": "2025-01-01", // Update with actual founding date
      "foundingLocation": "Sofia, Bulgaria"
    },
    "copyrightYear": new Date().getFullYear(),
    "inLanguage": "en",
    "audience": {
      "@type": "Audience",
      "audienceType": "Social Dancers",
      "geographicArea": {
        "@type": "AdministrativeArea",
        "name": "Worldwide"
      }
    },
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock",
      "description": "Free account to access dance music playlists and social features"
    }
  };
  
  return JSON.stringify(data);
}

/**
 * Generates article structured data in JSON-LD format
 * @param {object} article - The article data
 * @param {string} baseUrl - The base URL of the website
 * @returns {string} - JSON-LD structured data for the article
 */
function generateArticleStructuredData(article, baseUrl) {
  const data = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": article.title,
    "description": article.description || article.excerpt,
    "image": article.image ? `${baseUrl}${article.image}` : `${baseUrl}/img/social-dance-share.jpg`,
    "datePublished": article.publishedAt || new Date().toISOString(),
    "dateModified": article.updatedAt || new Date().toISOString(),
    "author": {
      "@type": "Person",
      "name": article.author || "Social Dance Moments Team",
      "url": article.authorUrl ? `${baseUrl}${article.authorUrl}` : undefined
    },
    "publisher": {
      "@type": "Organization",
      "name": "Social Dance Moments",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/img/logo.png`,
        "width": "180",
        "height": "60"
      }
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${baseUrl}/article/${article.slug}`
    },
    "articleSection": article.category || "Dance",
    "keywords": article.keywords || "social dance, bachata, salsa, kizomba",
    "wordCount": article.wordCount || "1000+",
    "inLanguage": article.language || "en"
  };
  
  // Add FAQ if available
  if (article.faqs && Array.isArray(article.faqs) && article.faqs.length > 0) {
    data.mainEntity = {
      "@type": "FAQPage",
      "mainEntity": article.faqs.map(faq => ({
        "@type": "Question",
        "name": faq.question,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": faq.answer
        }
      }))
    };
  }
  
  return JSON.stringify(data);
}

/**
 * Generates FAQPage structured data in JSON-LD format
 * @param {Array} faqs - Array of FAQ objects with question and answer properties
 * @param {string} baseUrl - The base URL of the website
 * @returns {string} - JSON-LD structured data for the FAQPage
 */
function generateFAQStructuredData(faqs, baseUrl) {
  if (!Array.isArray(faqs) || faqs.length === 0) {
    return null;
  }
  
  const data = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
  
  return JSON.stringify(data);
}

/**
 * Generates breadcrumb structured data in JSON-LD format
 * @param {string} path - The current request path
 * @param {string} baseUrl - The base URL of the website
 * @returns {string} - JSON-LD structured data for breadcrumbs
 */
function generateBreadcrumbStructuredData(path, baseUrl) {
  // Split the path into segments
  const segments = path.split('/').filter(segment => segment.length > 0);
  
  if (segments.length === 0) {
    // For homepage, no breadcrumbs needed
    return null;
  }
  
  const breadcrumbList = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": []
  };
  
  // Always add home as the first item
  breadcrumbList.itemListElement.push({
    "@type": "ListItem",
    "position": 1,
    "name": "Home",
    "item": baseUrl
  });
  
  let currentPath = '';
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];
    currentPath += `/${segment}`;
    
    // Generate human-readable name from the segment
    let name = segment
      .replace(/-/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase()); // Capitalize first letter of each word
    
    // Special case for known path types
    if (segment === 'article' && i < segments.length - 1) {
      name = 'Articles';
    } else if (segment === 'article' && i === segments.length - 1) {
      // Skip this segment as the next one will be the article title
      continue;
    } else if (currentPath.startsWith('/article/')) {
      // For article pages, get the actual article title
      const slug = segment;
      const articleData = getArticleData(slug);
      if (articleData) {
        name = articleData.title;
      }
    }
    
    breadcrumbList.itemListElement.push({
      "@type": "ListItem",
      "position": i + 2, // +2 because home is position 1
      "name": name,
      "item": `${baseUrl}${currentPath}`
    });
  }
  
  return JSON.stringify(breadcrumbList);
}

/**
 * Gets the base URL for the application, ensuring consistent canonical URLs
 * @param {object} req - Express request object
 * @returns {string} - The base URL
 */
function getBaseUrl(req) {
  if (!req) return 'https://www.social-dance.org';
  
  // In production, always use the canonical domain
  if (process.env.NODE_ENV === 'production') {
    return 'https://www.social-dance.org';
  }
  
  // For development, use the request headers but ensure consistency
  const protocol = req.headers['x-forwarded-proto'] || req.protocol || 'http';
  const host = req.headers['x-forwarded-host'] || req.headers.host || 'localhost:3001';
  
  // Ensure we always use https in production-like environments
  const finalProtocol = host.includes('social-dance.org') ? 'https' : protocol;
  
  return `${finalProtocol}://${host}`;
}

/**
 * Gets article data based on slug (placeholder implementation)
 * @param {string} slug - The article slug
 * @returns {object|null} - Article data or null if not found
 */
function getArticleData(slug) {
  // This is a placeholder. In a real implementation, you would fetch the article data from your database
  
  // Example hardcoded data for testing
  const articles = {
    'salsa-history': {
      title: 'The Rich History of Salsa Dancing',
      slug: 'salsa-history',
      excerpt: 'Explore the fascinating origins and evolution of salsa dancing through the decades.',
      description: 'Discover the origins of salsa dancing from its roots in Cuban Son, New York Latin music scene, and how it evolved into the global phenomenon it is today.',
      image: '/img/articles/salsa-history.jpg',
      author: 'Maria Rodriguez',
      publishedAt: '2025-04-15T14:30:00Z',
      updatedAt: '2025-04-17T09:15:00Z'
    },
    'bachata-moves': {
      title: 'Essential Bachata Moves for Beginners',
      slug: 'bachata-moves',
      excerpt: 'Learn the fundamental bachata steps and techniques to get you started on the dance floor.',
      description: 'Master the basic bachata steps, timing, and movement techniques. This guide covers everything beginners need to know to start dancing bachata confidently.',
      image: '/img/articles/bachata-moves.jpg',
      author: 'Carlos Sanchez',
      publishedAt: '2025-04-22T10:45:00Z',
      updatedAt: '2025-04-22T10:45:00Z'
    },
    'dance-festival': {
      title: 'Top Social Dance Festivals Around the World',
      slug: 'dance-festival',
      excerpt: 'Discover the most exciting social dance festivals and events happening worldwide.',
      description: 'A comprehensive guide to the best salsa, bachata, kizomba and other social dance festivals across the globe. Plan your dance travel adventure with our top recommendations.',
      image: '/img/articles/dance-festival.jpg',
      author: 'Sophie Johnson',
      publishedAt: '2025-05-01T16:20:00Z',
      updatedAt: '2025-05-03T11:30:00Z'
    },
    'dance-music': {
      title: 'How to Choose Great Dance Music for Your Event',
      slug: 'dance-music',
      excerpt: 'Tips for DJs and organizers on selecting the perfect music for social dance events.',
      description: 'Learn how to create the perfect playlist for social dance events. This guide covers music selection, BPM considerations, and how to keep dancers engaged all night long.',
      image: '/img/articles/dance-music.jpg',
      author: 'DJ Roberto',
      publishedAt: '2025-05-08T13:10:00Z',
      updatedAt: '2025-05-08T13:10:00Z'
    }
  };
  
  return articles[slug] || null;
}

/**
 * Generates event structured data in JSON-LD format
 * @param {object} event - The event data
 * @param {string} baseUrl - The base URL of the website
 * @returns {string} - JSON-LD structured data for the event
 */
function generateEventStructuredData(event, baseUrl) {
  const data = {
    "@context": "https://schema.org",
    "@type": "Event",
    "name": event.name,
    "description": event.description,
    "image": event.image ? `${baseUrl}${event.image}` : `${baseUrl}/img/events/default-event.jpg`,
    "startDate": event.startDate,
    "endDate": event.endDate,
    "eventStatus": event.status || "https://schema.org/EventScheduled",
    "eventAttendanceMode": event.attendanceMode || "https://schema.org/OfflineEventAttendanceMode",
    "location": {
      "@type": "Place",
      "name": event.venue.name,
      "address": {
        "@type": "PostalAddress",
        "streetAddress": event.venue.address.street,
        "addressLocality": event.venue.address.city,
        "addressRegion": event.venue.address.region,
        "postalCode": event.venue.address.postalCode,
        "addressCountry": event.venue.address.country
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": event.venue.coordinates?.latitude,
        "longitude": event.venue.coordinates?.longitude
      }
    },
    "organizer": {
      "@type": "Organization",
      "name": event.organizer.name,
      "url": event.organizer.url
    },
    "offers": {
      "@type": "Offer",
      "price": event.price?.amount || "0",
      "priceCurrency": event.price?.currency || "EUR",
      "availability": event.availability || "https://schema.org/InStock",
      "validFrom": event.validFrom || event.createdAt,
      "url": event.ticketUrl || `${baseUrl}/events/${event.slug}`
    },
    "performer": event.performers?.map(performer => ({
      "@type": "Person",
      "name": performer.name,
      "url": performer.url
    })) || [],
    "keywords": event.keywords || ["social dance", "dance event", "dance party"],
    "typicalAgeRange": event.ageRange || "18+",
    "inLanguage": event.language || "en"
  };
  
  return JSON.stringify(data);
}

/**
 * Generates events calendar structured data in JSON-LD format
 * @param {Array} events - Array of event objects
 * @param {string} baseUrl - The base URL of the website
 * @returns {string} - JSON-LD structured data for the events calendar
 */
function generateEventsCalendarStructuredData(events, baseUrl) {
  if (!Array.isArray(events) || events.length === 0) {
    return null;
  }
  
  const data = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "Dance Events Calendar",
    "description": "Social dance events, festivals, and workshops",
    "url": `${baseUrl}/events`,
    "hasPart": events.map((event, index) => ({
      "@type": "Event",
      "name": event.name,
      "description": event.description,
      "startDate": event.startDate,
      "endDate": event.endDate,
      "location": {
        "@type": "Place",
        "name": event.venue.name,
        "address": {
          "@type": "PostalAddress",
          "addressLocality": event.venue.address.city,
          "addressCountry": event.venue.address.country
        }
      },
      "url": `${baseUrl}/events/${event.slug}`
    }))
  };
  
  return JSON.stringify(data);
}

module.exports = {
  generateSeoMetadata,
  generateWebsiteStructuredData,
  generateArticleStructuredData,
  generateBreadcrumbStructuredData,
  generateLocalBusinessStructuredData,
  generateFAQStructuredData,
  generateEventStructuredData,
  generateEventsCalendarStructuredData
};

/**
 * Generates structured data for a local business (dance school or venue)
 * @param {object} business - The business data
 * @param {string} baseUrl - The base URL of the website
 * @returns {string} - JSON-LD structured data for the local business
 */
function generateLocalBusinessStructuredData(business, baseUrl) {
  const data = {
    "@context": "https://schema.org",
    "@type": "DanceSchool", // or "LocalBusiness"
    "name": business.name,
    "description": business.description,
    "url": `${baseUrl}/clubs/${business.slug}`,
    "telephone": business.telephone,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": business.address.street,
      "addressLocality": business.address.city,
      "addressRegion": business.address.region,
      "postalCode": business.address.postalCode,
      "addressCountry": business.address.country
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": business.coordinates?.latitude,
      "longitude": business.coordinates?.longitude
    },
    "image": business.image ? `${baseUrl}${business.image}` : `${baseUrl}/img/default-club.jpg`,
    "priceRange": business.priceRange || "$$",
    "openingHoursSpecification": business.openingHours || [],
    "sameAs": business.socialLinks || []
  };
  
  return JSON.stringify(data);
}

/**
 * Generates entity data for advanced entity-based SEO
 * @param {string} path - The current request path
 * @returns {object} - Entity data for the page
 */
function generateEntityData(path) {
  const entityMap = {
    '/': {
      primaryEntity: 'Social Dance Community',
      entityType: 'Organization',
      relatedEntities: ['Bachata', 'Salsa', 'Kizomba', 'Dance Music', 'Social Dancing']
    },
    '/dancers': {
      primaryEntity: 'Dance Instructors Bulgaria',
      entityType: 'Person',
      relatedEntities: ['Professional Dancers', 'Dance Teachers', 'Choreographers', 'Bulgaria']
    },
    '/djs': {
      primaryEntity: 'DJs Bulgaria',
      entityType: 'Person',
      relatedEntities: ['Music Selectors', 'Dance Music DJs', 'Event DJs', 'Bulgaria']
    },
    '/singers': {
      primaryEntity: 'Singers Bulgaria',
      entityType: 'Person',
      relatedEntities: ['Vocalists', 'Latin Music Artists', 'Bulgarian Artists', 'Bulgaria']
    },
    '/events': {
      primaryEntity: 'Dance Events',
      entityType: 'Event',
      relatedEntities: ['Dance Festivals', 'Workshops', 'Social Dance Parties', 'Bulgaria']
    },
    '/clubs': {
      primaryEntity: 'Dance Venues',
      entityType: 'Place',
      relatedEntities: ['Dance Schools', 'Dance Studios', 'Social Dance Venues', 'Bulgaria']
    }
  };

  return entityMap[path] || {
    primaryEntity: 'Social Dance',
    entityType: 'Thing',
    relatedEntities: ['Dance', 'Music', 'Community']
  };
}

/**
 * Generates organization structured data
 * @param {string} baseUrl - The base URL of the website
 * @returns {string} - JSON-LD structured data for the organization
 */
function generateOrganizationStructuredData(baseUrl) {
  const data = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Social Dance Moments",
    "url": baseUrl,
    "logo": {
      "@type": "ImageObject",
      "url": `${baseUrl}/img/logo.png`,
      "width": "180",
      "height": "60"
    },
    "description": "Premier social dance community platform connecting dancers worldwide through music, events, and shared experiences.",
    "foundingDate": "2025-01-01",
    "foundingLocation": {
      "@type": "Place",
      "name": "Sofia, Bulgaria"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["English", "Bulgarian"]
    },
    "sameAs": [
      "https://www.facebook.com/groups/363227234024091",
      "https://www.instagram.com/socialdancemoments/"
    ],
    "knowsAbout": [
      "Social Dancing",
      "Bachata",
      "Salsa", 
      "Kizomba",
      "Dance Music",
      "Dance Events",
      "Dance Education"
    ],
    "areaServed": {
      "@type": "Place",
      "name": "Worldwide"
    }
  };
  
  return JSON.stringify(data);
}

/**
 * Generates alternate language versions for international SEO
 * @param {string} path - The current request path
 * @param {string} baseUrl - The base URL of the website
 * @returns {Array} - Array of alternate language objects
 */
function generateAlternateLanguages(path, baseUrl) {
  return [
    { lang: 'en', url: `${baseUrl}${path}` },
    { lang: 'bg', url: `${baseUrl}/bg${path}` },
    { lang: 'x-default', url: `${baseUrl}${path}` }
  ];
}

/**
 * Determines the article section for content categorization
 * @param {string} path - The current request path
 * @returns {string} - Article section
 */
function determineArticleSection(path) {
  if (path.startsWith('/article/')) return 'Dance Articles';
  if (path.startsWith('/events')) return 'Events';
  if (path.startsWith('/dancers')) return 'Community';
  if (path.startsWith('/djs')) return 'Music';
  if (path.startsWith('/singers')) return 'Artists';
  if (path.startsWith('/clubs')) return 'Venues';
  return 'General';
}

/**
 * Gets the published time for content freshness
 * @param {string} path - The current request path
 * @returns {string} - ISO date string
 */
function getPublishedTime(path) {
  // In a real implementation, this would fetch from database
  const publishedDates = {
    '/article/salsa-history': '2025-04-15T14:30:00Z',
    '/article/bachata-moves': '2025-04-22T10:45:00Z',
    '/article/dance-festival': '2025-05-01T16:20:00Z',
    '/article/dance-music': '2025-05-08T13:10:00Z'
  };
  
  return publishedDates[path] || '2025-01-01T12:00:00Z';
}

/**
 * Determines the primary category for the page
 * @param {string} path - The current request path
 * @returns {string} - Primary category
 */
function determinePrimaryCategory(path) {
  if (path.startsWith('/article/')) return 'Education';
  if (path.startsWith('/events')) return 'Events';
  if (path.startsWith('/dancers')) return 'Community';
  if (path.startsWith('/djs')) return 'Music';
  if (path.startsWith('/singers')) return 'Artists';
  if (path.startsWith('/clubs')) return 'Venues';
  if (path === '/dancer') return 'Interactive';
  return 'General';
}

/**
 * Generates content tags for better categorization
 * @param {string} path - The current request path
 * @returns {Array} - Array of content tags
 */
function generateContentTags(path) {
  const baseTags = ['social dance', 'dance community', 'Bulgaria'];
  
  if (path.startsWith('/article/')) {
    return [...baseTags, 'dance education', 'dance tips', 'dance guide'];
  }
  if (path.startsWith('/events')) {
    return [...baseTags, 'dance events', 'festivals', 'workshops'];
  }
  if (path.startsWith('/dancers')) {
    return [...baseTags, 'dance instructors', 'professional dancers', 'choreographers'];
  }
  if (path.startsWith('/djs')) {
    return [...baseTags, 'dance music', 'djs', 'music selectors'];
  }
  if (path.startsWith('/singers')) {
    return [...baseTags, 'vocalists', 'latin music', 'artists'];
  }
  if (path.startsWith('/clubs')) {
    return [...baseTags, 'dance venues', 'dance schools', 'studios'];
  }
  
  return baseTags;
} 