const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const gameService = require('../services/gameService');
const battleService = require('../services/battleService');

const prisma = new PrismaClient();

// Setup WebSocket handlers for battle events
const setupBattleHandlers = (io, socket, user) => {
  logger.debug(`Setting up battle handlers for user ${user.id} (${user.username})`);
  
  // Join a specific battle room
  socket.on('join-battle', (battleId) => {
    logger.debug(`User ${user.id} (${user.username}) joined battle ${battleId}`);
    socket.join(`battle_${battleId}`);
  });
  
  // Leave a battle room
  socket.on('leave-battle', (battleId) => {
    logger.debug(`User ${user.id} (${user.username}) left battle ${battleId}`);
    socket.leave(`battle_${battleId}`);
  });
  
  // Send an emote in a battle
  socket.on('send-emote', async ({ battleId, emoji }) => {
    logger.debug(`User ${user.id} (${user.username}) sent emote ${emoji} in battle ${battleId}`);
    
    try {
      // Validate emoji (simplified validation)
      if (!emoji || typeof emoji !== 'string' || emoji.length > 10) {
        return socket.emit('error', { message: 'Invalid emoji' });
      }
      
      // Get hero info
      const hero = await prisma.hero.findUnique({
        where: { userId: user.id },
        select: { id: true, name: true }
      });
      
      if (!hero) {
        return socket.emit('error', { message: 'You need a hero to send emotes' });
      }
      
      // Broadcast to the battle room
      io.to(`battle_${battleId}`).emit('battle-emote', {
        heroId: hero.id,
        heroName: hero.name,
        userId: user.id,
        username: user.username,
        emoji
      });
      
    } catch (error) {
      logger.error('Error sending emote via WebSocket', { error: error.message, stack: error.stack });
      socket.emit('error', { message: 'Failed to send emote' });
    }
  });
  
  // Select a move for turn-based battle
  socket.on('select-move', async ({ battleId, moveId, timingScore = 0 }) => {
    logger.debug(`User ${user.id} (${user.username}) selected move ${moveId} with timing ${timingScore} in battle ${battleId}`);
    
    try {
      // Get hero info
      const hero = await prisma.hero.findUnique({
        where: { userId: user.id }
      });
      
      if (!hero) {
        return socket.emit('error', { message: 'You need a hero to battle' });
      }
      
      // In a real implementation, we would:
      // 1. Validate that this is an active battle
      // 2. Validate that it's this user's turn
      // 3. Process the move
      
      // For now, just broadcast the move selection
      io.to(`battle_${battleId}`).emit('move-selected', {
        heroId: hero.id,
        moveId,
        timingScore
      });
      
      // Log the timing score for quest progress
      if (timingScore >= 80) {
        await gameService.updateQuestProgress(hero.id, 'TIMING');
      }
      
      // Log the move type for quest progress
      const move = await prisma.battleMove.findUnique({
        where: { id: moveId }
      });
      
      if (move) {
        // Update quest progress for using specific move types
        if (move.type === 'SHINE') {
          await gameService.updateQuestProgress(hero.id, 'USE_SHINE');
        } else if (move.type === 'TURN') {
          await gameService.updateQuestProgress(hero.id, 'USE_TURN');
        }
      }
      
    } catch (error) {
      logger.error('Error processing move selection via WebSocket', { 
        error: error.message, 
        stack: error.stack,
        userId: user.id,
        battleId,
        moveId
      });
      socket.emit('error', { message: 'Failed to process move selection' });
    }
  });
};

module.exports = {
  setupBattleHandlers
}; 