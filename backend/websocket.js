const jwt = require('jsonwebtoken');
const logger = require('./utils/logger');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { setupBattleHandlers } = require('./websocket/battleHandler');

let ioInstance = null;
let lastEmittedPlayerState = {}; // Cache for comparison (optional for future optimization)

// --- Module-Level GetIO ---
// Moved getIo definition higher up for clarity
const getIo = () => {
    if (!ioInstance) {
        // Log a warning instead of throwing, as it might be called before setup in some init flows
        // Or during tests. Callers should handle null possibility if needed.
        logger.warn("Socket.IO instance requested before initialization!");
        return null;
    }
    return ioInstance;
}

// --- Helper DTO Functions (Module Scope) ---
const createSuggestionDTO = (suggestion) => {
  if (!suggestion) return null;
  // Return only essential fields needed by most clients
  return {
    id: suggestion.id,
    title: suggestion.title,
    youtubeVideoId: suggestion.youtubeVideoId,
    thumbnailUrl: suggestion.thumbnailUrl,
    danceStyle: suggestion.danceStyle,
    status: suggestion.status,
    votes: suggestion.votes,
    isLocked: suggestion.isLocked,
    durationSeconds: suggestion.durationSeconds,
    userId: suggestion.userId,
    username: suggestion.user?.username, // Safely access username
    createdAt: suggestion.createdAt,
    updatedAt: suggestion.updatedAt,
    // Excluded: channelTitle, full user object (profile, achievements, etc.)
  };
};


// --- Module-Level Player State Emitter Functions ---
// These now use getIo() internally

const emitCurrentSongChanged = (state) => {
    const io = getIo();
    if (!io) return;
    const payload = {
        currentSong: state.currentSong,
        nextSong: state.upcomingSongs.length > 0 ? state.upcomingSongs[0] : null,
        currentSongStartedAt: state.currentSongStartedAt?.toISOString(),
        durationSeconds: state.currentSong?.durationSeconds,
        lastUpdatedAt: state.lastUpdatedAt // Keep this for sync
    };
    logger.info('Emitting player:song:changed to public room', { songId: payload.currentSong?.id });
    io.to('public').emit('player:song:changed', payload);
};

const emitPlaybackStatusChanged = (state) => {
    const io = getIo();
    if (!io) return;
     const payload = {
        isPlaying: state.isPlaying,
        currentSongProgressSeconds: state.currentSongProgressSeconds,
        lastUpdatedAt: state.lastUpdatedAt
    };
    logger.info('Emitting player:status:changed to public room', { isPlaying: payload.isPlaying });
    io.to('public').emit('player:status:changed', payload);
};

const emitPlaylistStructureChanged = (state) => {
    const io = getIo();
    if (!io) return;
     const payload = {
        upcomingSongs: state.upcomingSongs,
        lastUpdatedAt: state.lastUpdatedAt
    };
    logger.info('Emitting player:playlist:changed to public room', { upcomingCount: payload.upcomingSongs.length });
    io.to('public').emit('player:playlist:changed', payload);
};

const emitFullPlayerState = (state, targetSocket = null) => {
     const io = getIo();
     if (!io) return;
     logger.info(`Emitting player:state:full_update`, { target: targetSocket ? targetSocket.id : 'public room' });
     if (targetSocket) {
         targetSocket.emit('player:state:full_update', state);
     } else {
         io.to('public').emit('player:state:full_update', state);
     }
     lastEmittedPlayerState = { ...state }; // Update cache after full emit
}


// --- Module-Level Core State Fetching and Orchestrating Emitter ---
const fetchAndEmitPlayerState = async (sourceAction = 'unknown', forceFullUpdate = false) => {
    const io = getIo();
    if (!io) {
        logger.warn('Cannot fetch/emit player state update: WebSocket instance not available.');
        return;
    }
    logger.debug(`Fetching latest player state (triggered by: ${sourceAction})`);
    try {
        // Fetch the latest state directly from the database
        const activePlaylist = await prisma.activePlaylist.findFirst({
            orderBy: { createdAt: 'desc' },
            include: {
                playlistItems: {
                    orderBy: { order: 'asc' },
                    select: {
                        id: true,
                        youtubeVideoId: true,
                        title: true,
                        thumbnailUrl: true,
                        channelTitle: true,
                        durationSeconds: true,
                        order: true,
                    }
                }
            }
        });

        let currentPlayerState = {}; // Default empty state

        if (!activePlaylist || !activePlaylist.youtubePlaylistId) {
            logger.info('fetchAndEmitPlayerState: No active playlist found or youtubePlaylistId missing.');
            currentPlayerState = {
                youtubePlaylistId: null,
                currentSong: null,
                upcomingSongs: [],
                isPlaying: false,
                currentSongStartedAt: null,
                currentSongProgressSeconds: 0,
                lastUpdatedAt: new Date().toISOString(),
            };
        } else {
            const items = activePlaylist.playlistItems || [];
            const currentIndex = activePlaylist.currentSongIndex ?? -1;
            const currentSong = (currentIndex >= 0 && currentIndex < items.length) ? items[currentIndex] : null;
            const upcomingSongs = (currentIndex >= -1 && items.length > 0) 
                ? items.slice(currentIndex + 1, currentIndex + 1 + 6) // Send next 5 (+1 for emitCurrentSongChanged)
                : [];
            
            let estimatedProgressSeconds = 0;
            if (activePlaylist.isPlaying && currentSong && activePlaylist.currentSongStartedAt && currentSong.durationSeconds && currentSong.durationSeconds > 0) {
                const elapsedMs = Date.now() - activePlaylist.currentSongStartedAt.getTime();
                estimatedProgressSeconds = Math.max(0, Math.min(currentSong.durationSeconds, Math.floor(elapsedMs / 1000)));
                if (estimatedProgressSeconds >= currentSong.durationSeconds) {
                     estimatedProgressSeconds = currentSong.durationSeconds;
                }
            }

            currentPlayerState = {
                youtubePlaylistId: activePlaylist.youtubePlaylistId,
                currentSong: currentSong,
                upcomingSongs: upcomingSongs,
                isPlaying: activePlaylist.isPlaying ?? false,
                currentSongStartedAt: activePlaylist.currentSongStartedAt,
                currentSongProgressSeconds: estimatedProgressSeconds,
                lastUpdatedAt: activePlaylist.updatedAt.toISOString(),
            };
        }

        if (forceFullUpdate) {
            emitFullPlayerState(currentPlayerState);
        } else {
            // Always emit granular updates for now
            emitCurrentSongChanged(currentPlayerState);
            emitPlaybackStatusChanged(currentPlayerState);
            emitPlaylistStructureChanged(currentPlayerState);
        }

    } catch (err) {
        logger.error('Error fetching or emitting player state update:', { error: err.message, stack: err.stack });
    }
  };

// Helper to fetch and emit full state, especially for new connections
// Now calls the module-level emitFullPlayerState
const fetchAndEmitFullPlayerState = async (targetSocket = null) => {
      logger.debug(`Fetching full player state for ${targetSocket ? `socket ${targetSocket.id}` : 'public broadcast'}`);
      try {
          const activePlaylist = await prisma.activePlaylist.findFirst({
              orderBy: { createdAt: 'desc' },
              include: {
                  playlistItems: {
                      orderBy: { order: 'asc' },
                      select: {
                          id: true, youtubeVideoId: true, title: true, thumbnailUrl: true,
                          channelTitle: true, durationSeconds: true, order: true,
                      }
                  }
              }
          });

          let fullState = {};
          if (!activePlaylist || !activePlaylist.youtubePlaylistId) {
              fullState = {
                  youtubePlaylistId: null, currentSong: null, upcomingSongs: [], isPlaying: false,
                  currentSongStartedAt: null, currentSongProgressSeconds: 0, lastUpdatedAt: new Date().toISOString(),
              };
          } else {
              const items = activePlaylist.playlistItems || [];
              const currentIndex = activePlaylist.currentSongIndex ?? -1;
              const currentSong = (currentIndex >= 0 && currentIndex < items.length) ? items[currentIndex] : null;
              const upcomingSongs = (currentIndex >= -1 && items.length > 0)
                  ? items.slice(currentIndex + 1, currentIndex + 1 + 6) : [];

              let estimatedProgressSeconds = 0;
              if (activePlaylist.isPlaying && currentSong && activePlaylist.currentSongStartedAt && currentSong.durationSeconds > 0) {
                  const elapsedMs = Date.now() - activePlaylist.currentSongStartedAt.getTime();
                  estimatedProgressSeconds = Math.max(0, Math.min(currentSong.durationSeconds, Math.floor(elapsedMs / 1000)));
              }

              fullState = {
                  youtubePlaylistId: activePlaylist.youtubePlaylistId, currentSong: currentSong,
                  upcomingSongs: upcomingSongs, isPlaying: activePlaylist.isPlaying ?? false,
                  currentSongStartedAt: activePlaylist.currentSongStartedAt,
                  currentSongProgressSeconds: estimatedProgressSeconds,
                  lastUpdatedAt: activePlaylist.updatedAt.toISOString(),
              };
          }
          // Call the module-level emitter
          emitFullPlayerState(fullState, targetSocket);

      } catch (err) {
          logger.error('Error fetching full player state:', { error: err.message, stack: err.stack });
      }
};


// --- Module-Level Trigger Function (Exported) ---
// This is the function that services/routes should call
const triggerPlayerStateUpdate = async (sourceAction = 'unknown') => {
    // Calls the module-level fetch/emit logic
    await fetchAndEmitPlayerState(sourceAction, false); // Normally triggers granular updates
};

// --- Module-Level Emitters for Specific Non-Player Events ---
// These also use getIo()

const emitCommentCreated = (suggestionId, comment) => {
    const io = getIo();
    if (!io) return;
    logger.debug(`Emitting comment:created to suggestion room ${suggestionId} and admin room`);
    io.to(`suggestion:${suggestionId}:comments`).emit('comment:created', comment);
    io.to('admin').emit('comment:created', comment); // Also notify admin?
};

const emitCommentDeleted = (suggestionId, commentId) => {
    const io = getIo();
    if (!io) return;
    logger.debug(`Emitting comment:deleted for comment ${commentId} in suggestion room ${suggestionId} and admin room`);
    io.to(`suggestion:${suggestionId}:comments`).emit('comment:deleted', { commentId });
    io.to('admin').emit('comment:deleted', { commentId });
};

const emitSuggestionCreated = (suggestion) => {
    const io = getIo();
    if (!io) return;
    const suggestionDTO = createSuggestionDTO(suggestion); // Create DTO
    if (!suggestionDTO) return; // Don't emit if DTO creation failed
    logger.debug(`Emitting suggestion:created (DTO)`, { suggestionId: suggestionDTO.id });
    io.to('admin').emit('suggestion:created', suggestionDTO);
    io.to('suggestions').emit('suggestion:created', suggestionDTO);
};

const emitSuggestionUpdated = (suggestion) => {
    const io = getIo();
    if (!io) return;
    const suggestionDTO = createSuggestionDTO(suggestion); // Create DTO
    if (!suggestionDTO) return; // Don't emit if DTO creation failed
    logger.debug(`Emitting suggestion:updated (DTO)`, { suggestionId: suggestionDTO.id });
    io.to('admin').emit('suggestion:updated', suggestionDTO);
    io.to('suggestions').emit('suggestion:updated', suggestionDTO);
};

const emitSuggestionVote = (voteData) => { // voteData = { suggestionId, votes }
    const io = getIo();
    if (!io) return;
    logger.debug(`Emitting suggestion:vote`);
    io.to('admin').emit('suggestion:vote', voteData);
    io.to('suggestions').emit('suggestion:vote', voteData);
};

const emitUserCreditsUpdate = (userId, credits) => {
    const io = getIo();
    if (!io) return;
    logger.debug(`Emitting user:creditsUpdated to user ${userId}`);
    io.to(`user:${userId}`).emit('user:creditsUpdated', { userId, credits });
};

const emitAchievementUnlocked = (userId, achievement) => {
    const io = getIo();
    if (!io) return;
    logger.debug(`Emitting achievement:unlocked to user ${userId}`);
    io.to(`user:${userId}`).emit('achievement:unlocked', achievement);
};


// --- WebSocket Setup Function (Exported) ---
const setupWebSocketHandlers = (io) => {
  ioInstance = io;
  logger.info('WebSocket instance initialized.');

  // Middleware to authenticate WebSocket connections if token is provided
  io.use(async (socket, next) => {
    const token = socket.handshake.auth.token;
    socket.user = null; // Initialize user as null
    socket.userId = null;

    if (token) { // Only attempt verification if a token exists
        try {
            logger.debug(`WS Attempting auth for socket ${socket.id} with token`);
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            socket.userId = decoded.userId;
            socket.user = decoded; // Attach decoded payload
            
            // Fetch full user data for battle features
            try {
              const userWithProfile = await prisma.user.findUnique({
                where: { id: decoded.userId }
              });
              if (userWithProfile) {
                socket.userDetails = userWithProfile;
              }
            } catch (err) {
              logger.warn(`Failed to load full user profile for WebSocket: ${err.message}`);
            }
            
            logger.info(`WS Client authenticated: ${socket.id}, UserID: ${socket.userId}, Role: ${socket.user.role}`);
        } catch (error) {
            logger.warn(`WebSocket authentication failed for token: ${error.message}`, { socketId: socket.id });
        }
    } else {
        logger.debug(`WS Client connecting without token: ${socket.id}`);
    }
    next();
  });

  io.on('connection', (socket) => {
    logger.info(`Client connected: ${socket.id}, Authenticated: ${!!socket.user}`);

    socket.join('public');
    logger.debug(`Socket ${socket.id} joined public room`);

    if (socket.user && socket.userId) {
        socket.join(`user:${socket.userId}`);
        logger.debug(`Socket ${socket.id} joined user room: user:${socket.userId}`);
        if (socket.user.role === 'ADMIN') {
            socket.join('admin');
            logger.debug(`Socket ${socket.id} joined admin room`);
            
            // Admin-specific socket event handlers
            socket.on('admin:sync_full_playlist_state', async () => {
                logger.info(`Admin ${socket.userId} requested full playlist state refresh`);
                try {
                    // Force a full refresh of the player state with fresh data from database
                    await fetchAndEmitPlayerState('admin-manual-refresh', true);
                    // Indicate success to the requester
                    socket.emit('notification', { 
                        message: 'Playlist state refreshed successfully', 
                        type: 'SUCCESS' 
                    });
                } catch (error) {
                    logger.error('Failed to refresh playlist state:', error);
                    socket.emit('notification', { 
                        message: 'Failed to refresh playlist state', 
                        type: 'ERROR' 
                    });
                }
            });
        }
        // Send full state directly to the new authenticated socket
        logger.info(`Emitting initial full player state to auth socket ${socket.id}`);
        fetchAndEmitFullPlayerState(socket);
        
        // Setup battle handlers if user details are available
        if (socket.userDetails) {
          setupBattleHandlers(io, socket, socket.userDetails);
          logger.debug(`Battle handlers setup for socket ${socket.id}`);
        }

    } else {
        logger.debug(`Socket ${socket.id} is unauthenticated, not joining user/admin rooms`);
        // Send full state directly to the new unauthenticated socket
        fetchAndEmitFullPlayerState(socket);
    }

    // ... existing handlers ...

    // Handle battle subscriptions
    socket.on('battle:subscribe', (battleId) => {
      if (battleId && socket.userDetails) {
        logger.debug(`Socket ${socket.id} subscribing to battle ${battleId}`);
        socket.join(`battle_${battleId}`);
      }
    });
    
    socket.on('battle:unsubscribe', (battleId) => {
      if (battleId) {
        logger.debug(`Socket ${socket.id} unsubscribing from battle ${battleId}`);
        socket.leave(`battle_${battleId}`);
      }
    });

    socket.on('disconnect', () => logger.info(`Client disconnected: ${socket.id}`));
    socket.on('error', (error) => logger.error('WebSocket error:', { error: error.message, socketId: socket.id }));
  });

  // ... existing code ...
};


// --- Module Exports ---
module.exports = {
    setupWebSocketHandlers,
    getIo,
    triggerPlayerStateUpdate,
    // Export other specific emitters if they need to be called directly from routes/services
    // (Generally prefer using triggerPlayerStateUpdate for player state)
    emitCommentCreated,
    emitCommentDeleted,
    emitSuggestionCreated,
    emitSuggestionUpdated,
    emitSuggestionVote,
    emitUserCreditsUpdate,
    emitAchievementUnlocked,
    fetchAndEmitPlayerState
}; 