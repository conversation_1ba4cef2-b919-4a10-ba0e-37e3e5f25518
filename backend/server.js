require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const { createServer } = require('http');
const { Server } = require('socket.io');
const path = require('path');
const rateLimit = require('express-rate-limit');
const cookieParser = require('cookie-parser');
const passport = require('passport');
require('./config/passportSetup'); // Initialize Passport strategies
const { PrismaClient } = require('@prisma/client');
const logger = require('./utils/logger');
const authRoutes = require('./routes/auth');
const playlistRoutes = require('./routes/playlists');
const suggestionRoutes = require('./routes/suggestions');
const userRoutes = require('./routes/users');
const youtubeRoutes = require('./routes/youtube');
const configRoutes = require('./routes/config');
const settingsRoutes = require('./routes/settings');
const publicRoutes = require('./routes/public');
const playbackRoutes = require('./routes/playback');
const radioRoutes = require('./routes/radio');
const gameRoutes = require('./routes/game');
const { authenticateJWT } = require('./middleware/auth');
const { setupWebSocketHandlers } = require('./websocket');
const { initCronJobs } = require('./services/cronJobs');

const fs = require('fs');
const app = express();

// Trust the proxy to get correct IP for rate limiting, logging etc.
// Set appropriately if behind a specific known proxy, e.g., app.set('trust proxy', 'loopback') or app.set('trust proxy', '***************')
// Setting to `true` trusts the X-Forwarded-* headers set by the immediate upstream proxy.
// For local development with CRA proxy, 'loopback' is usually sufficient and safer.
app.set('trust proxy', 'loopback');

// Support for Cloudflare Flexible SSL mode when tunneling
if (process.env.USE_CLOUDFLARE_FLEXIBLE_SSL === 'true') {
  // This middleware will fix the protocol when behind Cloudflare Flexible SSL
  app.use((req, res, next) => {
    // Check for Cloudflare headers indicating HTTPS on their side
    if (req.headers['cf-visitor'] && JSON.parse(req.headers['cf-visitor']).scheme === 'https') {
      req.headers['x-forwarded-proto'] = 'https';
    } else if (req.headers['x-forwarded-proto'] === 'http' && req.headers['cf-ray']) {
      // If Cloudflare ray ID exists but protocol is HTTP, client is using HTTPS but our origin is HTTP
      req.headers['x-forwarded-proto'] = 'https';
    }
    next();
  });
  
  logger.info('Cloudflare Flexible SSL mode enabled - protocol will be fixed for secure clients');
}

// Explicitly define allowed origins for the single-URL setup
const allowedOrigins = [
  process.env.FRONTEND_URL,
  'https://socialdance.share.zrok.io', // Added for zrok access
  process.env.NODE_ENV === 'development' ? 'http://localhost:3001' : undefined,
].filter(Boolean);

logger.info('Allowed CORS Origins:', allowedOrigins);

const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: allowedOrigins,
    methods: ['GET', 'POST']
  }
});

// Setup WebSocket handlers and store the io instance
setupWebSocketHandlers(io);

// Initialize Prisma Client
const prisma = new PrismaClient();

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      ...helmet.contentSecurityPolicy.getDefaultDirectives(), // Start with defaults
      "script-src": ["'self'", "https://www.youtube.com"],
      "media-src": ["'self'", "https://www.youtube.com", "https://*.googlevideo.com", "blob:", "data:"],
      "worker-src": ["'self'", "blob:"],
      "img-src": ["'self'", "https://i.ytimg.com", "data:", "https://imgs.search.brave.com"],
      "connect-src": [
        "'self'",
        ...(process.env.FRONTEND_URL ? [process.env.FRONTEND_URL] : []),
        "wss:",
        "ws://localhost:*",
        "wss://localhost:*",
        "https://www.youtube.com",
        "https://*.googlevideo.com",
        "https://*.doubleclick.net"
      ],
      "frame-src": ["'self'", "https://www.youtube.com"],
    }
  },
  permissionsPolicy: {
    features: {
      "web-share": ["'self'", "https://www.youtube.com"],
      "autoplay": ["'self'", "https://www.youtube.com"],
      "fullscreen": ["'self'", "https://www.youtube.com"],
      "encryptedMedia": ["'self'", "https://www.youtube.com"]
    }
  }
}));
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    if (allowedOrigins.indexOf(origin) === -1) {
      const msg = `CORS policy does not allow access from the specified Origin: ${origin}`;
      logger.warn(msg, { allowed: allowedOrigins });
      return callback(new Error(msg), false);
    }
    return callback(null, true);
  },
  credentials: true
}));
app.use(express.json({ limit: '2mb' }));
app.use(express.urlencoded({ extended: true, limit: '2mb' }));
app.use(cookieParser());
app.use(passport.initialize());

// --- Header Filtering Logic ---
const importantRequestHeaders = [
  'host', 'user-agent', 'referer', 'content-type', 'accept', 
  'x-forwarded-for', 'x-forwarded-proto', 'origin', 'authorization' // Be cautious logging Authorization in prod
];
const importantResponseHeaders = [
  'content-type', 'content-length', 'location', 'set-cookie', 
  'cache-control', 'etag', 'x-ratelimit-limit', 'x-ratelimit-remaining', 'x-ratelimit-reset'
];

const filterHeaders = (headers, allowList) => {
  if (!headers) return {};
  return Object.entries(headers)
    .filter(([key]) => allowList.includes(key.toLowerCase()))
    .reduce((obj, [key, value]) => {
      // Mask Authorization header value for security, unless explicitly needed
      if (key.toLowerCase() === 'authorization' && value) {
          obj[key] = 'Bearer [REDACTED]'; // Or completely omit based on policy
      } else if (key.toLowerCase() === 'cookie' && value) {
          obj[key] = '[REDACTED]'; // Generally avoid logging raw cookies
      } else {
          obj[key] = value;
      }
      return obj;
    }, {});
};
// --- End Header Filtering Logic ---

// Request/response logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  const requestLogDetails = {
    method: req.method,
    url: req.originalUrl,
    headers: filterHeaders(req.headers, importantRequestHeaders),
    body: req.body,
    query: req.query,
    params: req.params,
    ip: req.ip,
    timestamp: new Date().toISOString()
  };
  logger.debug('Incoming request', requestLogDetails);

  res.on('finish', () => {
    const duration = Date.now() - start;
    const responseLogDetails = {
      request: {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.headers['user-agent']
      },
      status: res.statusCode,
      duration,
      responseHeaders: filterHeaders(res.getHeaders(), importantResponseHeaders)
    };
    if (res.statusCode >= 400) {
      logger.error(`HTTP Error Response: ${res.statusCode}`, responseLogDetails);
    } else {
      logger.info(`HTTP Response: ${res.statusCode}`, responseLogDetails);
    }
  });

  res.on('error', (err) => {
    logger.error('Response stream error', { error: err, url: req.originalUrl });
  });

  next();
});

// --- Canonical URL Redirect Middleware ---
// Ensures all requests use the canonical domain (www.social-dance.org) to prevent duplicate content
app.use((req, res, next) => {
  // Only apply in production
  if (process.env.NODE_ENV === 'production') {
    const host = req.headers.host;
    const protocol = req.headers['x-forwarded-proto'] || req.protocol;
    
    // Check if we need to redirect to canonical domain
    if (host && host !== 'www.social-dance.org') {
      // Redirect non-www to www, and any other domains to canonical
      if (host === 'social-dance.org' || host.endsWith('.social-dance.org')) {
        const canonicalUrl = `https://www.social-dance.org${req.originalUrl}`;
        logger.info(`Canonical redirect: ${protocol}://${host}${req.originalUrl} -> ${canonicalUrl}`);
        return res.redirect(301, canonicalUrl);
      }
    }
    
    // Ensure HTTPS in production
    if (protocol !== 'https' && host && host.includes('social-dance.org')) {
      const httpsUrl = `https://${host}${req.originalUrl}`;
      logger.info(`HTTPS redirect: ${protocol}://${host}${req.originalUrl} -> ${httpsUrl}`);
      return res.redirect(301, httpsUrl);
    }
  }
  
  next();
});

// Rate limiting middleware
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 500, // Back to normal limit now that the infinite loop is fixed
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: false,
  message: 'Too many requests, please try again later.',
});

// Apply rate limiting to all routes
app.use(limiter);

// Apply a more strict rate limiter to sensitive routes
const authLimiter = rateLimit({
  windowMs: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
  max: parseInt(process.env.AUTH_RATE_LIMIT_MAX_REQUESTS) || 200,  // Increased from 100 to 200
  message: 'Too many authentication attempts, please try again later',
  standardHeaders: true,
  legacyHeaders: false
});

// Apply a more lenient limiter for client-side logging
const loggingLimiter = rateLimit({
  windowMs: parseInt(process.env.LOGGING_RATE_LIMIT_WINDOW_MS) || 5 * 60 * 1000,
  max: parseInt(process.env.LOGGING_RATE_LIMIT_MAX_REQUESTS) || 500,  // Increased from 200 to 500
  message: 'Too many logging requests',
  standardHeaders: true,
  legacyHeaders: false
});

// Apply global middleware to all routes
// These middlewares will apply to ALL routes including static files
// so be careful not to put anything here that should only apply to API routes

// --- API Routes ---
// Special debug route for checking authentication
app.get('/api/debug/auth-check', authenticateJWT, (req, res) => {
  logger.info('Auth check debug endpoint accessed', {
    user: req.user,
    headers: req.headers,
    cookies: req.cookies
  });
  
  // Send the user details back
  res.json({
    authenticated: !!req.user,
    user: req.user,
    authHeader: req.headers.authorization ? 'Present (masked)' : 'Missing',
    cookies: req.cookies ? Object.keys(req.cookies) : 'None'
  });
});

// Apply rate limiting and authentication middleware to API routes
app.use('/api/auth/logs', loggingLimiter); // Special handling for client-side logs
app.use('/api/auth/login', authLimiter);
app.use('/api/auth/signup', authLimiter);
app.use('/api/auth/admin', authLimiter);
app.use('/api/auth', authLimiter, authRoutes);
app.use('/api/playlists', authenticateJWT, playlistRoutes);
app.use('/api/suggestions', authenticateJWT, suggestionRoutes);  // Add authenticateJWT to ensure user is attached
app.use('/api/users', userRoutes);
app.use('/api/youtube', authenticateJWT, youtubeRoutes);
app.use('/api/config', configRoutes);
app.use('/api/settings', authenticateJWT, settingsRoutes);
app.use('/api/public', publicRoutes);
app.use('/api/playback', authenticateJWT, playbackRoutes);
app.use('/api/radio', radioRoutes);
app.use('/api/game', gameRoutes);

// --- Static file serving for Uploads ---
// Serve uploaded files (like avatars)
const uploadsPath = path.join(__dirname, '..', 'uploads'); // Use '..' to go up one level from backend/
logger.info(`Serving uploaded files from: ${uploadsPath}`);
// Mount it on the '/uploads' path so requests like /uploads/avatars/file.webp work
app.use('/uploads', express.static(uploadsPath));

// --- SEO ENHANCEMENT ROUTE - Must come BEFORE static file serving for React app ---
// This ensures our SEO processing happens for all HTML routes including the root path
app.get(['/', '/login', '/signup', '/article/*', '/events*', '/clubs*', '/dancers*', '/dancer', '/djs', '/singers', '/full-top-voted', '/leaderboard/all'], (req, res, next) => {
  logger.info(`SEO Enhancement: Processing path: ${req.path}`);
  
  // Generate SEO metadata based on route
  generateSeoMetadata(req.path, req)
    .then(seoMetadata => {
      // Read the HTML template file
      const filePath = path.join(__dirname, '../frontend/build/index.html');
      fs.readFile(filePath, 'utf8', (err, htmlData) => {
        if (err) {
          logger.error('Error reading index.html template for SEO enhancement', { error: err, path: req.path });
          return next(); // Continue to next middleware
        }
        
        // Generate HTML with injected SEO meta tags
        try {
          // Replace title
          let updatedHtml = htmlData.replace(
            /<title>.*?<\/title>/g,
            `<title>${seoMetadata.title}</title>`
          );
          
          // Add or replace description meta tag
          if (updatedHtml.includes('<meta name="description"')) {
            updatedHtml = updatedHtml.replace(
              /<meta name="description".*?>/g,
              `<meta name="description" content="${seoMetadata.description}">`
            );
          } else {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="description" content="${seoMetadata.description}">\n  </head>`
            );
          }
          
          // Add keywords meta tag if available
          if (seoMetadata.keywords) {
            if (updatedHtml.includes('<meta name="keywords"')) {
              updatedHtml = updatedHtml.replace(
                /<meta name="keywords".*?>/g,
                `<meta name="keywords" content="${seoMetadata.keywords}">`
              );
            } else {
              updatedHtml = updatedHtml.replace(
                '</head>',
                `  <meta name="keywords" content="${seoMetadata.keywords}">\n  </head>`
              );
            }
          }
          
          // Add canonical URL
          updatedHtml = updatedHtml.replace(
            '</head>',
            `  <link rel="canonical" href="${seoMetadata.canonicalUrl}">\n  </head>`
          );
          
          // Add Open Graph tags
          updatedHtml = updatedHtml.replace(
            '</head>',
            `  <meta property="og:title" content="${seoMetadata.ogTitle}">\n` +
            `  <meta property="og:description" content="${seoMetadata.ogDescription}">\n` +
            `  <meta property="og:url" content="${seoMetadata.ogUrl}">\n` +
            `  <meta property="og:type" content="${seoMetadata.ogType}">\n` +
            `  <meta property="og:image" content="${seoMetadata.ogImage}">\n` +
            `  </head>`
          );
          
          // Add Twitter Card tags
          updatedHtml = updatedHtml.replace(
            '</head>',
            `  <meta name="twitter:card" content="${seoMetadata.twitterCard}">\n` +
            `  <meta name="twitter:title" content="${seoMetadata.twitterTitle}">\n` +
            `  <meta name="twitter:description" content="${seoMetadata.twitterDescription}">\n` +
            `  <meta name="twitter:image" content="${seoMetadata.twitterImage}">\n` +
            `  </head>`
          );
          
          // Add structured data
          const structuredDataScripts = [];
          
          // Main structured data
          if (seoMetadata.structuredData) {
            structuredDataScripts.push(
              `  <script type="application/ld+json">\n` +
              `    ${seoMetadata.structuredData}\n` +
              `  </script>`
            );
          }
          
          // Breadcrumb structured data
          if (seoMetadata.breadcrumbData) {
            structuredDataScripts.push(
              `  <script type="application/ld+json">\n` +
              `    ${seoMetadata.breadcrumbData}\n` +
              `  </script>`
            );
          }
          
          // Insert all structured data scripts
          if (structuredDataScripts.length > 0) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `${structuredDataScripts.join('\n')}\n  </head>`
            );
          }
          
          // Add noindex tag if specified
          if (seoMetadata.noindex) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="robots" content="noindex,nofollow">\n  </head>`
            );
          } else {
            // Add robots meta tag for indexable pages
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="robots" content="${seoMetadata.robots || 'index,follow'}">\n  </head>`
            );
          }
          
          // Add author meta tag
          if (seoMetadata.author) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="author" content="${seoMetadata.author}">\n  </head>`
            );
          }
          
          // Add search engine verification tags
          if (seoMetadata.googleSiteVerification) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="google-site-verification" content="${seoMetadata.googleSiteVerification}">\n  </head>`
            );
          }
          
          if (seoMetadata.bingSiteVerification) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="msvalidate.01" content="${seoMetadata.bingSiteVerification}">\n  </head>`
            );
          }
          
          if (seoMetadata.yandexVerification) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="yandex-verification" content="${seoMetadata.yandexVerification}">\n  </head>`
            );
          }
          
          // Add hreflang tags for international SEO
          if (seoMetadata.hreflangs && seoMetadata.hreflangs.length > 0) {
            const hreflangTags = seoMetadata.hreflangs.map(hreflang => 
              `  <link rel="alternate" hreflang="${hreflang.lang}" href="${hreflang.url}">`
            ).join('\n');
            
            updatedHtml = updatedHtml.replace(
              '</head>',
              `${hreflangTags}\n  </head>`
            );
          }
          
          // Add additional Open Graph tags for better social sharing
          updatedHtml = updatedHtml.replace(
            '</head>',
            `  <meta property="og:locale" content="${seoMetadata.ogLocale || 'en_US'}">\n` +
            `  <meta property="og:site_name" content="${seoMetadata.ogSiteName || 'Social Dance Moments'}">\n` +
            `  </head>`
          );
          
          // Add Twitter site tag if available
          if (seoMetadata.twitterSite) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="twitter:site" content="${seoMetadata.twitterSite}">\n  </head>`
            );
          }
          
          // Add last modified date for better indexing
          if (seoMetadata.lastModified) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta http-equiv="last-modified" content="${seoMetadata.lastModified}">\n  </head>`
            );
          }
          
          // Add DNS prefetch for external resources
          updatedHtml = updatedHtml.replace(
            '</head>',
            `  <!-- DNS prefetch for external resources -->\n` +
            `  <link rel="dns-prefetch" href="//www.youtube.com">\n` +
            `  <link rel="dns-prefetch" href="//i.ytimg.com">\n` +
            `  <link rel="dns-prefetch" href="//fonts.googleapis.com">\n` +
            `  <link rel="dns-prefetch" href="//fonts.gstatic.com">\n` +
            `  </head>`
          );
          
          // Add resource hints for performance optimization
          updatedHtml = updatedHtml.replace(
            '</head>',
            `  <!-- Resource hints for performance optimization -->\n` +
            `  <link rel="preconnect" href="https://fonts.googleapis.com">\n` +
            `  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>\n` +
            `  <link rel="preload" href="/img/social-dance-share.jpg" as="image">\n` +
            `  </head>`
          );
          
          // Add enterprise-level meta tags and optimizations
          if (seoMetadata.preloadResources && seoMetadata.preloadResources.length > 0) {
            const preloadTags = seoMetadata.preloadResources.map(resource => {
              if (resource.rel === 'preconnect') {
                return `  <link rel="preconnect" href="${resource.href}"${resource.crossorigin ? ' crossorigin' : ''}>`;
              }
              return `  <link rel="preload" href="${resource.href}" as="${resource.as}"${resource.type ? ` type="${resource.type}"` : ''}>`;
            }).join('\n');
            
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <!-- Enterprise performance optimizations -->\n${preloadTags}\n  </head>`
            );
          }
          
          // Add professional mobile and app meta tags
          if (seoMetadata.themeColor) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="theme-color" content="${seoMetadata.themeColor}">\n` +
              `  <meta name="msapplication-TileColor" content="${seoMetadata.msapplicationTileColor || seoMetadata.themeColor}">\n` +
              `  <meta name="apple-mobile-web-app-capable" content="${seoMetadata.appleMobileWebAppCapable || 'yes'}">\n` +
              `  <meta name="apple-mobile-web-app-status-bar-style" content="${seoMetadata.appleMobileWebAppStatusBarStyle || 'black-translucent'}">\n` +
              `  </head>`
            );
          }
          
          // Add security and privacy meta tags
          if (seoMetadata.referrerPolicy) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="referrer" content="${seoMetadata.referrerPolicy}">\n  </head>`
            );
          }
          
          // Add content categorization meta tags
          if (seoMetadata.category) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="category" content="${seoMetadata.category}">\n  </head>`
            );
          }
          
          if (seoMetadata.contentRating) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="rating" content="${seoMetadata.contentRating}">\n  </head>`
            );
          }
          
          // Add advanced social media meta tags
          if (seoMetadata.facebookAppId) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta property="fb:app_id" content="${seoMetadata.facebookAppId}">\n  </head>`
            );
          }
          
          if (seoMetadata.twitterCreator) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="twitter:creator" content="${seoMetadata.twitterCreator}">\n  </head>`
            );
          }
          
          // Add professional search engine directives
          if (seoMetadata.googlebot) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="googlebot" content="${seoMetadata.googlebot}">\n  </head>`
            );
          }
          
          if (seoMetadata.bingbot) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="bingbot" content="${seoMetadata.bingbot}">\n  </head>`
            );
          }
          
          // Add content freshness signals
          if (seoMetadata.publishedTime) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta property="article:published_time" content="${seoMetadata.publishedTime}">\n  </head>`
            );
          }
          
          if (seoMetadata.modifiedTime) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta property="article:modified_time" content="${seoMetadata.modifiedTime}">\n  </head>`
            );
          }
          
          // Add content tags for better categorization
          if (seoMetadata.tags && seoMetadata.tags.length > 0) {
            const tagString = seoMetadata.tags.join(', ');
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="news_keywords" content="${tagString}">\n  </head>`
            );
          }
          
          // Add organization structured data if available
          if (seoMetadata.organizationData) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <script type="application/ld+json">\n` +
              `    ${seoMetadata.organizationData}\n` +
              `  </script>\n  </head>`
            );
          }
          
          // Add entity-based meta tags for advanced SEO
          if (seoMetadata.entityData) {
            updatedHtml = updatedHtml.replace(
              '</head>',
              `  <meta name="entity.primary" content="${seoMetadata.entityData.primaryEntity}">\n` +
              `  <meta name="entity.type" content="${seoMetadata.entityData.entityType}">\n` +
              `  <meta name="entity.related" content="${seoMetadata.entityData.relatedEntities.join(', ')}">\n` +
              `  </head>`
            );
          }
          
          // Send the enhanced HTML
          res.send(updatedHtml);
          
        } catch (htmlError) {
          logger.error('Error injecting SEO metadata into HTML', { error: htmlError, path: req.path });
          next(); // Continue to next middleware
        }
      });
    })
    .catch(error => {
      logger.error('Error generating SEO metadata', { error, path: req.path });
      next(); // Continue to next middleware
    });
});

// --- Static Files Serving ---
// Serve sitemap.xml dynamically with correct canonical URLs
app.get('/sitemap.xml', async (req, res) => {
  try {
    // Always use the canonical production URL for sitemap
    const baseUrl = process.env.NODE_ENV === 'production' 
      ? 'https://www.social-dance.org' 
      : 'https://www.social-dance.org'; // Use production URL even in development for sitemap
    
    // Generate sitemap content dynamically
    let sitemap = '<?xml version="1.0" encoding="UTF-8"?>\n';
    sitemap += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
    
    // Add static pages
    const staticPages = [
      { url: '/', priority: '1.0', changefreq: 'daily' },
      { url: '/events', priority: '0.8', changefreq: 'weekly' },
      { url: '/clubs', priority: '0.8', changefreq: 'weekly' },
      { url: '/dancers', priority: '0.9', changefreq: 'daily' },
      { url: '/djs', priority: '0.8', changefreq: 'weekly' },
      { url: '/singers', priority: '0.8', changefreq: 'weekly' },
      { url: '/full-top-voted', priority: '0.7', changefreq: 'daily' },
      { url: '/leaderboard/all', priority: '0.7', changefreq: 'daily' },
    ];
    
    for (const page of staticPages) {
      sitemap += `  <url>\n`;
      sitemap += `    <loc>${baseUrl}${page.url}</loc>\n`;
      sitemap += `    <changefreq>${page.changefreq}</changefreq>\n`;
      sitemap += `    <priority>${page.priority}</priority>\n`;
      sitemap += `  </url>\n`;
    }
    
    // Add article pages
    const articleSlugs = [
      'salsa-history',
      'bachata-moves', 
      'dance-festival',
      'dance-music'
    ];
    
    for (const slug of articleSlugs) {
      sitemap += `  <url>\n`;
      sitemap += `    <loc>${baseUrl}/article/${slug}</loc>\n`;
      sitemap += `    <changefreq>weekly</changefreq>\n`;
      sitemap += `    <priority>0.8</priority>\n`;
      sitemap += `  </url>\n`;
    }
    
    // Close XML
    sitemap += '</urlset>';
    
    res.header('Content-Type', 'application/xml');
    res.send(sitemap);
    
    logger.info('Sitemap served dynamically with canonical URLs');
    
  } catch (error) {
    logger.error('Error generating dynamic sitemap', { error });
    res.status(500).send('Error generating sitemap');
  }
});

// --- Static file serving for React app ---
// Serve static files from the React app build
const reactBuildPath = path.join(__dirname, '../frontend/build');
logger.info(`Serving static files from: ${reactBuildPath}`);
app.use(express.static(reactBuildPath));

// --- Fallback for Client-Side Routing ---
// Handles any requests that don't match API routes, SEO routes, or static files
// Sends the main index.html, allowing React Router to take over
app.get('*', (req, res, next) => {
  // Skip processing for specific paths like static assets, images, etc.
  if (
    req.path.startsWith('/static/') || 
    req.path.startsWith('/uploads/') ||
    req.path.match(/\.(jpg|jpeg|png|gif|svg|ico|webp|css|js|json|woff|woff2|ttf|eot|pdf)$/i)
  ) {
    // Try the next handler, likely express.static or eventually 404
    return next();
  }

  // For all other paths, serve the index.html to enable client-side routing
  const indexHtmlPath = path.join(__dirname, '../frontend/build/index.html');
  res.sendFile(indexHtmlPath);
});

// --- Custom 404 Handler ---
// This comes after all routes
app.use((req, res, next) => {
  // If we reach this point, no route handled the request
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  
  // Generate SEO-friendly 404 page
  const errorTitle = 'Page Not Found - Social Dance Moments';
  const errorDescription = 'The page you were looking for could not be found. Please check the URL or navigate to our homepage.';
  
  // Set proper status code
  res.status(404);
  
  // Check if the request accepts HTML
  if (req.accepts('html')) {
    // Read the HTML template file
    const filePath = path.join(__dirname, '../frontend/build/index.html');
    fs.readFile(filePath, 'utf8', (err, htmlData) => {
      if (err) {
        logger.error('Error reading index.html template for 404 page', { error: err, path: req.path });
        return res.type('txt').send('Page Not Found');
      }
      
      try {
        // Replace title
        let updatedHtml = htmlData.replace(
          /<title>.*?<\/title>/g,
          `<title>${errorTitle}</title>`
        );
        
        // Replace or add description meta tag
        if (updatedHtml.includes('<meta name="description"')) {
          updatedHtml = updatedHtml.replace(
            /<meta name="description".*?>/g,
            `<meta name="description" content="${errorDescription}">`
          );
        } else {
          updatedHtml = updatedHtml.replace(
            '</head>',
            `  <meta name="description" content="${errorDescription}">\n  </head>`
          );
        }
        
        // Add noindex tag
        updatedHtml = updatedHtml.replace(
          '</head>',
          `  <meta name="robots" content="noindex,nofollow">\n  </head>`
        );
        
        // Add structured data for error page
        const errorStructuredData = {
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": [{
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": baseUrl
          }, {
            "@type": "ListItem",
            "position": 2,
            "name": "Error 404",
            "item": `${baseUrl}${req.path}`
          }]
        };
        
        updatedHtml = updatedHtml.replace(
          '</head>',
          `  <script type="application/ld+json">\n` +
          `    ${JSON.stringify(errorStructuredData)}\n` +
          `  </script>\n  </head>`
        );
        
        // Send the 404 page
        return res.send(updatedHtml);
      } catch (htmlError) {
        logger.error('Error creating 404 page', { error: htmlError, path: req.path });
        return res.type('txt').send('Page Not Found');
      }
    });
  } else if (req.accepts('json')) {
    // If the request wants JSON, send a JSON response
    res.json({ error: 'Not found', message: errorDescription });
  } else {
    // Plain text fallback
    res.type('txt').send('404 Not Found');
  }
});

// Note: The inline generateSeoMetadata function was moved to utils/seoUtils.js
// The external version is imported below

// SEO and Sitemap Utilities
const { generateSeoMetadata } = require('./utils/seoUtils');
const { generateSitemap } = require('./utils/sitemapGenerator');

// Initialize server with all middleware and routes
async function initServer() {
  const PORT = process.env.PORT || 3000;
  
  // Initialize cron jobs
  initCronJobs();
  
  // Start HTTP server
  httpServer.listen(PORT, () => {
    logger.info(`Server running on port ${PORT}`);
  });
  
  // Generate sitemap on startup and schedule periodic regeneration
  try {
    // Determine the base URL for sitemap generation
    // In production, use the primary domain, otherwise use localhost
    const baseUrl = process.env.NODE_ENV === 'production' 
      ? 'https://www.social-dance.org' 
      : `http://localhost:${PORT}`;
      
    // Generate sitemap initially
    await generateSitemap(baseUrl);
    
    // Schedule sitemap regeneration every 24 hours
    setInterval(async () => {
      try {
        await generateSitemap(baseUrl);
        logger.info('Scheduled sitemap regeneration completed');
      } catch (error) {
        logger.error('Scheduled sitemap regeneration failed', { error });
      }
    }, 24 * 60 * 60 * 1000); // 24 hours
    
    logger.info('Sitemap generation scheduled successfully');
  } catch (error) {
    logger.error('Initial sitemap generation failed', { error });
    // Continue server startup even if sitemap generation fails
  }
}

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error caught in middleware', { 
      error: err.message, 
      stack: err.stack, 
      status: err.status, 
      url: req.originalUrl, 
      method: req.method 
  });
  const statusCode = typeof err.status === 'number' && err.status >= 400 ? err.status : 500;
  res.status(statusCode).json({
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal Server Error',
    details: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
});

// Graceful shutdown
const gracefulShutdown = async () => {
  logger.info('Received shutdown signal. Closing connections...');
  
  try {
    await prisma.$disconnect();
    logger.info('Database connections closed.');
    
    httpServer.close(() => {
      logger.info('HTTP server closed.');
      process.exit(0);
    });
  } catch (err) {
    logger.error('Error during shutdown:', err);
    process.exit(1);
  }
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start the server
initServer().catch(err => {
  logger.error('Server initialization failed', { error: err });
  process.exit(1);
}); 