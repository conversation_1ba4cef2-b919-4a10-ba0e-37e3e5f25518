# Party Playlist Application - Server-Centric Social Dance

This project is a mobile-first, server-centric web application designed for social dance parties. It allows an <PERSON><PERSON> (DJ) to manage dynamic YouTube playlists based on templates and real-time song suggestions from Dancer users. The backend handles all YouTube API interactions and database management (PostgreSQL), providing data to a responsive frontend via REST API and WebSockets for real-time updates.

**Operational Modes:**

*   **Generated Mode:** The application generates YouTube playlists from suggestions and optional source playlists. Playback is controlled by the app.
*   **External Mirror Mode:** The application mirrors an existing YouTube playlist set by the admin. Playback is controlled externally.

The Admin configures the desired mode via the settings interface.

Dancer users can suggest songs using credits, vote on suggestions, and (in future enhancements) engage with features like suggestion history, leaderboards, and basic profiles. Suggestion/voting features are primarily relevant in Generated Mode.

A Public Player view displays the current playlist status (adjusted for the active mode) without requiring login.

## QA, Bug Logging, and Production-Readiness

- **QA Checklist & Bug Log:**
  - All manual test cases, bug logging, and QA tracking are maintained in [`QA.md`](./QA.md).
  - Team members should check off test cases and log/resolve bugs during the QA phase.

- **Business Requirements:** See [`prd.md`](./prd.md)
- **Technical Specifications:** See [`prd_tech.md`](./prd_tech.md)
- **Project Status & Roadmap:** See [`status.md`](./status.md)

### Setup & Running (Windows & Linux)
1. Clone the repository.
2. Install dependencies:
   - `npm install` (in both `/backend` and `/frontend`)
3. Set up environment variables as described in `.env.example` (for both backend and frontend).
4. Run the setup script:
   - `node setup-and-run.js` (from the project root)
5. Start the backend and frontend servers as described in the main instructions.

### Handoff Checklist
- [ ] All major user flows tested and verified (see `QA.md`)
- [ ] All bugs resolved or documented in `QA.md`
- [ ] Documentation up to date (`README.md`, `prd.md`, `prd_tech.md`, `status.md`)
- [ ] Ready for production deployment

### Database Requirements
- The application now requires **PostgreSQL** as the database
- Set the `DATABASE_URL` in your `.env` file to your PostgreSQL connection string (e.g. `postgres://USER:PASSWORD@HOST:PORT/DATABASE`).
- After updating the database configuration, run `npx prisma migrate dev` and `npx prisma generate`.

### Error Log Monitoring

To facilitate easier debugging and issue tracking, a dedicated error monitoring service is included.

- **Script:** `backend/services/errorLogMonitor.js`
- **Function:** This script continuously monitors the main application log (`logs/combined.log`) for entries with the level `ERROR`.
- **Output:** It extracts these error entries, along with preceding context lines, and appends them to `logs/error_summary.log`.
- **Purpose:** Provides a concise, error-focused log file, simplifying the process of identifying and analyzing failures.

**Running the Monitor:**

This service needs to be run as a separate process alongside the main backend application.

1.  **Standalone:**
    ```bash
    node backend/services/errorLogMonitor.js
    ```
2.  **Using PM2 (Recommended for persistence):**
    ```bash
    pm2 start backend/services/errorLogMonitor.js --name error-monitor
    pm2 save # Optional: ensures monitor restarts after system reboot
    ```

## Installation and Running

## Key Features

*   **Dual Mode Operation:** Choose between `GENERATED` (app manages playlist) or `EXTERNAL_MIRROR` (app mirrors existing YouTube playlist).
*   **Role-Based Access:** Distinct interfaces and capabilities for Admin (DJ) and Dancer users.
*   **Public Player View:** A read-only view for displaying the current playlist and song status.
*   **Song Suggestions:** Dancers can suggest YouTube tracks (using credits in `GENERATED` mode).
*   **Voting System:** Dancers can vote on pending suggestions.
*   **Playlist Templates:** Admin can define templates for automatic playlist generation.
*   **Real-time Updates:** WebSockets (`socket.io`) are used for pushing updates (new suggestions, votes, player state) to connected clients.
*   **YouTube Integration:** Leverages the YouTube Data API v3 for searching, playlist management, and fetching video details.

## New: Party Playlist Generation (Admin)

Admins can now generate a separate, one-off YouTube playlist suitable for a specific party or event. This playlist combines approved user suggestions with admin-curated songs from external YouTube playlists, following the structure of the active template.

**Configuration:**

1.  Navigate to the "Playlist Management" section in the Admin dashboard.
2.  Define and **Activate** a Playlist Template using the "Style Rotation Builder". This template dictates the order and number of songs per style block in the generated playlist.
3.  Locate the "Manage Dance Style Sources" section.
4.  For each dance style you might want to include music for:
    *   Optionally, paste the full URL of a public YouTube playlist containing songs you want to add for that style (e.g., `https://www.youtube.com/playlist?list=PLxxxxxxxxxxxxxxx`).
    *   Leave the URL field blank if you only want to use approved user suggestions for that style.
5.  Click "Save Source URLs" to store your configurations.

**Generating the Playlist:**

1.  Ensure you have an **Active Playlist Template** defined using the "Style Rotation Builder". This template dictates the **repeating pattern** of styles (and the number of songs per style within one pattern cycle).
2.  Select the **Dance Styles** you want to include music for using the checkboxes in the "Manage Dance Style Sources" section. Only songs for these selected styles will be considered.
3.  Optionally, set a **Max Songs** limit. If unset, the generator will try to fill the playlist based on available unique songs following the template pattern.
4.  Click **"Generate Party Playlist"**.

*   **Logic:** The service will fetch all approved suggestions and songs from your configured source URLs for the selected styles.
*   It then cycles through your **active template's style rotation repeatedly**.
*   For each slot in the template cycle, it picks the next available **unique** song (from suggestions or admin sources) matching that slot's style.
*   This continues until the **Max Songs** limit is reached, or until a full cycle through the template pattern fails to find any new unique songs (because all available songs for the required styles have been used).
*   The final playlist is created on YouTube, containing only unique songs, following the repeated template order as closely as possible given song availability.

## Security

The application incorporates standard web security practices:

*   Secure authentication using JWT with HttpOnly cookies.
*   Password hashing using bcryptjs.
*   Role-based authorization to protect sensitive actions.
*   Rate limiting to prevent brute-force attacks.
*   Security headers set via `helmet`.
*   Input validation on API endpoints using `express-validator`.
*   Configurable CORS policy.
*   Encryption of sensitive tokens stored in the database.