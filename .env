PORT=3001
NODE_ENV=development
JWT_SECRET=jcrf1uvaedobkr6z5sz4
DATABASE_URL="postgres://partyuser:partypass@localhost:5433/partydb"
# Laptop
# YOUTUBE_CLIENT_ID=494492878293-9fpe6mrosfjottakhe6accma5buk7hse.apps.googleusercontent.com
# YOUTUBE_CLIENT_SECRET=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
# YOUTUBE_REDIRECT_URI=https://t2byvgmygaed.share.zrok.io/api/auth/youtube/callback
# FRONTEND_URL=https://t2byvgmygaed.share.zrok.io

# Linux machine
# YOUTUBE_CLIENT_ID=633440852971-2gblomh5i37h8l96hal3m4grr4o37ikc.apps.googleusercontent.com
# YOUTUBE_CLIENT_SECRET=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
YOUTUBE_CLIENT_ID=926156498085-r1sj66q8selv0ism6nfot3982me6c3eh.apps.googleusercontent.com
YOUTUBE_CLIENT_SECRET=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
YOUTUBE_REDIRECT_URI=https://www.social-dance.org/api/auth/youtube/callback
FRONTEND_URL=https://www.social-dance.org

GOOGLE_SIGNIN_CLIENT_ID=633440852971-dik77mfsim16unm5g2gntt5rkgudbg43.apps.googleusercontent.com
GOOGLE_SIGNIN_CLIENT_SECRET=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
GOOGLE_SIGNIN_CALLBACK_URL=https://www.social-dance.org/api/auth/google/callback 

FACEBOOK_APP_ID=633440852971-dik77mfsim16unm5g2gntt5rkgudbg43.apps.googleusercontent.com
FACEBOOK_APP_SECRET=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
FACEBOOK_CALLBACK_URL=https://www.social-dance.org/api/auth/google/callback 


BRAVE_SEARCH_API_KEY=BSAxSY11xO5nhIRyCJF0xQTQ8AyVKTU

RATE_LIMIT_MAX_REQUESTS=500
DEFAULT_DANCER_CREDITS=50
  
OPENWEATHERMAP_API_KEY=********************************

# VAPID Keys for Web Push Notifications
VAPID_PUBLIC_KEY=BOCxbSmzBpKtXAXp5GgABedKv-1VBW1ZjxuwqVPbDiIPLZwRgX_Z1_2aryi1xmMucYfqMvycso3xU9VcuUWVjq0
VAPID_PRIVATE_KEY=rHCkM6eOFO_c_doOJ6lJLxUrp8grAZRcFLVao3hLJL4
REDIS_URL=redis://localhost:6379

LOG_LEVEL=debug
DB_ENCRYPTION_KEY=f45603d66aabaad637dff04d1910a9b3d31bb0014684e16f5e79c50c8c1a34ba# Cloudflare SSL Configuration - Use Flexible SSL for tunneling compatibility
USE_CLOUDFLARE_FLEXIBLE_SSL=true
