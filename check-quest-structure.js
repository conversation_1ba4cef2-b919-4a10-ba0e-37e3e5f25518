const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkQuests() {
  try {
    const quest = await prisma.quest.findFirst();
    console.log('Sample quest structure:');
    console.log(JSON.stringify(quest, null, 2));
    
    const questWithProgress = await prisma.quest.findFirst({
      include: {
        progress: true
      }
    });
    console.log('\nQuest with progress:');
    console.log(JSON.stringify(questWithProgress, null, 2));
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('Error:', error);
    await prisma.$disconnect();
  }
}

checkQuests(); 