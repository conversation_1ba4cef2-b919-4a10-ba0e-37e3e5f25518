{"name": "server-centric-social-dance", "version": "1.0.0", "description": "A server-centric social dance party playlist application", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "client": "cd frontend && npm start", "build": "cd frontend && npm run build", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "test": "echo \"Error: no test specified\" && exit 1", "setup": "node setup-and-run.js", "watch": "node dev-watch.js", "build:client": "cd frontend && npm run build", "build:server": "cd frontend && npm run build:server", "build:ssr": "npm run build:client && npm run build:server", "optimize-images": "node optimize-images.js"}, "private": true, "dependencies": {"@prisma/client": "^5.10.0", "@radix-ui/react-label": "^2.1.4", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "googleapis": "^133.0.0", "helmet": "^7.2.0", "history": "^5.3.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "react": "18.2.0", "react-dom": "18.2.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.22.3", "sharp": "^0.32.6", "socket.io": "^4.8.1", "winston": "^3.11.0", "ytdl-core": "^4.11.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/lodash": "^4.17.16", "@types/multer": "^1.4.12", "@types/node-cron": "^3.0.11", "chalk": "^4.1.2", "chokidar": "^3.6.0", "cross-env": "^7.0.3", "node-cron": "^3.0.3", "nodemon": "^3.0.3", "prisma": "^5.10.0"}, "prisma": {"seed": "node prisma/seed.js"}, "overrides": {"react": "18.2.0", "react-dom": "18.2.0"}}