// dev-run.js
const { spawn } = require('child_process');
const path = require('path');
const chalk = require('chalk');

const FRONTEND_DIR = 'frontend';
const BACKEND_DIR = 'backend';

function runProcess(name, cmd, args, options = {}) {
    const proc = spawn(cmd, args, { stdio: 'inherit', shell: true, ...options });
    proc.on('close', (code) => {
        console.log(chalk.redBright(`[${name}] exited with code ${code}`));
        process.exitCode = code || 1;
        process.exit();
    });
    proc.on('error', (err) => {
        console.log(chalk.redBright(`[${name}] failed to start: ${err}`));
        process.exitCode = 1;
        process.exit();
    });
    return proc;
}

console.log(chalk.bold.inverse('\n 🚀 Starting Party Playlist App in DEV mode 🚀 \n'));

// Start backend with nodemon
console.log(chalk.cyan('[dev] Launching backend (nodemon)...'));
const backendProc = runProcess(
    'backend',
    'npx',
    ['nodemon', path.join(BACKEND_DIR, 'server.js')]
);

// Start frontend with npm start (or npm run dev)
console.log(chalk.cyan('[dev] Launching frontend (npm start)...'));
const frontendProc = runProcess(
    'frontend',
    'npm',
    ['start'],
    { cwd: FRONTEND_DIR }
);

// Handle Ctrl+C to kill both
process.on('SIGINT', () => {
    console.log(chalk.yellow('\n[dev] Caught SIGINT, shutting down...'));
    backendProc.kill('SIGINT');
    frontendProc.kill('SIGINT');
    process.exit(0);
});